# Scan7

A trading scanner application for monitoring and analyzing market data, designed to identify potential trading opportunities based on Opening Range Breakout (ORB) patterns.

## Overview

Scan7 is a sophisticated market scanner that monitors stock symbols during the critical first 7 minutes after market open to identify potential breakout candidates. The application connects to Interactive Brokers (IBKR) API to retrieve real-time market data, analyze price patterns, and monitor selected symbols for trading opportunities.

## Features

- **Opening Range Detection**: Tracks the price range during the first 7 minutes after market open
- **Multi-Phase Operation**: Pre-market preparation, Opening Range Building (ORB), Finalizing, and Monitoring phases
- **Real-time Market Data**: Connects to IBKR API for live price and volume data
- **Historical Data Analysis**: Can use historical data for opening ranges when running in after-open mode
- **Trend Alignment**: Validates candidates against longer-term trend data
- **Priority Scoring**: Ranks candidates based on multiple factors including volume, price action, and trend
- **Comprehensive Logging**: Detailed logs for all phases and operations
- **Report Generation**: Creates markdown reports summarizing scanner results
- **Email Notifications**: Optional email delivery of reports
- **News Monitoring**: Tracks and logs news events for monitored symbols
- **Rate Limiting**: Manages API request rates to comply with IBKR limitations

## Project Structure

### Main Directories

- `orbscanner/`: Core scanner functionality modules
  - `analysis/`: Market data analysis components
  - `core/`: Core business logic and trading models
  - `data/`: Data retrieval and processing modules
  - `reporting/`: Report generation and visualization
  - `scanners/`: Scanner implementations for different market conditions
  - `tests/`: Test suite for all components
  - `utils/`: Utility functions and helpers
    - `data/`: Data handling utilities
    - `logging/`: Logging configuration and utilities
    - `news/`: News monitoring and processing utilities
    - `phase/`: Phase management for the scanner lifecycle
    - `reporting/`: Report generation utilities
    - `time/`: Time-related utilities for market hours
- `bin/`: Executable scripts and entry points
- `docs/`: Documentation files
- `examples/`: Example code and usage patterns
  - `APItest/`: API testing examples
- `logs/`: Log files (gitignored)
  - `audit/`: Audit logs for code analysis
  - `live/`: Runtime logs from scanner execution
    - `old/`: Archived older logs
- `TARBALLS/`: Archive files (gitignored)

### Key Files

- `scanner_main.py`: Main entry point for the scanner application
- `monitoring_loop.py`: Implements continuous monitoring of symbols
- `app_logic.py`: Core application logic
- `data_handling.py`: Data retrieval and processing
- `scanner_processing.py`: Scanner result processing
- `orbscanner/scanners/scanner_factory.py`: Factory for creating scanner instances
- `orbscanner/scanners/result_processor.py`: Processes scanner results and calculates priority scores
- `orbscanner/utils/phase/manager.py`: Manages scanner execution phases
- `orbscanner/utils/logging/logging_utils.py`: Centralized logging configuration

## Execution Phases

1. **Pre-Market Phase**: Prepares for market open, pre-caches data for potential candidates
2. **ORB Phase**: Tracks the opening range during the first 7 minutes after market open
3. **Finalizing Phase**: Processes opening ranges and selects final candidates
4. **Monitoring Phase**: Continuously monitors selected candidates for breakouts

## Log Structure

The Scan7 application generates a comprehensive set of log files to help with monitoring, debugging, and troubleshooting. Logs are organized by type and purpose, with consistent naming conventions.

### Log Directory Structure

- `logs/audit/`: Contains code audit logs (not runtime logs)
- `logs/live/`: Contains all runtime logs from scanner execution
  - Main scanner logs
  - Phase-specific logs
  - Diagnostic logs
  - Data request trace logs
  - News logs

### Log File Naming Conventions

Log files follow these naming patterns:

- `scan_orb_YYYYMMDD_HHMMSS_*.log`: Main scanner logs with timestamp
- `scan_orb_YYYYMMDD_HHMMSS_*_phase.log`: Phase-specific logs
- `data_request_trace_YYYYMMDD_HHMMSS.log`: Data request tracing logs
- `scanner_wrapper.log`: Method call tracing for debugging
- `news_YYYYMMDD_HHMMSS.log`: News monitoring logs

### Key Log Files for Troubleshooting

1. **Main Scanner Log**: `scan_orb_YYYYMMDD_HHMMSS.log`
   - Contains high-level scanner operations and status messages
   - First place to look for general issues

2. **Phase-Specific Logs**:
   - `*_pre_market.log`: Pre-market phase activities
   - `*_orb.log`: Opening Range Building phase activities
   - `*_finalizing.log`: Finalizing phase activities
   - `*_monitoring.log`: Monitoring phase activities
   - Use these to debug issues in specific scanner phases

3. **Diagnostic Logs**:
   - `*_range_debug.log`: Detailed logs for range calculations
   - `*_volume_debug.log`: Volume data processing logs
   - `*_priority_debug.log`: Priority score calculation details
   - `*_news.log`: News events for monitored symbols
   - Use these for in-depth debugging of specific components

4. **Data Request Trace Logs**: `data_request_trace_YYYYMMDD_HHMMSS.log`
   - Detailed tracking of all data requests
   - Includes request type, symbol, timing, and response status
   - Essential for diagnosing data retrieval issues

5. **Selection Logs**: `*_selection.log`
   - Records symbol selection decisions
   - Useful for understanding why certain symbols were chosen or rejected

6. **Screen Logs**: `*_screen.log`
   - Console output captured to file
   - Useful for seeing what would have been displayed during execution

7. **Report Files**: `*_report.md`
   - Markdown-formatted summary reports
   - Provides overview of scanner results

### Troubleshooting with Logs

When troubleshooting issues:

1. Start with the main scanner log to identify the general problem area
2. Check the relevant phase-specific log for the time period when the issue occurred
3. For data-related issues, examine the data request trace logs
4. For calculation problems, review the appropriate diagnostic log (range, volume, or priority)
5. Look for ERROR or WARNING level messages which highlight potential problems
6. Check timestamps to correlate events across different log files

Each log entry includes a timestamp, log level, and contextual information to help with debugging.
