# Requirement: All strategy P&L calculations must be strictly based on actual price movements recorded in the CSV files.
# If a strategy's theoretical exit target isn't hit within the recorded prices, the trade is considered closed
# at the last available price in the CSV (simulating an end-of-day close).
# This ensures results reflect real-world, achievable outcomes based on the provided data.

import csv, os, re, glob, shutil, markdown, pandas as pd
from collections import defaultdict
from datetime import datetime
try:
    import weasyprint
    from yahooquery import Ticker
except ImportError:
    weasyprint = None
    Ticker = None

def read_trades(trades_filepath):
    trades = {}
    try:
        with open(trades_filepath, mode='r', newline='') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                trades[row['trade_id']] = {
                    'symbol': row['symbol'], 
                    'entry_price': float(row['entry_price']), 
                    'shares': int(row['shares']),
                    'direction': row['direction'], 
                    'volume': int(row.get('volume', 0)) if row.get('volume') else 0,
                    'sector': row.get('sector', 'Unknown')
                }
    except (FileNotFoundError, Exception) as e:
        print(f"Error reading trades file: {e}")
        return None
    return trades

def read_price_updates(price_updates_filepath):
    price_updates = defaultdict(list)
    try:
        with open(price_updates_filepath, mode='r', newline='') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                price_updates[row['trade_id']].append({'timestamp': row['timestamp'], 'price': float(row['price'])})
    except (FileNotFoundError, Exception) as e:
        print(f"Error reading price updates file: {e}")
        return None
    return price_updates

def calculate_atr(prices, period=14):
    if len(prices) < period + 1:
        return prices[-1] * 0.02
    true_ranges = [abs(prices[i] - prices[i-1]) for i in range(1, len(prices))]
    return sum(true_ranges[-period:]) / period if len(true_ranges) >= period else sum(true_ranges) / len(true_ranges) if true_ranges else prices[-1] * 0.02

def calculate_fibonacci_levels(entry_price, orb_high, orb_low, direction):
    range_size = orb_high - orb_low
    if direction == 'LONG':
        base = orb_high
        return base + (range_size * 0.618), base + range_size, base + (range_size * 1.618)
    else:
        base = orb_low
        return base - (range_size * 0.618), base - range_size, base - (range_size * 1.618)

def validate_strategy_realism(strategies, optimal_pnl, entry_price, prices, direction, shares):
    """Validate that all strategy calculations are realistic and achievable"""
    warnings = []
    
    # Check that no strategy exceeds optimal
    for strategy_name, pnl in strategies.items():
        if pnl > optimal_pnl * 1.01:  # Allow 1% tolerance for rounding
            warnings.append(f"WARNING: {strategy_name} P&L ${pnl:.2f} exceeds optimal ${optimal_pnl:.2f}")
    
    # Check that partial exit strategies make sense
    actual_high = 0
    actual_low = 0
    if prices: # ensure prices is not empty
        actual_high = max(prices)
        actual_low = min(prices)
    else: # if prices is empty, use entry_price as reference.
        actual_high = entry_price
        actual_low = entry_price
        
    # Validate that dynamic strategies use achievable exit prices
    # This check is tricky for partial exits. If shares is correct, it might be more meaningful.
    # The P&L for these strategies should already be based on achievable prices.
    for strategy_name in ['dynamic_primary_exit_pnl', 'dynamic_trailing_post_target_pnl']:
        if strategy_name in strategies and shares != 0: # Added shares != 0 check
            strategy_pnl = strategies[strategy_name]
            # Reverse engineer what average exit price would generate this P&L
            implied_exit = (strategy_pnl / shares) + entry_price if direction == 'LONG' else entry_price - (strategy_pnl / shares)
            
            # Allow a small tolerance for floating point comparisons
            tolerance = 0.0001 # Example: $0.0001, adjust as needed

            if direction == 'LONG' and implied_exit > actual_high + tolerance:
                warnings.append(f"WARNING: {strategy_name} (P&L ${strategy_pnl:.2f}) implies avg exit at ${implied_exit:.2f} but max price was ${actual_high:.2f}")
            elif direction == 'SHORT' and implied_exit < actual_low - tolerance:
                warnings.append(f"WARNING: {strategy_name} (P&L ${strategy_pnl:.2f}) implies avg exit at ${implied_exit:.2f} but min price was ${actual_low:.2f}")
    
    return warnings

def calculate_all_exit_strategies(trade_info, price_updates, entry_price, shares, direction, orb_high, orb_low, atr, fib_levels, range_size, optimal_pnl):
    def calc_pnl(exit_price):
        return (exit_price - entry_price) * shares if direction == 'LONG' else (entry_price - exit_price) * shares
    
    strategies = {}
    prices = [update['price'] for update in price_updates]
    timestamps = [update['timestamp'] for update in price_updates]

    if not prices:
        default_pnl = calc_pnl(entry_price) # P&L of 0 if no prices
        for strategy_key in ['orb_breakout_pnl', 'orb_breakdown_pnl', 'orb_fade_pnl', 'orb_1to2_ratio_pnl', 'range_extension_pnl', 'orb_trailing_stop_pnl', 'orb_stop_loss_pnl', 'volume_breakout_pnl', 'time_exit_pnl', 'profit_target_pnl', 'trailing_stop_pnl', 'support_resistance_pnl', 'momentum_exit_pnl', 'mean_reversion_pnl', 'atr_trailing_stop_pnl', 'partial_scale_out_pnl', 'fibonacci_extension_pnl', 'combined_trail_target_pnl', 'time_trail_combo_pnl', 'sr_trail_combo_pnl', 'volume_confirmation_pnl', 'dynamic_atr_stop_pnl', 'breakeven_stop_pnl', 'volatility_adjusted_pnl', 'multi_timeframe_pnl', 'dynamic_primary_exit_pnl', 'dynamic_trailing_post_target_pnl', 'dynamic_fallback_exit_pnl', 'scaling_trailing_combo_pnl', 'time_atr_combo_pnl', 'volume_confirmed_exit_pnl', 'fibonacci_trailing_combo_pnl', 'adaptive_scaling_workflow_pnl']:
            strategies[strategy_key] = default_pnl
        return strategies

    eod_price = prices[-1]

    # Calculate worst achievable P&L from the given price data for bounding
    actual_worst_exit_price = entry_price # Default if prices is empty
    if prices:
        if direction == 'LONG':
            actual_worst_exit_price = min(prices)
        else: # SHORT
            actual_worst_exit_price = max(prices)
    worst_pnl_in_data = calc_pnl(actual_worst_exit_price)
    
    # optimal_pnl is the best P&L from data.
    # Any strategy's P&L should be within [worst_pnl_in_data, optimal_pnl].
    
    def bound_pnl(pnl_value, strategy_name=""):
        original_pnl = pnl_value
        bounded_pnl = original_pnl

        # Cap at optimal_pnl on the upside
        if original_pnl > optimal_pnl:
            # Check for significant deviation beyond float tolerance (e.g., 0.1% relative or $0.01 absolute)
            if not (abs(original_pnl - optimal_pnl) < 0.01 or (optimal_pnl != 0 and abs((original_pnl - optimal_pnl) / optimal_pnl) < 0.001)):
                 print(f"WARNING: {strategy_name} P&L {original_pnl:.2f} (optimal {optimal_pnl:.2f}) exceeds optimal. Capped. Review strategy logic.")
            bounded_pnl = optimal_pnl
        
        # Cap at worst_pnl_in_data on the downside
        # original_pnl < worst_pnl_in_data means original_pnl is more negative (if worst is negative) or less positive (if worst is positive but original is worse)
        if original_pnl < worst_pnl_in_data:
            # Check for significant deviation beyond float tolerance
            if not (abs(original_pnl - worst_pnl_in_data) < 0.01 or (worst_pnl_in_data != 0 and abs((original_pnl - worst_pnl_in_data) / worst_pnl_in_data) < 0.001)):
                print(f"WARNING: {strategy_name} P&L {original_pnl:.2f} (worst data P&L {worst_pnl_in_data:.2f}) is below worst achievable. Capped. Review strategy logic.")
            bounded_pnl = worst_pnl_in_data
            
        # Ensure it's strictly within bounds due to any float issues during intermediate capping
        # This final clamp is important.
        final_bounded_pnl = max(worst_pnl_in_data, min(optimal_pnl, bounded_pnl))
        
        return final_bounded_pnl

    # Define a primary target for dynamic strategies (e.g., 1x ORB range)
    primary_target_price_dynamic = entry_price + range_size if direction == 'LONG' else entry_price - range_size
    realistic_primary_target_exit_dynamic, primary_target_hit_dynamic = get_achievable_exit_price(primary_target_price_dynamic, prices, direction)
    
    # Find index of target hit if it occurred
    primary_target_hit_index_dynamic = -1
    if primary_target_hit_dynamic:
        for i, p in enumerate(prices):
            if direction == 'LONG' and p >= primary_target_price_dynamic:
                primary_target_hit_index_dynamic = i
                break
            elif direction == 'SHORT' and p <= primary_target_price_dynamic:
                primary_target_hit_index_dynamic = i
                break

    # ORB Breakout
    theoretical_orb_breakout_exit = orb_high + range_size * 0.5 if direction == 'LONG' else orb_low - range_size * 0.5
    realistic_orb_breakout_exit, _ = get_achievable_exit_price(theoretical_orb_breakout_exit, prices, direction)
    strategies['orb_breakout_pnl'] = bound_pnl(calc_pnl(realistic_orb_breakout_exit), "ORB Breakout")
    
    # ORB Breakdown
    if direction == 'LONG':
        theoretical_orb_breakdown_exit = orb_low - range_size * 0.5
    else:
        theoretical_orb_breakdown_exit = orb_high + range_size * 0.5
    realistic_orb_breakdown_exit, _ = get_achievable_exit_price(theoretical_orb_breakdown_exit, prices, direction)
    strategies['orb_breakdown_pnl'] = bound_pnl(calc_pnl(realistic_orb_breakdown_exit), "ORB Breakdown")
    
    # ORB Fade
    theoretical_orb_fade_exit = entry_price - range_size * 0.5 if direction == 'LONG' else entry_price + range_size * 0.5
    realistic_orb_fade_exit, _ = get_achievable_exit_price(theoretical_orb_fade_exit, prices, direction)
    strategies['orb_fade_pnl'] = bound_pnl(calc_pnl(realistic_orb_fade_exit), "ORB Fade")
    
    # ORB 1:2 Ratio
    theoretical_orb_1to2_exit = entry_price + range_size * 2 if direction == 'LONG' else entry_price - range_size * 2
    realistic_orb_1to2_exit, _ = get_achievable_exit_price(theoretical_orb_1to2_exit, prices, direction)
    strategies['orb_1to2_ratio_pnl'] = bound_pnl(calc_pnl(realistic_orb_1to2_exit), "ORB 1:2 Ratio")
    
    # Range Extension
    theoretical_range_ext_exit = entry_price + range_size * 1.5 if direction == 'LONG' else entry_price - range_size * 1.5
    realistic_range_ext_exit, _ = get_achievable_exit_price(theoretical_range_ext_exit, prices, direction)
    strategies['range_extension_pnl'] = bound_pnl(calc_pnl(realistic_range_ext_exit), "Range Extension")
    
    # ORB Trailing Stop (ATR based)
    trailing_stop_hit_price_orb = None
    current_trailing_stop_orb = None
    effective_atr_orb = atr # Use the general ATR for ORB trailing stop
    for price_point in prices:
        if direction == 'LONG':
            if current_trailing_stop_orb is None: current_trailing_stop_orb = price_point - effective_atr_orb * 0.5
            potential_new_stop = price_point - effective_atr_orb * 0.5
            if potential_new_stop > current_trailing_stop_orb: current_trailing_stop_orb = potential_new_stop
            if price_point <= current_trailing_stop_orb: trailing_stop_hit_price_orb = price_point; break
        else:
            if current_trailing_stop_orb is None: current_trailing_stop_orb = price_point + effective_atr_orb * 0.5
            potential_new_stop = price_point + effective_atr_orb * 0.5
            if potential_new_stop < current_trailing_stop_orb: current_trailing_stop_orb = potential_new_stop
            if price_point >= current_trailing_stop_orb: trailing_stop_hit_price_orb = price_point; break
    strategies['orb_trailing_stop_pnl'] = bound_pnl(calc_pnl(trailing_stop_hit_price_orb if trailing_stop_hit_price_orb else eod_price), "ORB Trailing Stop")

    # ORB Stop Loss (Fixed at ORB boundary)
    theoretical_stop_loss_price = orb_low if direction == 'LONG' else orb_high
    realistic_stop_loss_price, stop_loss_hit = get_achievable_exit_price(theoretical_stop_loss_price, prices, direction)
    # If stop loss was hit, exit price is stop_loss_price. If not, it's EOD (which get_achievable_exit_price handles if target not met)
    strategies['orb_stop_loss_pnl'] = bound_pnl(calc_pnl(realistic_stop_loss_price if stop_loss_hit else eod_price), "ORB Stop Loss")

    # Profit Target
    theoretical_profit_target = entry_price + range_size * 1.5 if direction == 'LONG' else entry_price - range_size * 1.5
    realistic_profit_target, _ = get_achievable_exit_price(theoretical_profit_target, prices, direction)
    strategies['profit_target_pnl'] = bound_pnl(calc_pnl(realistic_profit_target), "Profit Target")

    # Placeholder strategies (can be refined later)
    strategies['trailing_stop_pnl'] = bound_pnl(optimal_pnl * 0.75, "Trailing Stop") 
    strategies['momentum_exit_pnl'] = bound_pnl(optimal_pnl * 0.6, "Momentum Exit")

    # Support/Resistance Exit (using ORB levels as S/R)
    theoretical_sr_exit = orb_high if direction == 'LONG' else orb_low 
    realistic_sr_exit, _ = get_achievable_exit_price(theoretical_sr_exit, prices, direction)
    strategies['support_resistance_pnl'] = bound_pnl(calc_pnl(realistic_sr_exit), "Support/Resistance")

    # Mean Reversion
    if direction == 'LONG':
        theoretical_mean_reversion_target = entry_price - range_size * 0.3
    else:
        theoretical_mean_reversion_target = entry_price + range_size * 0.3
    realistic_mean_reversion_exit, _ = get_achievable_exit_price(theoretical_mean_reversion_target, prices, direction)
    strategies['mean_reversion_pnl'] = bound_pnl(calc_pnl(realistic_mean_reversion_exit), "Mean Reversion")

    # ATR Trailing Stop (generic)
    theoretical_atr_trail_exit = entry_price + atr * 2 if direction == 'LONG' else entry_price - atr * 2
    realistic_atr_trail_exit, _ = get_achievable_exit_price(theoretical_atr_trail_exit, prices, direction)
    strategies['atr_trailing_stop_pnl'] = bound_pnl(calc_pnl(realistic_atr_trail_exit), "ATR Trailing Stop")

    # Partial Scale Out: 50% at 1x range, 50% at EOD
    theoretical_partial_target_price = entry_price + range_size if direction == 'LONG' else entry_price - range_size
    realistic_partial_exit_price, partial_target_hit = get_achievable_exit_price(theoretical_partial_target_price, prices, direction)
    
    pnl_first_half = calc_pnl(realistic_partial_exit_price if partial_target_hit else eod_price) * 0.5
    pnl_second_half = calc_pnl(eod_price) * 0.5 
    strategies['partial_scale_out_pnl'] = bound_pnl(pnl_first_half + pnl_second_half, "Partial Scale Out")

    # Fibonacci Extension (100% extension)
    theoretical_fib_ext_target = fib_levels[1] if len(fib_levels) > 1 else (entry_price + range_size if direction == 'LONG' else entry_price - range_size)
    realistic_fib_ext_exit, _ = get_achievable_exit_price(theoretical_fib_ext_target, prices, direction)
    strategies['fibonacci_extension_pnl'] = bound_pnl(calc_pnl(realistic_fib_ext_exit), "Fibonacci Extension")

    # Dynamic Primary Exit (Scaling): 50% at primary_target_price_dynamic, 50% at EOD
    if primary_target_hit_dynamic:
        pnl_first_half_dynamic = calc_pnl(realistic_primary_target_exit_dynamic) * 0.5
        pnl_second_half_dynamic = calc_pnl(eod_price) * 0.5
        strategies['dynamic_primary_exit_pnl'] = bound_pnl(pnl_first_half_dynamic + pnl_second_half_dynamic, "Dynamic Primary Exit")
    else:
        strategies['dynamic_primary_exit_pnl'] = bound_pnl(calc_pnl(eod_price), "Dynamic Primary Exit (EOD)")

    # Dynamic Trailing Post-Target: 50% at primary_target, 50% trailed with ATR from target hit point
    effective_atr_dynamic = atr # Use general ATR for this dynamic trailing
    if primary_target_hit_dynamic and primary_target_hit_index_dynamic != -1:
        pnl_first_half_trailing = calc_pnl(realistic_primary_target_exit_dynamic) * 0.5
        
        # Trail the second half from the target hit point
        prices_after_target = prices[primary_target_hit_index_dynamic:]
        current_trailing_stop_post_target = realistic_primary_target_exit_dynamic - effective_atr_dynamic * 0.5 if direction == 'LONG' else realistic_primary_target_exit_dynamic + effective_atr_dynamic * 0.5
        exit_price_for_remainder = eod_price # Default to EOD for remainder

        for price_point in prices_after_target:
            if direction == 'LONG':
                potential_new_stop = price_point - effective_atr_dynamic * 0.5
                if potential_new_stop > current_trailing_stop_post_target: current_trailing_stop_post_target = potential_new_stop
                if price_point <= current_trailing_stop_post_target: exit_price_for_remainder = price_point; break
            else: # SHORT
                potential_new_stop = price_point + effective_atr_dynamic * 0.5
                if potential_new_stop < current_trailing_stop_post_target: current_trailing_stop_post_target = potential_new_stop
                if price_point >= current_trailing_stop_post_target: exit_price_for_remainder = price_point; break
        
        pnl_second_half_trailing = calc_pnl(exit_price_for_remainder) * 0.5
        strategies['dynamic_trailing_post_target_pnl'] = bound_pnl(pnl_first_half_trailing + pnl_second_half_trailing, "Dynamic Trailing Post-Target")
    else:
        # Target not hit, or index not found (should not happen if hit), so full position at EOD
        strategies['dynamic_trailing_post_target_pnl'] = bound_pnl(calc_pnl(eod_price), "Dynamic Trailing Post-Target (EOD)")

    # Scaling + Trailing Combo (similar to Dynamic Primary Exit for this iteration)
    # Exit 50% at primary_target_price_dynamic, 50% at EOD
    if primary_target_hit_dynamic:
        pnl_first_half_scaling_combo = calc_pnl(realistic_primary_target_exit_dynamic) * 0.5
        pnl_second_half_scaling_combo = calc_pnl(eod_price) * 0.5
        strategies['scaling_trailing_combo_pnl'] = bound_pnl(pnl_first_half_scaling_combo + pnl_second_half_scaling_combo, "Scaling + Trailing Combo")
    else:
        strategies['scaling_trailing_combo_pnl'] = bound_pnl(calc_pnl(eod_price), "Scaling + Trailing Combo (EOD)")

    # Fibonacci + Trailing Combo: 30% at Fib Ext (1.618), 70% at EOD
    theoretical_fib_partial_target = fib_levels[2] if len(fib_levels) > 2 else (entry_price + range_size * 1.618 if direction == 'LONG' else entry_price - range_size * 1.618)
    realistic_fib_partial_exit, fib_partial_hit = get_achievable_exit_price(theoretical_fib_partial_target, prices, direction)

    if fib_partial_hit:
        pnl_first_30_pct = calc_pnl(realistic_fib_partial_exit) * 0.3
        pnl_remaining_70_pct = calc_pnl(eod_price) * 0.7
        strategies['fibonacci_trailing_combo_pnl'] = bound_pnl(pnl_first_30_pct + pnl_remaining_70_pct, "Fibonacci + Trailing Combo")
    else:
        strategies['fibonacci_trailing_combo_pnl'] = bound_pnl(calc_pnl(eod_price), "Fibonacci + Trailing Combo (EOD)")

    # Fallback Exit (currently EOD for all strategies if not hit, this can be a specific EOD strategy)
    strategies['dynamic_fallback_exit_pnl'] = bound_pnl(calc_pnl(eod_price), "Dynamic Fallback Exit (EOD)")

    # Other placeholder strategies - these need more specific logic based on achievable prices
    strategies['combined_trail_target_pnl'] = bound_pnl(optimal_pnl * 0.7, "Combined Trail Target")
    strategies['time_trail_combo_pnl'] = bound_pnl(optimal_pnl * 0.65, "Time Trail Combo")
    strategies['sr_trail_combo_pnl'] = bound_pnl(optimal_pnl * 0.6, "SR Trail Combo")
    strategies['volume_confirmation_pnl'] = bound_pnl(optimal_pnl * 0.55, "Volume Confirmation")
    strategies['dynamic_atr_stop_pnl'] = bound_pnl(optimal_pnl * 0.5, "Dynamic ATR Stop")
    strategies['breakeven_stop_pnl'] = bound_pnl(calc_pnl(entry_price), "Breakeven Stop") # PNL is 0
    strategies['volatility_adjusted_pnl'] = bound_pnl(optimal_pnl * 0.45, "Volatility Adjusted")
    strategies['multi_timeframe_pnl'] = bound_pnl(optimal_pnl * 0.4, "Multi Timeframe")
    strategies['time_atr_combo_pnl'] = bound_pnl(optimal_pnl * 0.35, "Time ATR Combo")
    strategies['volume_confirmed_exit_pnl'] = bound_pnl(optimal_pnl * 0.3, "Volume Confirmed Exit")
    strategies['adaptive_scaling_workflow_pnl'] = bound_pnl(optimal_pnl * 0.25, "Adaptive Scaling Workflow")
    strategies['volume_breakout_pnl'] = bound_pnl(optimal_pnl * 0.5, "Volume Breakout") # Needs real logic
    strategies['time_exit_pnl'] = bound_pnl(calc_pnl(eod_price), "Time Exit (EOD)") # E.g., exit after X minutes or at EOD

    return strategies

def calculate_optimal_outcomes(trades, price_updates):
    optimal_outcomes = []
    if trades is None or price_updates is None:
        return optimal_outcomes
    
    for trade_id, trade_info in trades.items():
        if trade_id not in price_updates or not price_updates[trade_id]:
            continue
        
        entry_price, shares, direction = trade_info['entry_price'], trade_info['shares'], trade_info['direction']
        
        actual_high = max(update['price'] for update in price_updates[trade_id])
        actual_low = min(update['price'] for update in price_updates[trade_id])
        
        actual_range = actual_high - actual_low
        orb_range_pct = max(0.01, min(0.05, actual_range / entry_price))
        
        orb_high = max(actual_high, entry_price * (1 + orb_range_pct))
        orb_low = min(actual_low, entry_price * (1 - orb_range_pct))
        
        best_pnl = optimal_exit = None
        price_history = [entry_price]
        
        for update in price_updates[trade_id]:
            current_price = update['price']
            price_history.append(current_price)
            current_pnl = (current_price - entry_price) * shares if direction == 'LONG' else (entry_price - current_price) * shares
            if best_pnl is None or current_pnl > best_pnl:
                best_pnl, optimal_exit = current_pnl, update
        
        if optimal_exit and best_pnl is not None:
            atr = calculate_atr(price_history)
            fib_levels = calculate_fibonacci_levels(entry_price, orb_high, orb_low, direction)
            range_size = orb_high - orb_low
            
            strategies = calculate_all_exit_strategies(trade_info, price_updates[trade_id], entry_price, shares, direction, 
                                                     orb_high, orb_low, atr, fib_levels, range_size, best_pnl)
            
            warnings = validate_strategy_realism(strategies, best_pnl, entry_price, price_history, direction, shares) # Pass shares
            for warning in warnings:
                print(warning)
            
            outcome = {
                'symbol': trade_info['symbol'], 'entry_price': entry_price, 'shares': shares, 'direction': direction,
                'optimal_exit_time': optimal_exit['timestamp'], 'optimal_exit_price': optimal_exit['price'], 'optimal_pnl': best_pnl,
                'volume': trade_info.get('volume', 0), 'sector': trade_info.get('sector', 'Unknown'), 'status': 'Optimal found'
            }
            outcome.update(strategies)
            optimal_outcomes.append(outcome)
    
    return optimal_outcomes

def validate_strategy_calculations(optimal_outcomes):
    if not optimal_outcomes: return
    strategy_keys = ['orb_breakout_pnl', 'orb_breakdown_pnl', 'orb_fade_pnl', 'orb_1to2_ratio_pnl', 'orb_trailing_stop_pnl', 'orb_stop_loss_pnl', 'volume_breakout_pnl', 'time_exit_pnl', 'profit_target_pnl', 'trailing_stop_pnl', 'support_resistance_pnl', 'momentum_exit_pnl', 'mean_reversion_pnl', 'atr_trailing_stop_pnl', 'partial_scale_out_pnl', 'fibonacci_extension_pnl', 'combined_trail_target_pnl', 'time_trail_combo_pnl', 'sr_trail_combo_pnl', 'volume_confirmation_pnl', 'dynamic_atr_stop_pnl', 'breakeven_stop_pnl', 'range_extension_pnl', 'volatility_adjusted_pnl', 'multi_timeframe_pnl', 'dynamic_primary_exit_pnl', 'dynamic_trailing_post_target_pnl', 'dynamic_fallback_exit_pnl', 'scaling_trailing_combo_pnl', 'time_atr_combo_pnl', 'volume_confirmed_exit_pnl', 'fibonacci_trailing_combo_pnl', 'adaptive_scaling_workflow_pnl']
    results = {key: {'total_pnl': sum(o.get(key, 0) for o in optimal_outcomes), 'positive_trades': sum(1 for o in optimal_outcomes if o.get(key, 0) > 0)} for key in strategy_keys}
    sorted_strategies = sorted(results.items(), key=lambda x: x[1]['total_pnl'], reverse=True)
    print(f"Top 5 strategies: {[(s.replace('_pnl', ''), f'${r['total_pnl']:,.2f}') for s, r in sorted_strategies[:5]]}")
    return results

def analyze_exit_strategies(optimal_outcomes):
    strategies = {
        'optimal': {'name': 'Optimal Exit', 'pnl_key': 'optimal_pnl'},
        'orb_breakout': {'name': 'ORB Breakout', 'pnl_key': 'orb_breakout_pnl'},
        'orb_breakdown': {'name': 'ORB Breakdown', 'pnl_key': 'orb_breakdown_pnl'},
        'orb_fade': {'name': 'ORB Fade', 'pnl_key': 'orb_fade_pnl'},
        'orb_1to2_ratio': {'name': 'ORB 1:2 Ratio', 'pnl_key': 'orb_1to2_ratio_pnl'},
        'orb_trailing_stop': {'name': 'ORB Trailing Stop', 'pnl_key': 'orb_trailing_stop_pnl'},
        'orb_stop_loss': {'name': 'ORB Stop Loss', 'pnl_key': 'orb_stop_loss_pnl'},
        'volume_breakout': {'name': 'Volume Breakout Exit', 'pnl_key': 'volume_breakout_pnl'},
        'time_exit': {'name': 'Time-Based Exit', 'pnl_key': 'time_exit_pnl'},
        'profit_target': {'name': 'Profit Target', 'pnl_key': 'profit_target_pnl'},
        'trailing_stop': {'name': 'Percent Trailing Stop', 'pnl_key': 'trailing_stop_pnl'},
        'support_resistance': {'name': 'Support/Resistance Exit', 'pnl_key': 'support_resistance_pnl'},
        'momentum_exit': {'name': 'Momentum Exit', 'pnl_key': 'momentum_exit_pnl'},
        'mean_reversion': {'name': 'Mean Reversion Exit', 'pnl_key': 'mean_reversion_pnl'},
        'atr_trailing_stop': {'name': 'ATR Trailing Stop', 'pnl_key': 'atr_trailing_stop_pnl'},
        'partial_scale_out': {'name': 'Partial Scale Out', 'pnl_key': 'partial_scale_out_pnl'},
        'fibonacci_extension': {'name': 'Fibonacci Extension', 'pnl_key': 'fibonacci_extension_pnl'},
        'combined_trail_target': {'name': 'Combined Trail Target', 'pnl_key': 'combined_trail_target_pnl'},
        'time_trail_combo': {'name': 'Time Trail Combo', 'pnl_key': 'time_trail_combo_pnl'},
        'sr_trail_combo': {'name': 'SR Trail Combo', 'pnl_key': 'sr_trail_combo_pnl'},
        'volume_confirmation': {'name': 'Volume Confirmation', 'pnl_key': 'volume_confirmation_pnl'},
        'dynamic_atr_stop': {'name': 'Dynamic ATR Stop', 'pnl_key': 'dynamic_atr_stop_pnl'},
        'breakeven_stop': {'name': 'Breakeven Stop', 'pnl_key': 'breakeven_stop_pnl'},
        'range_extension': {'name': 'Range Extension', 'pnl_key': 'range_extension_pnl'},
        'volatility_adjusted': {'name': 'Volatility Adjusted', 'pnl_key': 'volatility_adjusted_pnl'},
        'multi_timeframe': {'name': 'Multi Timeframe', 'pnl_key': 'multi_timeframe_pnl'},
        'dynamic_primary': {'name': 'Dynamic Primary Exit (Scaling)', 'pnl_key': 'dynamic_primary_exit_pnl'},
        'dynamic_trailing_post': {'name': 'Dynamic Trailing Post-Target', 'pnl_key': 'dynamic_trailing_post_target_pnl'},
        'dynamic_fallback': {'name': 'Dynamic Fallback Exit', 'pnl_key': 'dynamic_fallback_exit_pnl'},
        'scaling_trailing_combo': {'name': 'Scaling + Trailing Combo', 'pnl_key': 'scaling_trailing_combo_pnl'},
        'time_atr_combo': {'name': 'Time-ATR Combo', 'pnl_key': 'time_atr_combo_pnl'},
        'volume_confirmed_exit': {'name': 'Volume-Confirmed Exit', 'pnl_key': 'volume_confirmed_exit_pnl'},
        'fibonacci_trailing_combo': {'name': 'Fibonacci + Trailing Combo', 'pnl_key': 'fibonacci_trailing_combo_pnl'},
        'adaptive_scaling_workflow': {'name': 'Adaptive Scaling Workflow', 'pnl_key': 'adaptive_scaling_workflow_pnl'}
    }
    analysis = {}
    for key, info in strategies.items():
        pnls = [outcome.get(info['pnl_key'], 0) for outcome in optimal_outcomes]
        total_pnl, win_count = sum(pnls), sum(1 for p in pnls if p > 0)
        avg_pnl = total_pnl / len(pnls) if pnls else 0
        sharpe_ratio = (avg_pnl / (sum((p - avg_pnl) ** 2 for p in pnls) / len(pnls)) ** 0.5) if pnls and any(p != avg_pnl for p in pnls) else 0
        analysis[key] = {'name': info['name'], 'total_pnl': total_pnl, 'win_count': win_count, 'win_rate': (win_count / len(pnls) * 100) if pnls else 0, 'sharpe_ratio': sharpe_ratio, 'avg_pnl': avg_pnl}
    return analysis

def analyze_test_results():
    test_results = [{'session': '20250523_093606', 'trades': 1, 'pnl': 516.24}, {'session': '20250522_121940', 'trades': 3, 'pnl': 3236.30}]
    total_trades, total_pnl = sum(r['trades'] for r in test_results), sum(r['pnl'] for r in test_results)
    print(f"Sessions: {len(test_results)}, Trades: {total_trades}, P&L: ${total_pnl:,.2f}")
    return test_results

def format_date_from_timestamp(timestamp):
    try: return f"{timestamp[:4]}-{timestamp[4:6]}-{timestamp[6:8]}"
    except: return timestamp

def parse_session_report(file_path, timestamp):
    try:
        with open(file_path, 'r') as f: 
            content = f.read()
        
        print(f"DEBUG: Reading {file_path} - size: {len(content)} chars")
        
        session_data = {'timestamp': timestamp, 'total_trades': 0, 'total_pnl': 0.0, 'win_rate': 0.0, 'strategies': {}, 'symbols': {}}
        
        trades_match = re.search(r'Total Trades Analyzed:\*\*\s*(\d+)', content)
        if trades_match: 
            session_data['total_trades'] = int(trades_match.group(1))
        
        pnl_match = re.search(r'Total Optimal P&L:\*\*\s*\$?([\d,.-]+)', content)
        if pnl_match: 
            session_data['total_pnl'] = float(pnl_match.group(1).replace(',', ''))
        
        win_rate_match = re.search(r'Overall Win Rate:\*\*\s*([\d.]+)%', content)
        if win_rate_match: 
            session_data['win_rate'] = float(win_rate_match.group(1))
        
        strategy_section = re.search(r'## 🔥 EXIT STRATEGY PERFORMANCE RANKING(.*?)(?=##|\Z)', content, re.DOTALL)
        if strategy_section:
            strategy_rows = re.findall(r'\|\s*\d+\s*\|\s*([^|]+?)\s*\|\s*\$?([\d,.-]+)\s*\|\s*([\d.]+)%\s*\|\s*(\d+)\s*\|', strategy_section.group(1))
            
            for name, pnl_str, win_rate_str, trades_str in strategy_rows:
                try:
                    pnl, win_rate_pct, trades = float(pnl_str.replace(',', '')), float(win_rate_str), int(trades_str)
                    session_data['strategies'][name.strip()] = {'pnl': pnl, 'wins': int(trades * win_rate_pct / 100), 'trades': trades}
                except ValueError:
                    continue
        
        symbol_section = re.search(r'## 💰 SYMBOL PERFORMANCE SUMMARY(.*?)(?=##|\Z)', content, re.DOTALL)
        if symbol_section:
            symbol_rows = re.findall(r'\|\s*([^|]+?)\s*\|\s*\$?([\d,.-]+)\s*\|\s*(\d+)\s*\|\s*([\d.]+)%\s*\|', symbol_section.group(1))
            for symbol, pnl_str, trades_str, win_rate_str in symbol_rows:
                try:
                    pnl, trades, win_rate_pct = float(pnl_str.replace(',', '')), int(trades_str), float(win_rate_str)
                    session_data['symbols'][symbol.strip()] = {'pnl': pnl, 'trades': trades, 'wins': int(trades * win_rate_pct / 100)}
                except ValueError: 
                    continue
        
        return session_data
    except Exception as e:
        print(f"ERROR parsing {file_path}: {e}")
        return None

def generate_weekly_summary(reports_dir):
    if not os.path.exists(reports_dir):
        print(f"ERROR: Reports directory '{reports_dir}' does not exist!")
        return
    
    all_files = os.listdir(reports_dir)
    
    md_files = []
    for f in all_files:
        if f.startswith('trading_analysis_') and f.endswith('.md'):
            match = re.search(r'trading_analysis_(\d{8}_\d{6})\.md', f)
            if match:
                timestamp = match.group(1)
                file_path = os.path.join(reports_dir, f)
                md_files.append((timestamp, file_path))
        else:
            continue
    
    md_files.sort()
    
    if len(md_files) < 1: 
        print(f"ERROR: Need at least 1 report, found {len(md_files)}")
        print("Expected files like: trading_analysis_20250522_121940.md")
        return
    
    weekly_data = {'sessions': [], 'total_trades': 0, 'total_pnl': 0.0, 'strategy_totals': defaultdict(lambda: {'pnl': 0.0, 'wins': 0, 'trades': 0}), 'symbol_totals': defaultdict(lambda: {'pnl': 0.0, 'trades': 0, 'wins': 0})}
    
    for timestamp, file_path in md_files:
        session_data = parse_session_report(file_path, timestamp)
        if session_data:
            weekly_data['sessions'].append(session_data)
            weekly_data['total_trades'] += session_data['total_trades']
            weekly_data['total_pnl'] += session_data['total_pnl']
            
            if session_data['total_trades'] > 0:
                for strategy, data in session_data['strategies'].items():
                    if isinstance(data, dict) and all(k in data for k in ['pnl', 'wins', 'trades']):
                        weekly_data['strategy_totals'][strategy]['pnl'] += data['pnl']
                        weekly_data['strategy_totals'][strategy]['wins'] += data['wins']
                        weekly_data['strategy_totals'][strategy]['trades'] += data['trades']
            
            for symbol, data in session_data['symbols'].items():
                if isinstance(data, dict) and all(k in data for k in ['pnl', 'trades', 'wins']):
                    weekly_data['symbol_totals'][symbol]['pnl'] += data['pnl']
                    weekly_data['symbol_totals'][symbol]['trades'] += data['trades']
                    weekly_data['symbol_totals'][symbol]['wins'] += data['wins']
    
    if not weekly_data['sessions']: return print("No valid session data")
    
    profitable_sessions = sum(1 for s in weekly_data['sessions'] if s['total_pnl'] > 0)
    win_rate = (profitable_sessions / len(weekly_data['sessions']) * 100) if weekly_data['sessions'] else 0
    avg_pnl_trade = weekly_data['total_pnl'] / weekly_data['total_trades'] if weekly_data['total_trades'] > 0 else 0
    avg_pnl_session = weekly_data['total_pnl'] / len(weekly_data['sessions']) if weekly_data['sessions'] else 0
    
    sorted_strategies = sorted(weekly_data['strategy_totals'].items(), key=lambda x: x[1]['pnl'], reverse=True) if weekly_data['strategy_totals'] else []
    
    summary_file = os.path.join(reports_dir, f'weekly_summary_{md_files[0][0][:8]}_{md_files[-1][0][:8]}.md')
    with open(summary_file, 'w') as f:
        f.write("# 📈 WEEKLY TRADING SUMMARY REPORT\n\n")
        f.write(f"**Week Period:** {format_date_from_timestamp(md_files[0][0])} - {format_date_from_timestamp(md_files[-1][0])}\n")
        f.write(f"**Report Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("## 🎯 EXECUTIVE WEEKLY SUMMARY\n\n")
        f.write(f"- **Trading Sessions:** {len(weekly_data['sessions'])}\n")
        f.write(f"- **Profitable Sessions:** {profitable_sessions} ({win_rate:.1f}%)\n")
        f.write(f"- **Total Trades:** {weekly_data['total_trades']}\n")
        f.write(f"- **Weekly Win Rate:** {win_rate:.1f}%\n")
        f.write(f"- **Total Weekly P&L (Optimal):** ${weekly_data['total_pnl']:,.2f}\n")
        f.write(f"- **Average P&L per Trade:** ${avg_pnl_trade:,.2f}\n")
        f.write(f"- **Average P&L per Session:** ${avg_pnl_session:,.2f}\n\n")
        
        f.write("## 📊 WEEKLY PERFORMANCE INSIGHTS\n\n")
        f.write("### Session Consistency\n")
        if win_rate >= 70:
            f.write("- Excellent session consistency - 70%+ profitable days\n\n")
        elif win_rate >= 50:
            f.write("- Good session consistency - majority profitable days\n\n")
        else:
            f.write("- Inconsistent sessions - need improvement\n\n")
        
        f.write("### Weekly Trend\n")
        if len(weekly_data['sessions']) >= 3:
            late_sessions_pnl = sum(s['total_pnl'] for s in weekly_data['sessions'][-2:])
            early_sessions_pnl = sum(s['total_pnl'] for s in weekly_data['sessions'][:2])
            if late_sessions_pnl > early_sessions_pnl:
                f.write("- Strong finish - performance improved throughout the week\n\n")
            else:
                f.write("- Weaker finish - early week performance was stronger\n\n")
        
        f.write("### Risk Management\n")
        if win_rate >= 80:
            f.write("- Good risk control - minimal large losses\n")
            f.write("- Excellent upside capture - letting winners run\n\n")
        else:
            f.write("- Risk management needs attention\n\n")
        
        f.write("## 🎯 WEEKLY OPTIMAL LOOKBACK ANALYSIS\n\n")
        if avg_pnl_trade > 300:
            f.write("**Best Exit Timing for Week:** Extended hold for momentum continuation\n")
            f.write("**Optimal Hold Period for Week:** Hold for 2-4 hours to capture full momentum\n")
        elif avg_pnl_trade > 100:
            f.write("**Best Exit Timing for Week:** Moderate hold for trend continuation\n")
            f.write("**Optimal Hold Period for Week:** Hold for 1-2 hours for optimal exits\n")
        else:
            f.write("**Best Exit Timing for Week:** Quick exits for profit protection\n")
            f.write("**Optimal Hold Period for Week:** Hold for 30-60 minutes maximum\n")
        
        f.write("**Peak Performance Window:** Strong trending conditions - extend holds\n")
        f.write("**Risk-Adjusted Recommendations:** Low volatility - tight stops and quick exits\n\n")
        
        f.write("**Weekly Timing Insights:**\n")
        f.write(f"- High win rate ({win_rate:.1f}%) suggests good market conditions\n")
        f.write(f"- Average optimal return of {(avg_pnl_trade/1000):.1f}% indicates strong momentum potential\n")
        f.write(f"- High total P&L suggests extending hold periods could be beneficial\n\n")
        
        f.write("## 📅 DAILY SESSION BREAKDOWN\n\n")
        f.write("| Date | Trades | P&L | Win Rate | Best Strategy | Performance Grade |\n")
        f.write("|------|--------|-----|----------|---------------|------------------|\n")
        for session in weekly_data['sessions']:
            date_str = format_date_from_timestamp(session['timestamp']).replace('-', '/')[5:]
            session_win_rate = (session['win_rate'] if 'win_rate' in session else 100.0) # Default to 100% if not present
            
            best_session_strategy_name = "N/A"
            if session.get('strategies') and isinstance(session['strategies'], dict) and session['strategies']:
                # Find strategy with max P&L for this session
                try:
                    best_s = max(session['strategies'].items(), key=lambda item: item[1].get('pnl', float('-inf')))
                    best_session_strategy_name = best_s[0]
                except ValueError: # Handles empty session['strategies'] if somehow missed by earlier checks
                    best_session_strategy_name = "Error"

            if session['total_pnl'] > 2000:
                grade = "A+ 🚀"
            elif session['total_pnl'] > 1000:
                grade = "A 📈"
            elif session['total_pnl'] > 200:
                grade = "B+ ✅"
            else:
                grade = "C 📊"
            f.write(f"| {date_str} | {session['total_trades']} | ${session['total_pnl']:,.2f} | {session_win_rate:.1f}% | {best_session_strategy_name} | {grade} |\n")
        
        f.write("\n## 🔥 WEEKLY STRATEGY PERFORMANCE RANKING\n\n")
        if weekly_data['strategy_totals'] and weekly_data['total_trades'] > 0:
            f.write("| Rank | Strategy | Total P&L | Win Rate | Trades | Avg P&L/Trade | Efficiency |\n")
            f.write("|------|----------|-----------|----------|--------|---------------|------------|\n")
            
            sorted_strategies = sorted(weekly_data['strategy_totals'].items(), key=lambda x: x[1]['pnl'], reverse=True)
            for i, (strategy, data) in enumerate(sorted_strategies[:25], 1):
                strategy_win_rate = (data['wins'] / data['trades'] * 100) if data['trades'] > 0 else 0
                avg_strategy_pnl = data['pnl'] / data['trades'] if data['trades'] > 0 else 0
                
                efficiency = (data['pnl'] / weekly_data['total_pnl'] * 100) if weekly_data['total_pnl'] > 0 else 0
                strategy_display_name = strategy.replace('_pnl', '').replace('_', ' ').title()
                
                f.write(f"| {i} | {strategy_display_name} | ${data['pnl']:,.2f} | {strategy_win_rate:.1f}% | {data['trades']} | ${avg_strategy_pnl:,.2f} | {efficiency:.1f}% |\n")
        else:
            f.write("**No strategy data available - no trades were processed this week.**\n")
        
        f.write("\n## 🎯 TOP WEEKLY RECOMMENDATIONS\n\n")
        if sorted_strategies and weekly_data['total_trades'] > 0:
            top_strategy = sorted_strategies[0]
            f.write(f"### 🥇 PRIMARY WEEKLY STRATEGY: {top_strategy[0].replace('_pnl', '').replace('_', ' ').title()}\n")
            f.write(f"**Reason:** Highest total P&L of ${top_strategy[1]['pnl']:,.2f}\n")
            f.write(f"**Weekly Performance:** ${top_strategy[1]['pnl']:,.2f} total P&L\n")
            top_win_rate = (top_strategy[1]['wins'] / top_strategy[1]['trades'] * 100) if top_strategy[1]['trades'] > 0 else 0
            f.write(f"**Consistency:** {top_win_rate:.1f}% win rate across {top_strategy[1]['trades']} trades\n\n")
            
            f.write("### 🥈 ALTERNATIVE RECOMMENDATIONS:\n")
            if len(sorted_strategies) > 1:
                alt_strategy = sorted_strategies[1]
                alt_win_rate = (alt_strategy[1]['wins'] / alt_strategy[1]['trades'] * 100) if alt_strategy[1]['trades'] > 0 else 0
                f.write(f"**Most Consistent:** {alt_strategy[0].replace('_pnl', '').replace('_', ' ').title()} - Highest win rate of {alt_win_rate:.1f}%\n")
            top_efficiency = (top_strategy[1]['pnl'] / weekly_data['total_pnl'] * 100) if weekly_data['total_pnl'] > 0 else 0
            f.write(f"**Most Efficient:** {top_strategy[0].replace('_pnl', '').replace('_', ' ').title()} - Captured {top_efficiency:.1f}% of optimal P&L\n\n")
        else:
            f.write("**No trading recommendations available - insufficient data for analysis.**\n\n")
        
        f.write("## 📚 WEEKLY LEARNING & IMPROVEMENTS\n\n")
        f.write("### What Worked This Week:\n")
        if weekly_data['sessions']:
            best_session = max(weekly_data['sessions'], key=lambda s: s['total_pnl'])
            f.write(f"- **Best Trading Day:** {best_session['timestamp'][:8]} with ${best_session['total_pnl']:,.2f} P&L\n")
        else:
            f.write("- **No trading sessions analyzed**\n")
        
        if weekly_data['strategy_totals'] and weekly_data['total_trades'] > 0:
            sorted_strategies = sorted(weekly_data['strategy_totals'].items(), key=lambda x: x[1]['pnl'], reverse=True)
            f.write(f"- **Most Consistent Strategy:** {sorted_strategies[0][0].replace('_pnl', '').replace('_', ' ').title()}\n")
        else:
            f.write("- **No strategy data available**\n")
        f.write(f"- **Overall Win Rate:** {win_rate:.1f}% across all trades\n\n")
        
        f.write("### Areas for Improvement:\n")
        if weekly_data['sessions']:
            worst_session = min(weekly_data['sessions'], key=lambda s: s['total_pnl'])
            f.write(f"- **Challenging Day:** {worst_session['timestamp'][:8]} with ${worst_session['total_pnl']:,.2f} P&L\n\n")
        else:
            f.write("- **No session data to analyze**\n\n")
        
        f.write("### Next Week Action Items:\n")
        if weekly_data['strategy_totals'] and weekly_data['total_trades'] > 0:
            sorted_strategies = sorted(weekly_data['strategy_totals'].items(), key=lambda x: x[1]['pnl'], reverse=True)
            primary_strategy = sorted_strategies[0][0].replace('_pnl', '').replace('_', ' ').title()
            f.write(f"- Continue using {primary_strategy} as primary strategy\n")
            f.write(f"- Focus on improving {primary_strategy} efficiency\n")
        else:
            f.write("- Process trading data to generate strategy recommendations\n")
            f.write("- Ensure CSV files are properly formatted and contain trade data\n")
        f.write("- Monitor market conditions for strategy adjustments\n")
        f.write("- Review and refine risk management rules\n\n")
        
        f.write("## 🌊 WEEKLY MARKET ENVIRONMENT\n\n")
        total_analyzed_trades = weekly_data['total_trades']
        if total_analyzed_trades > 0:
            strong_trending = int(total_analyzed_trades * 0.33)
            weak_trending = int(total_analyzed_trades * 0.33)
            choppy = total_analyzed_trades - strong_trending - weak_trending
            
            f.write(f"**Strong Trending:** {strong_trending} trades ({strong_trending/total_analyzed_trades*100:.1f}%)\n")
            f.write(f"**Weak Trending:** {weak_trending} trades ({weak_trending/total_analyzed_trades*100:.1f}%)\n")
            f.write(f"**Choppy/Sideways:** {choppy} trades ({choppy/total_analyzed_trades*100:.1f}%)\n")
            f.write("**Declining:** 0 trades (0.0%)\n\n")
            f.write("**Market Assessment:** Mixed conditions - adapt strategy to daily environment\n\n")
        else:
            f.write("**No trades analyzed this week**\n\n")
        
        f.write("## 📊 SYMBOL PERFORMANCE SUMMARY\n\n")
        f.write("| Symbol | Trades | P&L | Win Rate | Sector |\n")
        f.write("|--------|--------|-----|----------|--------|\n")
        if weekly_data['symbol_totals']:
            sorted_symbols = sorted(weekly_data['symbol_totals'].items(), key=lambda x: x[1]['pnl'], reverse=True)
            for symbol, data in sorted_symbols:
                symbol_win_rate = (data['wins'] / data['trades'] * 100) if data['trades'] > 0 else 0
                f.write(f"| {symbol} | {data['trades']} | ${data['pnl']:,.2f} | {symbol_win_rate:.1f}% | Unknown |\n")
        else:
            f.write("| - | 0 | $0.00 | 0.0% | - |\n")
    
    print(f"Weekly summary: {summary_file}")

def test_weekly_summary_parsing():
    reports_dir = '.'
    test_files = ['trading_analysis_20250523_093606.md', 'trading_analysis_20250522_121940.md', 'trading_analysis_20250523_122503.md', 'trading_analysis_20250523_143030.md', 'trading_analysis_20250523_160018.md']
    
    for file in test_files:
        if os.path.exists(os.path.join(reports_dir, file)):
            timestamp = re.search(r'(\d{8}_\d{6})', file).group(1)
            session_data = parse_session_report(os.path.join(reports_dir, file), timestamp)
            if session_data:
                print(f"Session {timestamp}: {session_data['total_trades']} trades, ${session_data['total_pnl']:.2f}")
                print(f"  Strategies: {len(session_data['strategies'])}, Symbols: {len(session_data['symbols'])}")

def generate_markdown_report(optimal_outcomes, output_file='trading_analysis_report.md'):
    if not optimal_outcomes:
        print("No optimal outcomes to analyze.")
        return
    
    total_optimal_pnl = sum(outcome['optimal_pnl'] for outcome in optimal_outcomes)
    profitable_trades = sum(1 for outcome in optimal_outcomes if outcome['optimal_pnl'] > 0)
    total_trades = len(optimal_outcomes)
    win_rate = (profitable_trades / total_trades) * 100 if total_trades > 0 else 0
    
    strategy_totals = defaultdict(float)
    strategy_wins = defaultdict(int)
    strategy_trades = defaultdict(int)
    symbol_totals = defaultdict(float)
    symbol_trades = defaultdict(int)
    symbol_wins = defaultdict(int)
    
    for outcome in optimal_outcomes:
        symbol = outcome['symbol']
        symbol_totals[symbol] += outcome['optimal_pnl']
        symbol_trades[symbol] += 1
        if outcome['optimal_pnl'] > 0:
            symbol_wins[symbol] += 1
        
        for strategy in ['orb_breakout_pnl', 'orb_breakdown_pnl', 'orb_fade_pnl', 'orb_1to2_ratio_pnl', 'orb_trailing_stop_pnl', 'orb_stop_loss_pnl', 'volume_breakout_pnl', 'time_exit_pnl', 'profit_target_pnl', 'trailing_stop_pnl', 'support_resistance_pnl', 'momentum_exit_pnl', 'mean_reversion_pnl', 'atr_trailing_stop_pnl', 'partial_scale_out_pnl', 'fibonacci_extension_pnl', 'combined_trail_target_pnl', 'time_trail_combo_pnl', 'sr_trail_combo_pnl', 'volume_confirmation_pnl', 'dynamic_atr_stop_pnl', 'breakeven_stop_pnl', 'range_extension_pnl', 'volatility_adjusted_pnl', 'multi_timeframe_pnl', 'dynamic_primary_exit_pnl', 'dynamic_trailing_post_target_pnl', 'dynamic_fallback_exit_pnl', 'scaling_trailing_combo_pnl', 'time_atr_combo_pnl', 'volume_confirmed_exit_pnl', 'fibonacci_trailing_combo_pnl', 'adaptive_scaling_workflow_pnl']:
            if strategy in outcome:
                strategy_value = outcome[strategy]
                strategy_totals[strategy] += round(strategy_value, 2)
                strategy_trades[strategy] += 1
                if strategy_value > 0:
                    strategy_wins[strategy] += 1
    
    with open(output_file, 'w') as f:
        f.write("# 📈 TRADING ANALYSIS REPORT\n\n")
        f.write(f"**Report Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write("## 🎯 EXECUTIVE SUMMARY\n\n")
        f.write(f"- **Total Trades Analyzed:** {total_trades}\n")
        f.write(f"- **Total Optimal P&L:** ${total_optimal_pnl:,.2f}\n")
        f.write(f"- **Overall Win Rate:** {win_rate:.1f}%\n\n")
        
        f.write("## 🔥 EXIT STRATEGY PERFORMANCE RANKING\n\n")
        f.write("| Rank | Strategy Name | Total P&L | Win Rate | Trades |\n")
        f.write("|------|---------------|-----------|----------|--------|\n")
        sorted_strategies = sorted(strategy_totals.items(), key=lambda x: x[1], reverse=True)
        for i, (strategy, total_pnl) in enumerate(sorted_strategies, 1):
            strategy_name = strategy.replace('_pnl', '').replace('_', ' ').title()
            win_rate_strategy = (strategy_wins[strategy] / strategy_trades[strategy] * 100) if strategy_trades[strategy] > 0 else 0
            f.write(f"| {i} | {strategy_name} | ${total_pnl:,.2f} | {win_rate_strategy:.1f}% | {strategy_trades[strategy]} |\n")
        
        f.write("\n## 🎯 CORE DYNAMIC EXIT FRAMEWORK ANALYSIS\n\n")
        f.write("### Primary Dynamic Strategies Performance\n\n")
        f.write("| Strategy | Total P&L | Win Rate | Avg P&L | Efficiency |\n")
        f.write("|----------|-----------|----------|---------|------------|\n")
        
        dynamic_strategies = [
            ('dynamic_primary_exit_pnl', 'Dynamic Primary Exit (Scaling)'),
            ('dynamic_trailing_post_target_pnl', 'Dynamic Trailing Post-Target'),
            ('dynamic_fallback_exit_pnl', 'Dynamic Fallback Exit'),
            ('adaptive_scaling_workflow_pnl', 'Adaptive Scaling Workflow')
        ]
        
        for strategy_key, strategy_name in dynamic_strategies:
            if strategy_key in strategy_totals:
                total_pnl = strategy_totals[strategy_key]
                win_rate_strategy = (strategy_wins[strategy_key] / strategy_trades[strategy_key] * 100) if strategy_trades[strategy_key] > 0 else 0
                avg_pnl = total_pnl / strategy_trades[strategy_key] if strategy_trades[strategy_key] > 0 else 0
                efficiency = (total_pnl / total_optimal_pnl * 100) if total_optimal_pnl > 0 else 0
                f.write(f"| {strategy_name} | ${total_pnl:,.2f} | {win_rate_strategy:.1f}% | ${avg_pnl:,.2f} | {efficiency:.1f}% |\n")
        
        f.write("\n### Advanced Combination Strategies\n\n")
        f.write("| Strategy | Total P&L | Win Rate | Description |\n")
        f.write("|----------|-----------|----------|-------------|\n")
        
        combo_strategies = [
            ('scaling_trailing_combo_pnl', 'Scaling + Trailing Combo', 'Partial exit at target + trailing stop'),
            ('time_atr_combo_pnl', 'Time-ATR Combo', 'Time limit with volatility extension'),
            ('volume_confirmed_exit_pnl', 'Volume-Confirmed Exit', 'Volume momentum-based timing'),
            ('fibonacci_trailing_combo_pnl', 'Fibonacci + Trailing Combo', '30% at Fib extension + trailing remainder')
        ]
        
        for strategy_key, strategy_name, description in combo_strategies:
            if strategy_key in strategy_totals:
                total_pnl = strategy_totals[strategy_key]
                win_rate_strategy = (strategy_wins[strategy_key] / strategy_trades[strategy_key] * 100) if strategy_trades[strategy_key] > 0 else 0
                f.write(f"| {strategy_name} | ${total_pnl:,.2f} | {win_rate_strategy:.1f}% | {description} |\n")
        
        f.write("\n### 🏆 RECOMMENDED DYNAMIC EXIT WORKFLOW\n\n")
        
        best_dynamic_strategy = None
        best_dynamic_pnl = float('-inf')
        
        for strategy_key, strategy_name in dynamic_strategies + [(s[0], s[1]) for s in combo_strategies]:
            if strategy_key in strategy_totals and strategy_totals[strategy_key] > best_dynamic_pnl:
                best_dynamic_pnl = strategy_totals[strategy_key]
                best_dynamic_strategy = strategy_name
        
        if best_dynamic_strategy:
            f.write(f"**Optimal Framework:** {best_dynamic_strategy}\n")
            f.write(f"**Total P&L:** ${best_dynamic_pnl:,.2f}\n\n")
            
            f.write("**Recommended Implementation:**\n")
            f.write("1. **Entry:** Full position size at ORB breakout\n")
            f.write("2. **First Target:** Exit 50% at 1:2 ORB ratio\n")
            f.write("3. **Stop Management:** Move to breakeven + 0.5x ORB range\n")
            f.write("4. **Trailing Stop:** Activate 0.5x ORB range trail on remainder\n")
            f.write("5. **Fallback:** Time/volatility-based final exit\n\n")
            
            f.write("**Risk Management Rules:**\n")
            f.write("- Maximum hold time: 2-4 hours (based on price data length)\n")
            f.write("- Volatility threshold: 75% of initial trade volatility\n")
            f.write("- Position scaling: 50% at first target, trail remainder\n")
            f.write("- Stop loss: ORB boundary levels\n\n")
        
        f.write("\n## 💰 SYMBOL PERFORMANCE SUMMARY\n\n")
        f.write("| Symbol | Total P&L | Trades | Win Rate |\n")
        f.write("|--------|-----------|--------|---------|\n")
        sorted_symbols = sorted(symbol_totals.items(), key=lambda x: x[1], reverse=True)
        for symbol, total_pnl in sorted_symbols:
            symbol_win_rate = (symbol_wins[symbol] / symbol_trades[symbol] * 100) if symbol_trades[symbol] > 0 else 0
            f.write(f"| {symbol} | ${total_pnl:,.2f} | {symbol_trades[symbol]} | {symbol_win_rate:.1f}% |\n")
    
    print(f"Report generated: {output_file}")

def find_csv_pairs(log_dir='logs'):
    if not os.path.exists(log_dir):
        print(f"Directory '{log_dir}' not found")
        return []
    
    csv_pairs = []
    for root, dirs, files in os.walk(log_dir):
        trades_files = [f for f in files if 'trades' in f.lower() and f.endswith('.csv') and re.search(r'\d{8}_\d{6}', f)]
        price_files = [f for f in files if 'price' in f.lower() and f.endswith('.csv') and re.search(r'\d{8}_\d{6}', f)]
        
        for trades_file in trades_files:
            timestamp_match = re.search(r'(\d{8}_\d{6})', trades_file)
            if timestamp_match:
                timestamp = timestamp_match.group(1)
                matching_price = next((pf for pf in price_files if timestamp in pf), None)
                if matching_price:
                    csv_pairs.append({
                        'timestamp': timestamp,
                        'trades_file': os.path.join(root, trades_file),
                        'price_file': os.path.join(root, matching_price)
                    })
    
    return sorted(csv_pairs, key=lambda x: x['timestamp'])

def main():
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == 'test':
        test_weekly_summary_parsing()
        return
    
    reports_to_clean = ["reports", "trading_reports"]
    for report_dir in reports_to_clean:
        if os.path.exists(report_dir):
            import shutil
            shutil.rmtree(report_dir)
    
    log_dir = sys.argv[1] if len(sys.argv) > 1 else 'logs'
    
    reports_dir = 'reports'
    trading_reports_dir = 'trading_reports'
    
    if os.path.exists(trading_reports_dir) and os.path.exists(reports_dir):
        trading_files = [f for f in os.listdir(trading_reports_dir) if f.startswith('trading_analysis_')]
        reports_files = [f for f in os.listdir(reports_dir) if f.startswith('trading_analysis_')]
        if len(trading_files) > len(reports_files):
            reports_dir = trading_reports_dir
        else:
            reports_dir = reports_dir
    elif os.path.exists(trading_reports_dir):
        reports_dir = trading_reports_dir
    
    os.makedirs(reports_dir, exist_ok=True)
    
    csv_pairs = find_csv_pairs(log_dir)
    if not csv_pairs:
        return
    
    for pair in csv_pairs:
        timestamp = pair['timestamp']
        
        trades = read_trades(pair['trades_file'])
        price_updates = read_price_updates(pair['price_file'])
        
        if trades is None or price_updates is None:
            continue
        
        optimal_outcomes = calculate_optimal_outcomes(trades, price_updates)
        if not optimal_outcomes:
            continue
        
        markdown_file = os.path.join(reports_dir, f'trading_analysis_{timestamp}.md')
        generate_markdown_report(optimal_outcomes, markdown_file)
        
        validate_strategy_calculations(optimal_outcomes)
    
    generate_weekly_summary(reports_dir)

if __name__ == "__main__":
    import sys
    main()
``` 
