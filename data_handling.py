#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Data Handling - Market data processing for the ORB Scanner.

This module contains methods related to market data callbacks (ticks, historical data, contract details),
opening range calculations, volume handling, and related data validation/processing.
"""

import logging
import time
import threading
from datetime import datetime

from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract

# Import our modules from the orbscanner package
from orbscanner.utils.phase.logging import log_to_phase
from orbscanner.data.historical_data import get_historical_opening_range_with_exchange

# Base class for IBApp that handles market data
class IBAppDataHandling(EWrapper, EClient):
    def __init__(self):
        """
        Initialize the IBAppDataHandling instance.
        This method initializes attributes related to market data handling.
        """
        # Initialize attributes that might be accessed before they're defined
        self.market_data_req_ids = {}
        self.tracked_symbols = {}
        self.opening_range_data = {}
        self.vix_received_event = threading.Event()
        self.after_open_timer = None
        self.historical_data_buffer = {}
        self.historical_data_finished = {}
        self.historical_data_events = {}
        self.reqId_to_symbol = {}
        self.contract_details_events = {}
        self.phase_manager = None
        self.args = None
        self.valid_symbols = []
        self.reqId = 0
        self.active_requests = {}
        self.historical_data_processed_for_orb = False  # Track if historical data processing completed for ORB

        # Initialize real-time data dictionary for monitoring phase
        self.real_time_data = {}  # Dictionary to store real-time price and volume data

        # Initialize dictionaries used in various methods that check for existence
        self.daily_data_buffer = {}
        self.daily_data_finished = {}
        self.daily_data_events = {}
        self.avg_volumes = {}
        self.symbol_data = {}
        self.contract_details = {}
        self.contract_details_received = {}

    def get_next_req_id(self):
        """
        Get a unique request ID and track it as active.

        Returns:
            int: A unique request ID
        """
        self.reqId += 1
        req_id = self.reqId
        self.active_requests[req_id] = time.time()
        return req_id

    def create_standard_contract(self, symbol):
        """
        Create a standardized contract with only essential parameters.

        Args:
            symbol (str): The stock symbol

        Returns:
            Contract: A standard contract object
        """
        # Ensure symbol is a string
        if not isinstance(symbol, str):
            logging.error(f"Cannot create contract for invalid symbol type: {type(symbol)}")
            return None

        # If symbol is invalid, return None
        if symbol == "UNKNOWN" or symbol == "INVALID":
            logging.error(f"Cannot create contract for invalid symbol: {symbol}")
            return None

        # Create contract with validated symbol
        contract = Contract()
        contract.symbol = symbol
        contract.secType = "STK"
        contract.exchange = "SMART"
        contract.currency = "USD"

        # Log the created contract for debugging
        logging.debug(f"Created standard contract: {contract.symbol} {contract.secType} {contract.exchange} {contract.currency}")

        return contract

    def get_next_req_id(self):
        """
        Get the next available request ID.

        Returns:
            int: Next request ID
        """
        self.reqId += 1
        return self.reqId

    def subscribe_to_market_data(self, symbols):
        """
        Subscribe to real-time market data for a list of symbols.
        This method is called when transitioning to the monitoring phase.

        Args:
            symbols (list): List of symbols to subscribe to

        Returns:
            int: Number of successful subscriptions
        """
        from orbscanner.utils.phase.manager import PhaseManager

        if not symbols:
            logging.warning("No symbols provided for market data subscription")
            log_to_phase(PhaseManager.MONITORING, "No symbols provided for market data subscription", level=logging.WARNING)
            return 0

        logging.info(f"Subscribing to real-time market data for {len(symbols)} symbols")
        log_to_phase(PhaseManager.MONITORING, f"Subscribing to real-time market data for {len(symbols)} symbols", level=logging.INFO)

        # Initialize real-time data dictionary if needed
        if not hasattr(self, 'real_time_data'):
            self.real_time_data = {}

        # Track successful subscriptions
        success_count = 0

        # Subscribe to each symbol
        for symbol in symbols:
            # Skip if already subscribed
            if symbol in self.market_data_req_ids:
                logging.info(f"Already subscribed to market data for {symbol}")
                success_count += 1
                continue

            # Create contract
            contract = self.create_standard_contract(symbol)
            if not contract:
                logging.error(f"Failed to create contract for {symbol}")
                log_to_phase(PhaseManager.MONITORING, f"Failed to create contract for {symbol}", level=logging.ERROR)
                continue

            # Generate request ID
            req_id = self.get_next_req_id()

            # Store mapping of request ID to symbol
            self.reqId_to_symbol[req_id] = symbol
            self.market_data_req_ids[symbol] = req_id

            # Initialize real-time data entry
            self.real_time_data[symbol] = {
                'last_price': 0,
                'volume': 0,
                'bid': 0,
                'ask': 0,
                'last_update': time.time()
            }

            # Request market data with tick types for price and volume
            # 233 = RTVolume (real-time volume, price, and time)
            # 165 = Misc Stats (implied volatility, etc.)
            # 295 = RTTrdVolume (real-time trade volume)
            # 318 = LastRTHTrade (last regular trading hours trade)
            generic_tick_list = "165,233,295,318"

            try:
                logging.info(f"Requesting market data for {symbol} (reqId: {req_id})")
                log_to_phase(PhaseManager.MONITORING, f"Requesting market data for {symbol} (reqId: {req_id})", level=logging.INFO)

                self.reqMktData(
                    req_id,
                    contract,
                    generic_tick_list,  # Generic tick types
                    False,              # Snapshot
                    False,              # Regulatory snapshot
                    []                  # Market data options
                )
                success_count += 1
            except Exception as e:
                logging.error(f"Error requesting market data for {symbol}: {e}")
                log_to_phase(PhaseManager.MONITORING, f"Error requesting market data for {symbol}: {e}", level=logging.ERROR)
                continue

        logging.info(f"Successfully subscribed to market data for {success_count}/{len(symbols)} symbols")
        log_to_phase(PhaseManager.MONITORING, f"Successfully subscribed to market data for {success_count}/{len(symbols)} symbols", level=logging.INFO)

        return success_count

    def tickPrice(self, reqId, tickType, price, attrib):  # pylint: disable=unused-argument
        """
        Callback for price tick data.

        Args:
            reqId (int): Request ID
            tickType (int): Tick type
            price (float): Price value
            attrib (TickAttrib): Tick attributes
        """
        # Find symbol for this request ID
        symbol = None
        for sym, rid in self.market_data_req_ids.items():
            if rid == reqId:
                symbol = sym
                break

        if not symbol:
            logging.warning(f"Received tick price for unknown reqId: {reqId}")
            return

        # Log the tick with more detail
        tick_names = {1: "Bid", 2: "Ask", 4: "Last", 6: "High", 7: "Low", 9: "Close", 14: "Open"}
        tick_name = tick_names.get(tickType, f"Type-{tickType}")
        
        # Get current phase for better logging context
        from orbscanner.utils.phase.manager import PhaseManager
        current_phase = "UNKNOWN"
        if hasattr(self, 'phase_manager') and self.phase_manager:
            current_phase = self.phase_manager.current_phase
            
        # Enhanced logging with phase information
        logging.debug(f"Tick price [{current_phase}]: {symbol} - {tick_name}: {price}")
        
        # More verbose logging during ORB phase
        if current_phase == PhaseManager.ORB:
            logging.info(f"ORB PHASE: Received {tick_name} price for {symbol}: {price}")
            log_to_phase(PhaseManager.ORB, f"Received {tick_name} price for {symbol}: {price}", level=logging.INFO)

        # Process tick based on type
        if tickType == 1:  # Bid price
            if symbol in self.tracked_symbols:
                self.tracked_symbols[symbol]['bid'] = price
                logging.debug(f"Updated bid price for tracked symbol {symbol}: {price}")
            # Update real-time data for monitoring
            if symbol in self.real_time_data:
                self.real_time_data[symbol]['bid'] = price
                self.real_time_data[symbol]['last_update'] = time.time()
        elif tickType == 2:  # Ask price
            if symbol in self.tracked_symbols:
                self.tracked_symbols[symbol]['ask'] = price
                logging.debug(f"Updated ask price for tracked symbol {symbol}: {price}")
            # Update real-time data for monitoring
            if symbol in self.real_time_data:
                self.real_time_data[symbol]['ask'] = price
                self.real_time_data[symbol]['last_update'] = time.time()
        elif tickType == 4:  # Last price
            if symbol in self.tracked_symbols:
                self.tracked_symbols[symbol]['last'] = price
                logging.debug(f"Updated last price for tracked symbol {symbol}: {price}")
                # Update opening range from tick
                self.update_opening_range_from_tick(symbol, price)
            # Update real-time data for monitoring
            if symbol in self.real_time_data:
                self.real_time_data[symbol]['last_price'] = price
                self.real_time_data[symbol]['last_update'] = time.time()
        elif tickType == 6:  # High price
            if symbol in self.tracked_symbols:
                self.tracked_symbols[symbol]['high'] = price
        elif tickType == 7:  # Low price
            if symbol in self.tracked_symbols:
                self.tracked_symbols[symbol]['low'] = price
        elif tickType == 9:  # Close price
            if symbol in self.tracked_symbols:
                self.tracked_symbols[symbol]['close'] = price
        elif tickType == 14:  # Open price
            if symbol in self.tracked_symbols:
                self.tracked_symbols[symbol]['open'] = price

        # Special handling for VIX
        if reqId == getattr(self, 'vix_req_id', None):
            if tickType == 4:  # Last price
                # Update VIX value
                self.vix_value = price
                logging.info(f"VIX value updated: {price:.2f}")
                # Signal that VIX data has been received
                self.vix_received_event.set()
                # Cancel the request
                self.cancelMktData(reqId)

    def tickSize(self, reqId, tickType, size):
        """
        Callback for size tick data.

        Args:
            reqId (int): Request ID
            tickType (int): Tick type
            size (int): Size value
        """
        # Find symbol for this request ID
        symbol = None
        for sym, rid in self.market_data_req_ids.items():
            if rid == reqId:
                symbol = sym
                break

        if not symbol:
            logging.warning(f"Received tick size for unknown reqId: {reqId}")
            return

        # Log the tick
        logging.debug(f"Tick size: {symbol} - Type {tickType}: {size}")

        # Process tick based on type
        if tickType == 0:  # Bid size
            if symbol in self.tracked_symbols:
                self.tracked_symbols[symbol]['bid_size'] = size
        elif tickType == 3:  # Ask size
            if symbol in self.tracked_symbols:
                self.tracked_symbols[symbol]['ask_size'] = size
        elif tickType == 5:  # Last size
            if symbol in self.tracked_symbols:
                self.tracked_symbols[symbol]['last_size'] = size
        elif tickType == 8:  # Volume
            if symbol in self.tracked_symbols:
                self.tracked_symbols[symbol]['volume'] = size
            # Update real-time data for monitoring
            if symbol in self.real_time_data:
                self.real_time_data[symbol]['volume'] = size
                self.real_time_data[symbol]['last_update'] = time.time()
                logging.debug(f"Updated real-time volume for {symbol}: {size}")

    def tickString(self, reqId, tickType, value):
        """
        Callback for string tick data.

        Args:
            reqId (int): Request ID
            tickType (int): Tick type
            value (str): Tick value as string
        """
        # Find symbol for this request ID
        symbol = None
        for sym, rid in self.market_data_req_ids.items():
            if rid == reqId:
                symbol = sym
                break

        if not symbol:
            logging.warning(f"Received tick string for unknown reqId: {reqId}")
            return

        # Log the tick
        logging.debug(f"Tick string: {symbol} - Type {tickType}: {value}")

        # Process tick based on type
        if tickType == 48:  # RT Volume - format: price;size;time;totalVolume;VWAP;single
            try:
                # Skip empty values
                if not value or value.isspace():
                    logging.debug(f"Empty RT Volume value for {symbol}")
                    return

                parts = value.split(';')
                if len(parts) >= 4:
                    # Handle price - skip if empty
                    if parts[0] and not parts[0].isspace():
                        try:
                            price = float(parts[0])
                            # Update real-time data for monitoring
                            if symbol in self.real_time_data:
                                self.real_time_data[symbol]['last_price'] = price
                                self.real_time_data[symbol]['last_update'] = time.time()
                                logging.debug(f"Updated real-time price for {symbol}: {price}")
                        except ValueError as e:
                            logging.debug(f"Error parsing price in RT Volume for {symbol}: {e}")

                    # Handle volume - convert to float first to handle "40.0000000000000000" format
                    if parts[3] and not parts[3].isspace():
                        try:
                            # Convert to float first, then to int to handle floating point strings
                            total_volume = int(float(parts[3]))
                            # Update real-time data for monitoring
                            if symbol in self.real_time_data:
                                self.real_time_data[symbol]['volume'] = total_volume
                                self.real_time_data[symbol]['last_update'] = time.time()
                                logging.debug(f"Updated real-time volume for {symbol}: {total_volume}")
                        except ValueError as e:
                            logging.debug(f"Error parsing volume in RT Volume for {symbol}: {e}")
            except Exception as e:
                logging.warning(f"Error parsing RT Volume for {symbol}: {e}")

        elif tickType == 32:  # Bid/Ask Exchange
            # Format: BID_EXCH;ASK_EXCH
            try:
                parts = value.split(';')
                if len(parts) >= 2:
                    bid_exchange = parts[0]
                    ask_exchange = parts[1]

                    # Update real-time data for monitoring
                    if symbol in self.real_time_data:
                        self.real_time_data[symbol]['bid_exchange'] = bid_exchange
                        self.real_time_data[symbol]['ask_exchange'] = ask_exchange
                        logging.debug(f"Updated exchanges for {symbol}: bid={bid_exchange}, ask={ask_exchange}")
            except (ValueError, IndexError) as e:
                logging.warning(f"Error parsing Bid/Ask Exchange for {symbol}: {e}")

        elif tickType == 33:  # Last Exchange
            # Update real-time data for monitoring
            if symbol in self.real_time_data:
                self.real_time_data[symbol]['last_exchange'] = value
                logging.debug(f"Updated last exchange for {symbol}: {value}")

    def tickGeneric(self, reqId, tickType, value):
        """
        Callback for generic tick data.

        Args:
            reqId (int): Request ID
            tickType (int): Tick type
            value (float): Tick value
        """
        # Find symbol for this request ID
        symbol = None
        for sym, rid in self.market_data_req_ids.items():
            if rid == reqId:
                symbol = sym
                break

        if not symbol:
            logging.warning(f"Received generic tick for unknown reqId: {reqId}")
            return

        # Log the tick
        logging.debug(f"Generic tick: {symbol} - Type {tickType}: {value}")

        # Update real-time data for monitoring
        if symbol in self.real_time_data:
            self.real_time_data[symbol][f'generic_{tickType}'] = value
            self.real_time_data[symbol]['last_update'] = time.time()

    def update_opening_range_from_tick(self, symbol, price):
        """
        Update opening range for a symbol based on a tick.

        Args:
            symbol (str): Symbol to update
            price (float): Price from tick
        """
        from orbscanner.utils.phase.manager import PhaseManager

        # Check if we're in ORB phase
        in_orb_phase = False
        if hasattr(self, 'phase_manager') and self.phase_manager:
            in_orb_phase = self.phase_manager.current_phase == PhaseManager.ORB
        
        # Enhanced logging for ORB phase
        if in_orb_phase:
            logging.info(f"ORB PHASE: Updating opening range from tick for {symbol} with price {price}")
            log_to_phase(PhaseManager.ORB, f"Updating opening range from tick for {symbol} with price {price}", level=logging.INFO)
        else:
            logging.debug(f"Updating opening range from tick for {symbol} with price {price}")

        # Ensure we have a valid price
        if not price or price <= 0:
            logging.warning(f"Invalid price for {symbol}: {price}")
            if in_orb_phase:
                log_to_phase(PhaseManager.ORB, f"Invalid price for {symbol}: {price}", level=logging.WARNING)
            return

        # Ensure symbol is in opening_range_data
        if symbol not in self.opening_range_data:
            self.opening_range_data[symbol] = {
                'high': None,
                'low': None,
                'open': None,
                'close': None,
                'volume': 0,
                'timestamp': datetime.now().isoformat(),
                'source': 'tick'
            }
            
            if in_orb_phase:
                logging.info(f"ORB PHASE: Initialized opening range data for {symbol}")
                log_to_phase(PhaseManager.ORB, f"Initialized opening range data for {symbol}", level=logging.INFO)

        # Get current opening range data
        range_data = self.opening_range_data[symbol]

        # Update high if needed
        if range_data['high'] is None or price > range_data['high']:
            self._update_symbol_opening_range_high(symbol, price)

        # Update low if needed
        if range_data['low'] is None or price < range_data['low']:
            self._update_symbol_opening_range_low(symbol, price)

        # Update open if needed
        if range_data['open'] is None:
            self._update_symbol_opening_range_open(symbol, price)

        # Always update close (latest price)
        self._update_symbol_opening_range(symbol, 'close', price)

        # Log the update
        logging.debug(f"Updated opening range for {symbol} from tick: {range_data}")

    def _update_symbol_opening_range(self, symbol, field, value):
        """
        Update a specific field in the opening range data for a symbol.

        Args:
            symbol (str): Symbol to update
            field (str): Field to update
            value: Value to set
        """
        if symbol in self.opening_range_data:
            self.opening_range_data[symbol][field] = value
            self.opening_range_data[symbol]['timestamp'] = datetime.now().isoformat()

    def _update_symbol_opening_range_high(self, symbol, value):
        """
        Update the high value in the opening range data for a symbol.

        Args:
            symbol (str): Symbol to update
            value: Value to set
        """
        self._update_symbol_opening_range(symbol, 'high', value)

    def _update_symbol_opening_range_low(self, symbol, value):
        """
        Update the low value in the opening range data for a symbol.

        Args:
            symbol (str): Symbol to update
            value: Value to set
        """
        self._update_symbol_opening_range(symbol, 'low', value)

    def _update_symbol_opening_range_open(self, symbol, value):
        """
        Update the open value in the opening range data for a symbol.

        Args:
            symbol (str): Symbol to update
            value: Value to set
        """
        self._update_symbol_opening_range(symbol, 'open', value)

    def update_opening_ranges(self):
        """
        Update opening ranges for all tracked symbols.
        This method is called periodically during the opening range building phase.
        """
        from orbscanner.utils.phase.manager import PhaseManager

        logging.info("Updating opening ranges for all tracked symbols")
        log_to_phase(PhaseManager.ORB, "Updating opening ranges for all tracked symbols", level=logging.INFO)

        # Log current tracked symbols
        tracked_count = len(self.tracked_symbols)
        logging.info(f"Currently tracking {tracked_count} symbols")
        log_to_phase(PhaseManager.ORB, f"Currently tracking {tracked_count} symbols", level=logging.INFO)
        
        # CRITICAL FIX: Ensure market data subscriptions exist for all tracked symbols
        # This ensures that tickPrice callbacks will be received for price updates
        if tracked_count > 0:
            symbols_to_subscribe = list(self.tracked_symbols.keys())
            logging.info(f"ORB PHASE: Subscribing to market data for {tracked_count} tracked symbols")
            log_to_phase(PhaseManager.ORB, f"Subscribing to market data for {tracked_count} tracked symbols", level=logging.INFO)
            
            # Use the existing subscribe_to_market_data method
            subscribed_count = self.subscribe_to_market_data(symbols_to_subscribe)
            logging.info(f"ORB PHASE: Successfully subscribed to market data for {subscribed_count}/{tracked_count} symbols")
            log_to_phase(PhaseManager.ORB, f"Successfully subscribed to market data for {subscribed_count}/{tracked_count} symbols", level=logging.INFO)

        # Check if we should use historical data
        from scanner_main import USE_HISTORICAL_DATA
        if USE_HISTORICAL_DATA:
            logging.info("Using historical data for opening ranges")
            log_to_phase(PhaseManager.ORB, "Using historical data for opening ranges", level=logging.INFO)
            self.update_opening_ranges_historical()
        else:
            logging.info("Using real-time data for opening ranges")
            log_to_phase(PhaseManager.ORB, "Using real-time data for opening ranges", level=logging.INFO)

            # Set flag to indicate that we're not using historical data for ORB
            self.historical_data_processed_for_orb = False
            logging.info("Using real-time data only, historical_data_processed_for_orb set to False")

            # Check if we have after_open_timer and we're in after-open mode
            if hasattr(self, 'after_open_timer') and self.after_open_timer:
                # AfterOpenTimer doesn't have is_after_open, but we know we're in after-open mode
                # if the timer exists
                logging.info("In after-open mode, using historical data for opening ranges")
                log_to_phase(PhaseManager.ORB, "In after-open mode, using historical data for opening ranges", level=logging.INFO)
                self.update_opening_ranges_historical()
                return

            # Update from real-time data
            for symbol in list(self.tracked_symbols.keys()):
                # Get current data
                symbol_data = self.tracked_symbols.get(symbol, {})

                # Check if we have price data
                if 'last' in symbol_data and symbol_data['last'] > 0:
                    # Update opening range from last price
                    self.update_opening_range_from_tick(symbol, symbol_data['last'])
                elif 'close' in symbol_data and symbol_data['close'] > 0:
                    # Update opening range from close price
                    self.update_opening_range_from_tick(symbol, symbol_data['close'])
                else:
                    logging.warning(f"No price data available for {symbol}")
                    log_to_phase(PhaseManager.ORB, f"No price data available for {symbol}", level=logging.WARNING)

        # Log updated opening ranges
        range_count = len(self.opening_range_data)
        logging.info(f"Updated opening ranges for {range_count} symbols")
        log_to_phase(PhaseManager.ORB, f"Updated opening ranges for {range_count} symbols", level=logging.INFO)

    def _update_opening_ranges_historical(self, symbols=None, duration_str="420 S", bar_size="1 min", use_rth=True):
        """
        Update opening ranges using historical data.
        This is an internal method called by update_opening_ranges_historical.

        Args:
            symbols (list, optional): List of symbols to update. If None, updates all tracked symbols.
            duration_str (str, optional): Duration string for historical data request.
            bar_size (str, optional): Bar size for historical data request.
            use_rth (bool, optional): Whether to use regular trading hours data only.

        Returns:
            dict: Dictionary of events for historical data requests
        """
        from orbscanner.utils.phase.manager import PhaseManager
        from scanner_main import HISTORICAL_DATA_TIMEOUT

        # Use all tracked symbols if none provided
        if symbols is None:
            symbols = list(self.tracked_symbols.keys())

        # Log the request
        symbol_count = len(symbols)
        logging.info(f"Requesting historical data for {symbol_count} symbols")
        log_to_phase(PhaseManager.ORB, f"Requesting historical data for {symbol_count} symbols", level=logging.INFO)

        # Create events for each symbol
        events = {}
        for symbol in symbols:
            events[symbol] = threading.Event()

        # Request historical data for each symbol
        for symbol in symbols:
            # Create contract
            contract = self.create_standard_contract(symbol)
            if not contract:
                logging.error(f"Failed to create contract for {symbol}")
                log_to_phase(PhaseManager.ORB, f"Failed to create contract for {symbol}", level=logging.ERROR)
                continue

            # Generate request ID
            req_id = self.get_next_req_id()

            # Store mapping of request ID to symbol
            self.reqId_to_symbol[req_id] = symbol

            # Initialize buffer for this request
            self.historical_data_buffer[req_id] = []
            self.historical_data_finished[req_id] = False

            # Store event for this request
            self.historical_data_events[req_id] = events[symbol]

            # Get end time (now)
            end_time = datetime.now().strftime("%Y%m%d %H:%M:%S")

            # Request historical data
            logging.info(f"Requesting historical data for {symbol} (reqId: {req_id})")
            log_to_phase(PhaseManager.ORB, f"Requesting historical data for {symbol} (reqId: {req_id})", level=logging.INFO)

            try:
                self.reqHistoricalData(
                    req_id,
                    contract,
                    end_time,
                    duration_str,
                    bar_size,
                    "TRADES",
                    1 if use_rth else 0,
                    1,
                    False,
                    []
                )
            except Exception as e:
                logging.error(f"Error requesting historical data for {symbol}: {e}")
                log_to_phase(PhaseManager.ORB, f"Error requesting historical data for {symbol}: {e}", level=logging.ERROR)
                continue

        # Wait for all requests to complete
        timeout = HISTORICAL_DATA_TIMEOUT  # seconds
        start_time = time.time()
        for symbol, event in events.items():
            remaining_time = timeout - (time.time() - start_time)
            if remaining_time <= 0:
                logging.warning(f"Timeout waiting for historical data")
                log_to_phase(PhaseManager.ORB, f"Timeout waiting for historical data", level=logging.WARNING)
                break

            if not event.wait(remaining_time):
                logging.warning(f"Timeout waiting for historical data for {symbol}")
                log_to_phase(PhaseManager.ORB, f"Timeout waiting for historical data for {symbol}", level=logging.WARNING)

        # Process historical data for each symbol
        for req_id, buffer in self.historical_data_buffer.items():
            if req_id in self.reqId_to_symbol:
                symbol = self.reqId_to_symbol[req_id]

                # Check if we have data
                if buffer:
                    # Calculate opening range from historical data
                    self._process_historical_opening_range(symbol, buffer)
                else:
                    logging.warning(f"No historical data received for {symbol}")
                    log_to_phase(PhaseManager.ORB, f"No historical data received for {symbol}", level=logging.WARNING)

        # Return events for testing
        return events

    def _process_historical_opening_range(self, symbol, bars):
        """
        Process historical data bars to calculate opening range for a symbol.

        Args:
            symbol (str): Symbol to process
            bars (list): List of BarData objects from historical data request

        Returns:
            dict: Opening range data (high, low, open, close, volume)
        """
        from orbscanner.utils.phase.manager import PhaseManager

        logging.info(f"Processing historical opening range for {symbol} with {len(bars)} bars")
        log_to_phase(PhaseManager.ORB, f"Processing historical opening range for {symbol} with {len(bars)} bars", level=logging.INFO)

        # Initialize opening range data if needed
        if symbol not in self.opening_range_data:
            self.opening_range_data[symbol] = {
                'high': 0,
                'low': float('inf'),
                'open': 0,
                'close': 0,
                'volume': 0,
                'timestamp': datetime.now().isoformat(),
                'source': 'historical'
            }

        # Get current opening range data
        range_data = self.opening_range_data[symbol]

        # Initialize values
        high = range_data.get('high', 0)
        low = range_data.get('low', float('inf'))
        if low == 0:
            low = float('inf')
        open_price = range_data.get('open', 0)
        close_price = range_data.get('close', 0)
        volume = range_data.get('volume', 0)

        # Process each bar
        for bar in bars:
            # Update high
            if bar.high > high:
                high = bar.high

            # Update low
            if bar.low < low and bar.low > 0:
                low = bar.low

            # Update open (use first bar's open)
            if open_price == 0:
                open_price = bar.open

            # Update close (use last bar's close)
            close_price = bar.close

            # Update volume
            volume += bar.volume

        # Update opening range data
        range_data['high'] = high
        range_data['low'] = low if low != float('inf') else 0
        range_data['open'] = open_price
        range_data['close'] = close_price
        range_data['volume'] = volume
        range_data['timestamp'] = datetime.now().isoformat()
        range_data['source'] = 'historical'

        # Calculate range percentage for logging
        range_pct = ((high - low) / open_price) * 100 if open_price > 0 and low != float('inf') else 0

        # Log the result
        logging.info(f"Historical opening range for {symbol}: High={high:.2f}, Low={low:.2f}, Open={open_price:.2f}, Close={close_price:.2f}, Volume={volume}, Range={range_pct:.2f}%")
        log_to_phase(PhaseManager.ORB, f"Historical opening range for {symbol}: High={high:.2f}, Low={low:.2f}, Open={open_price:.2f}, Close={close_price:.2f}, Volume={volume}, Range={range_pct:.2f}%", level=logging.INFO)

        # Return the updated opening range data
        return range_data

    def update_opening_ranges_historical(self):
        """
        Update opening ranges using historical data.
        This method requests historical data for all tracked symbols and updates opening ranges.
        """
        from orbscanner.utils.phase.manager import PhaseManager
        from scanner_main import OPENING_RANGE_MINUTES

        logging.info("Updating opening ranges using historical data")
        log_to_phase(PhaseManager.ORB, "Updating opening ranges using historical data", level=logging.INFO)

        # Calculate duration in seconds (OPENING_RANGE_MINUTES minutes)
        duration_seconds = OPENING_RANGE_MINUTES * 60
        duration_str = f"{duration_seconds} S"  # Use seconds for precision

        # Request historical data
        self._update_opening_ranges_historical(
            symbols=None,  # Use all tracked symbols
            duration_str=duration_str,
            bar_size="1 min",
            use_rth=True
        )

        # Log updated opening ranges
        range_count = len(self.opening_range_data)
        logging.info(f"Updated opening ranges for {range_count} symbols using historical data")
        log_to_phase(PhaseManager.ORB, f"Updated opening ranges for {range_count} symbols using historical data", level=logging.INFO)

        # Set flag to indicate historical data was processed for ORB
        self.historical_data_processed_for_orb = True
        logging.info("Historical data processing for ORB completed successfully")

    def finalize_opening_ranges(self):
        """
        Finalize opening ranges for all tracked symbols.
        This method is called at the end of the opening range building phase.
        It validates opening ranges and filters out invalid ones.
        """
        from orbscanner.utils.phase.manager import PhaseManager
        from scanner_main import ENABLE_FAILSAFE, ENABLE_TEST_CANDIDATE, TEST_CANDIDATE_SYMBOL

        logging.info("Finalizing opening ranges")
        log_to_phase(PhaseManager.FINALIZING, "Finalizing opening ranges", level=logging.INFO)
        
        # DIAGNOSTIC: Log market data subscription status
        market_data_count = len(getattr(self, 'market_data_req_ids', {}))
        tracked_count = len(getattr(self, 'tracked_symbols', {}))
        logging.info(f"MARKET DATA DIAGNOSTIC: {market_data_count} market data subscriptions for {tracked_count} tracked symbols")
        log_to_phase(PhaseManager.FINALIZING, f"MARKET DATA DIAGNOSTIC: {market_data_count} market data subscriptions for {tracked_count} tracked symbols", level=logging.INFO)
        
        # Log symbols with market data but no price updates
        if hasattr(self, 'market_data_req_ids') and hasattr(self, 'tracked_symbols'):
            for symbol in self.market_data_req_ids:
                if symbol in self.tracked_symbols:
                    symbol_data = self.tracked_symbols[symbol]
                    has_price = any(symbol_data.get(field) not in (None, 0) for field in ['last', 'bid', 'ask'])
                    if not has_price:
                        logging.warning(f"MARKET DATA WARNING: Symbol {symbol} has market data subscription but no price data")
                        log_to_phase(PhaseManager.FINALIZING, f"MARKET DATA WARNING: Symbol {symbol} has market data subscription but no price data", level=logging.WARNING)

        # Ensure avg_volumes is initialized
        if not hasattr(self, 'avg_volumes'):
            self.avg_volumes = {}

        # Log current opening range data
        range_count = len(self.opening_range_data)
        logging.info(f"Found {range_count} opening ranges to finalize")
        log_to_phase(PhaseManager.FINALIZING, f"Found {range_count} opening ranges to finalize", level=logging.INFO)

        # Get the final list of candidate symbols only (not all opening_range_data)
        from scanner_main import SKIP_VOLUME_DATA
        from orbscanner.utils.data.avg_volume_calculator import calculate_adv_for_candidates, integrate_adv_with_candidates
        
        # Only calculate ADV if SKIP_VOLUME_DATA is True (since otherwise we already have the data)
        if SKIP_VOLUME_DATA:
            # Get unique symbols from longCandidates and shortCandidates
            final_symbols = set()
            if hasattr(self, 'longCandidates'):
                for candidate in self.longCandidates:
                    if 'symbol' in candidate:
                        final_symbols.add(candidate['symbol'])
            
            if hasattr(self, 'shortCandidates'):
                for candidate in self.shortCandidates:
                    if 'symbol' in candidate:
                        final_symbols.add(candidate['symbol'])
            
            # Calculate ADV for final candidates only
            if final_symbols:
                logging.info(f"Calculating ADV for {len(final_symbols)} final candidates only (skipping full collection)")
                log_to_phase(PhaseManager.FINALIZING, f"Calculating ADV for {len(final_symbols)} final candidates only", level=logging.INFO)
                
                try:
                    adv_data = calculate_adv_for_candidates(self, list(final_symbols), days=30)
                    integrate_adv_with_candidates(self, adv_data)
                except Exception as e:
                    logging.error(f"Error calculating ADV for final candidates: {e}")
                    log_to_phase(PhaseManager.FINALIZING, f"Error calculating ADV for final candidates: {e}", level=logging.ERROR)
        else:
            # Original approach: collect ADV for all symbols
            symbols_to_process = [symbol for symbol in self.opening_range_data.keys()]
            logging.info(f"Pre-caching average volume data for {len(symbols_to_process)} symbols")
            log_to_phase(PhaseManager.FINALIZING, f"Pre-caching average volume data for {len(symbols_to_process)} symbols", level=logging.INFO)
            
            try:
                self.pre_cache_average_volumes(symbols=symbols_to_process, days=30)
                logging.info(f"Successfully pre-cached average volume data for {len(self.avg_volumes)} symbols")
                log_to_phase(PhaseManager.FINALIZING, f"Successfully pre-cached average volume data for {len(self.avg_volumes)} symbols", level=logging.INFO)
            except Exception as e:
                logging.error(f"Error pre-caching average volume data: {e}")
                log_to_phase(PhaseManager.FINALIZING, f"Error pre-caching average volume data: {e}", level=logging.ERROR)

        # Add average volume data to opening_range_data
        symbols_with_adv = 0
        for symbol, data in self.opening_range_data.items():
            # Check if we have average volume data for this symbol
            if hasattr(self, 'avg_volumes') and symbol in self.avg_volumes and self.avg_volumes[symbol] > 0:
                # Add average volume to opening_range_data
                adv = self.avg_volumes[symbol]
                data['avg_volume'] = adv
                symbols_with_adv += 1
                logging.debug(f"Added average volume for {symbol}: {adv:.0f}")

        logging.info(f"Added average volume data to {symbols_with_adv} symbols")
        log_to_phase(PhaseManager.FINALIZING, f"Added average volume data to {symbols_with_adv} symbols", level=logging.INFO)

        # Validate each opening range
        valid_symbols = []
        invalid_symbols = []
        for symbol, range_data in self.opening_range_data.items():
            # Validate the opening range
            is_valid, reason = self.validate_opening_range(symbol, range_data)

            if is_valid:
                valid_symbols.append(symbol)
                logging.info(f"Valid opening range for {symbol}: {range_data}")
                log_to_phase(PhaseManager.FINALIZING, f"Valid opening range for {symbol}: {range_data}", level=logging.INFO)
            else:
                invalid_symbols.append((symbol, reason))
                logging.warning(f"Invalid opening range for {symbol}: {reason}")
                log_to_phase(PhaseManager.FINALIZING, f"Invalid opening range for {symbol}: {reason}", level=logging.WARNING)

        # Log validation results
        valid_count = len(valid_symbols)
        invalid_count = len(invalid_symbols)
        logging.info(f"Found {valid_count} valid and {invalid_count} invalid opening ranges")
        log_to_phase(PhaseManager.FINALIZING, f"Found {valid_count} valid and {invalid_count} invalid opening ranges", level=logging.INFO)

        # Add test candidate if enabled and no valid symbols
        if valid_count == 0 and ENABLE_TEST_CANDIDATE:
            test_symbol = TEST_CANDIDATE_SYMBOL
            logging.warning(f"No valid opening ranges found. Adding test candidate: {test_symbol}")
            log_to_phase(PhaseManager.FINALIZING, f"No valid opening ranges found. Adding test candidate: {test_symbol}", level=logging.WARNING)

            # Create test opening range
            self.opening_range_data[test_symbol] = {
                'high': 150.0,
                'low': 149.0,
                'open': 149.5,
                'close': 149.75,
                'volume': 10000,
                'timestamp': datetime.now().isoformat(),
                'source': 'test'
            }

            # Add to valid symbols
            valid_symbols.append(test_symbol)
            valid_count = 1

            logging.info(f"Added test candidate {test_symbol} with opening range: {self.opening_range_data[test_symbol]}")
            log_to_phase(PhaseManager.FINALIZING, f"Added test candidate {test_symbol} with opening range: {self.opening_range_data[test_symbol]}", level=logging.INFO)

        # Check if we have valid symbols
        if valid_count == 0 and ENABLE_FAILSAFE:
            logging.error("No valid opening ranges found. Exiting.")
            log_to_phase(PhaseManager.FINALIZING, "No valid opening ranges found. Exiting.", level=logging.ERROR)
            # Exit the program
            import sys
            sys.exit(1)

        # Store valid symbols
        self.valid_symbols = valid_symbols

        # Update phase manager with valid symbol count
        if hasattr(self, 'phase_manager') and self.phase_manager:
            self.phase_manager.update_symbol_validation(PhaseManager.FINALIZING, valid_count, valid_count)
            logging.info(f"Updated phase manager with {valid_count} valid symbols")
            log_to_phase(PhaseManager.FINALIZING, f"Updated phase manager with {valid_count} valid symbols", level=logging.INFO)

        # Return valid symbols
        return valid_symbols

    def validate_opening_range(self, symbol, range_data):  # pylint: disable=unused-argument
        """
        Validate an opening range.

        Args:
            symbol (str): Symbol to validate
            range_data (dict): Opening range data

        Returns:
            tuple: (is_valid, reason)
        """
        # Check if we have all required fields
        required_fields = ['high', 'low', 'open']
        for field in required_fields:
            if field not in range_data or range_data[field] is None:
                return False, f"Missing {field}"

        # Check if high and low are valid
        high = range_data['high']
        low = range_data['low']

        if high <= 0 or low <= 0:
            return False, "Invalid high/low values"

        if high < low:
            return False, "High is less than low"

        # Check if range is too narrow
        range_width = high - low
        price_level = (high + low) / 2

        # Calculate minimum range width based on price level
        min_range_width = 0.01  # Default minimum range width

        if price_level < 10:
            min_range_width = 0.02  # 2 cents for stocks under $10
        elif price_level < 50:
            min_range_width = 0.05  # 5 cents for stocks under $50
        elif price_level < 100:
            min_range_width = 0.10  # 10 cents for stocks under $100
        else:
            min_range_width = 0.20  # 20 cents for stocks over $100

        if range_width < min_range_width:
            return False, f"Range too narrow: {range_width:.2f} < {min_range_width:.2f}"

        # Check if range is too wide
        max_range_width = price_level * 0.05  # 5% of price level
        if range_width > max_range_width:
            return False, f"Range too wide: {range_width:.2f} > {max_range_width:.2f}"

        # All checks passed
        return True, "Valid"

    def pre_screen_symbol(self, symbol):
        """
        Pre-screen a symbol for trend data collection.
        This is a wrapper around the orbscanner.analysis.trend_analyzer.pre_screen_symbol function.

        Args:
            symbol (str): Symbol to pre-screen

        Returns:
            bool: True if symbol passes pre-screening, False otherwise
        """
        # Import the function
        from orbscanner.analysis.trend_analyzer import pre_screen_symbol as pre_screen_symbol_func

        # Call the imported function with self as the first argument
        return pre_screen_symbol_func(self, symbol)

    def get_symbol_data(self, symbol):
        """
        Get data for a symbol.

        Args:
            symbol (str): Symbol to get data for

        Returns:
            dict: Symbol data
        """
        # Ensure symbol_data dictionary exists
        if not hasattr(self, 'symbol_data'):
            self.symbol_data = {}

        # Initialize symbol data if needed
        if symbol not in self.symbol_data:
            self.symbol_data[symbol] = {}

        # Return symbol data
        return self.symbol_data[symbol]

    def get_historical_opening_range(self, symbol, market_open_time=None):
        """
        Get historical opening range for a symbol.
        This is a wrapper around the orbscanner.data.historical_data.get_historical_opening_range function.

        Args:
            symbol (str): Symbol to get historical opening range for
            market_open_time (datetime, optional): Market open time. If None, uses 9:30 AM today.

        Returns:
            dict: Historical opening range data
        """
        # Import the function
        from orbscanner.data.historical_data import get_historical_opening_range as get_historical_opening_range_func

        # Call the imported function with self as the first argument
        return get_historical_opening_range_func(self, symbol, market_open_time)

    def _get_historical_opening_range_with_exchange(self, symbol, exchange="SMART"):
        """
        Get historical opening range for a symbol with a specific exchange.
        This is a wrapper around the orbscanner.data.historical_data.get_historical_opening_range_with_exchange function.

        Args:
            symbol (str): Symbol to get historical opening range for
            exchange (str, optional): Exchange to use

        Returns:
            dict: Historical opening range data
        """
        # Call the imported function
        return get_historical_opening_range_with_exchange(self, symbol, exchange)

    def get_daily_trend_data(self, symbol):
        """
        Get daily trend data for a symbol.
        This is a wrapper around the orbscanner.analysis.trend_analyzer.get_daily_trend_data function.

        Args:
            symbol (str): Symbol to get daily trend data for

        Returns:
            dict: Daily trend data
        """
        # Import the function
        from orbscanner.analysis.trend_analyzer import get_daily_trend_data as get_daily_trend_data_func

        # Call the imported function with self as the first argument
        return get_daily_trend_data_func(self, symbol)

    def pre_cache_average_volumes(self, symbols=None, days=30):
        """
        Pre-cache average volume data for a list of symbols.
        This method requests historical daily data for each symbol and calculates average volume.

        Args:
            symbols (list, optional): List of symbols to pre-cache. If None, uses all tracked symbols.
            days (int, optional): Number of days to use for average volume calculation.

        Returns:
            dict: Dictionary of average volumes by symbol
        """
        from orbscanner.utils.phase.manager import PhaseManager

        # Use all tracked symbols if none provided
        if symbols is None:
            symbols = list(self.tracked_symbols.keys())

        # Log the request
        symbol_count = len(symbols)
        logging.info(f"Pre-caching average volume data for {symbol_count} symbols")
        log_to_phase(PhaseManager.PRE_MARKET, f"Pre-caching average volume data for {symbol_count} symbols", level=logging.INFO)

        # Initialize avg_volumes dictionary if needed
        if not hasattr(self, 'avg_volumes'):
            self.avg_volumes = {}

        # Initialize historical_data_errors dictionary if needed
        if not hasattr(self, 'historical_data_errors'):
            self.historical_data_errors = {}

        # Check if we should load from cache
        cache_loaded = False
        if hasattr(self, 'args') and hasattr(self.args, 'use_volume_cache') and self.args.use_volume_cache:
            # Try to load from cache
            cache_loaded = self._load_cached_volumes()

            if cache_loaded:
                logging.info(f"Loaded average volume data from cache for {len(self.avg_volumes)} symbols")
                log_to_phase(PhaseManager.PRE_MARKET, f"Loaded average volume data from cache for {len(self.avg_volumes)} symbols", level=logging.INFO)

                # Check if cache is stale
                if self._is_cache_stale():
                    logging.warning("Volume cache is stale, refreshing...")
                    log_to_phase(PhaseManager.PRE_MARKET, "Volume cache is stale, refreshing...", level=logging.WARNING)
                    cache_loaded = False
                else:
                    # Return the loaded cache
                    return self.avg_volumes

        # If cache not loaded or stale, request historical data
        if not cache_loaded:
            # Request historical data for each symbol
            self._pre_cache_from_historical_data(symbols, days)

            # Save to cache if enabled
            if hasattr(self, 'args') and hasattr(self.args, 'use_volume_cache') and self.args.use_volume_cache:
                self._save_cached_volumes()

        # Return the avg_volumes dictionary
        return self.avg_volumes

    def _pre_cache_from_historical_data(self, symbols, days=30):
        """
        Request historical daily data for a list of symbols and calculate average volume.
        This is an internal method called by pre_cache_average_volumes.

        Args:
            symbols (list): List of symbols to request data for
            days (int, optional): Number of days to request

        Returns:
            dict: Dictionary of average volumes by symbol
        """
        from orbscanner.utils.phase.manager import PhaseManager
        from scanner_main import HISTORICAL_DATA_TIMEOUT

        # Log the request
        symbol_count = len(symbols)
        logging.info(f"Requesting historical daily data for {symbol_count} symbols")
        log_to_phase(PhaseManager.PRE_MARKET, f"Requesting historical daily data for {symbol_count} symbols", level=logging.INFO)

        # Initialize daily_data_buffer dictionary if needed
        if not hasattr(self, 'daily_data_buffer'):
            self.daily_data_buffer = {}

        # Initialize daily_data_events dictionary if needed
        if not hasattr(self, 'daily_data_events'):
            self.daily_data_events = {}

        # Initialize daily_data_finished dictionary if needed
        if not hasattr(self, 'daily_data_finished'):
            self.daily_data_finished = {}

        # Create events for each symbol
        events = {}
        for symbol in symbols:
            events[symbol] = threading.Event()

        # Request historical data for each symbol
        for symbol in symbols:
            # Create contract
            contract = self.create_standard_contract(symbol)
            if not contract:
                logging.error(f"Failed to create contract for {symbol}")
                log_to_phase(PhaseManager.PRE_MARKET, f"Failed to create contract for {symbol}", level=logging.ERROR)
                continue

            # Generate request ID
            req_id = self.get_next_req_id()

            # Store mapping of request ID to symbol
            self.reqId_to_symbol[req_id] = symbol

            # Initialize buffer for this request
            self.daily_data_buffer[req_id] = []
            self.daily_data_finished[req_id] = False

            # Store event for this request
            self.daily_data_events[req_id] = events[symbol]

            # Get end time (now)
            end_time = datetime.now().strftime("%Y%m%d %H:%M:%S")

            # Request historical data
            logging.info(f"Requesting historical daily data for {symbol} (reqId: {req_id})")
            log_to_phase(PhaseManager.PRE_MARKET, f"Requesting historical daily data for {symbol} (reqId: {req_id})", level=logging.INFO)

            try:
                self.reqHistoricalData(
                    req_id,
                    contract,
                    end_time,
                    f"{days} D",  # Duration string
                    "1 day",      # Bar size
                    "TRADES",     # What to show
                    1,            # Use regular trading hours
                    1,            # Date format (1 = yyyymmdd)
                    False,        # Keep up to date
                    []            # Chart options
                )
            except Exception as e:
                logging.error(f"Error requesting historical daily data for {symbol}: {e}")
                log_to_phase(PhaseManager.PRE_MARKET, f"Error requesting historical daily data for {symbol}: {e}", level=logging.ERROR)
                continue

        # Wait for all requests to complete
        timeout = HISTORICAL_DATA_TIMEOUT  # seconds
        start_time = time.time()
        for symbol, event in events.items():
            remaining_time = timeout - (time.time() - start_time)
            if remaining_time <= 0:
                logging.warning(f"Timeout waiting for historical daily data")
                log_to_phase(PhaseManager.PRE_MARKET, f"Timeout waiting for historical daily data", level=logging.WARNING)
                break

            if not event.wait(remaining_time):
                logging.warning(f"Timeout waiting for historical daily data for {symbol}")
                log_to_phase(PhaseManager.PRE_MARKET, f"Timeout waiting for historical daily data for {symbol}", level=logging.WARNING)

        # Process historical data for each symbol
        for req_id, buffer in self.daily_data_buffer.items():
            if req_id in self.reqId_to_symbol:
                symbol = self.reqId_to_symbol[req_id]

                # Check if we have data
                if buffer:
                    # Process historical volume data
                    self._process_historical_volume_data(symbol, buffer, days)
                else:
                    logging.warning(f"No historical daily data received for {symbol}")
                    log_to_phase(PhaseManager.PRE_MARKET, f"No historical daily data received for {symbol}", level=logging.WARNING)

        # Log results
        volume_count = len(self.avg_volumes)
        logging.info(f"Calculated average volume for {volume_count} symbols")
        log_to_phase(PhaseManager.PRE_MARKET, f"Calculated average volume for {volume_count} symbols", level=logging.INFO)

        # Return the avg_volumes dictionary
        return self.avg_volumes

    def _process_historical_volume_data(self, symbol, bars, days=30):
        """
        Process historical daily data bars to calculate average volume for a symbol.

        Args:
            symbol (str): Symbol to process
            bars (list): List of BarData objects from historical data request
            days (int, optional): Number of days to use for average calculation

        Returns:
            float: Average volume for the symbol
        """
        from orbscanner.utils.phase.manager import PhaseManager

        # Log the processing
        logging.info(f"Processing historical volume data for {symbol} with {len(bars)} bars")
        log_to_phase(PhaseManager.PRE_MARKET, f"Processing historical volume data for {symbol} with {len(bars)} bars", level=logging.INFO)

        # Validate data quality
        is_valid, reason = self._validate_historical_data_quality(symbol, bars)
        if not is_valid:
            logging.warning(f"Invalid historical volume data for {symbol}: {reason}")
            log_to_phase(PhaseManager.PRE_MARKET, f"Invalid historical volume data for {symbol}: {reason}", level=logging.WARNING)
            return 0.0

        # Sort bars by date (newest first)
        sorted_bars = sorted(bars, key=lambda bar: bar.date, reverse=True)

        # Limit to requested number of days
        if len(sorted_bars) > days:
            sorted_bars = sorted_bars[:days]

        # Calculate average volume
        total_volume = sum(bar.volume for bar in sorted_bars)
        avg_volume = total_volume / len(sorted_bars) if sorted_bars else 0.0

        # Store in avg_volumes dictionary
        self.avg_volumes[symbol] = avg_volume

        # Log the result
        logging.info(f"Average volume for {symbol}: {avg_volume:.0f} over {len(sorted_bars)} days")
        log_to_phase(PhaseManager.PRE_MARKET, f"Average volume for {symbol}: {avg_volume:.0f} over {len(sorted_bars)} days", level=logging.INFO)

        # Return the average volume
        return avg_volume

    def _validate_historical_data_quality(self, symbol, bars):  # pylint: disable=unused-argument
        """
        Validate the quality of historical data bars.

        Args:
            symbol (str): Symbol to validate
            bars (list): List of BarData objects from historical data request

        Returns:
            tuple: (is_valid, reason)
        """
        # Check if we have any bars
        if not bars:
            return False, "No data bars received"

        # Check if we have enough bars
        if len(bars) < 5:
            return False, f"Insufficient data: {len(bars)} bars (minimum 5 required)"

        # Check for zero volume bars
        zero_volume_bars = [bar for bar in bars if bar.volume == 0]
        if len(zero_volume_bars) > len(bars) * 0.5:
            return False, f"Too many zero volume bars: {len(zero_volume_bars)}/{len(bars)}"

        # Check for invalid prices
        invalid_price_bars = [bar for bar in bars if bar.close <= 0 or bar.high <= 0 or bar.low <= 0]
        if invalid_price_bars:
            return False, f"Invalid price bars: {len(invalid_price_bars)}/{len(bars)}"

        # All checks passed
        return True, "Valid"

    def _save_cached_volumes(self):
        """
        Save average volumes to cache file.

        Returns:
            bool: True if successful, False otherwise
        """
        import json
        import os

        # Check if we have any volumes to save
        if not hasattr(self, 'avg_volumes') or not self.avg_volumes:
            logging.warning("No average volumes to save to cache")
            return False

        # Create cache directory if it doesn't exist
        cache_dir = os.path.join(os.path.expanduser("~"), ".orbscanner", "cache")
        os.makedirs(cache_dir, exist_ok=True)

        # Create cache file path
        cache_file = os.path.join(cache_dir, "avg_volumes.json")

        try:
            # Create cache data
            cache_data = {
                "timestamp": datetime.now().isoformat(),
                "volumes": self.avg_volumes
            }

            # Save to file
            with open(cache_file, 'w') as f:
                json.dump(cache_data, f)

            logging.info(f"Saved {len(self.avg_volumes)} average volumes to cache: {cache_file}")
            return True
        except Exception as e:
            logging.error(f"Error saving average volumes to cache: {e}")
            return False

    def _load_cached_volumes(self):
        """
        Load average volumes from cache file.

        Returns:
            bool: True if successful, False otherwise
        """
        import json
        import os

        # Create cache file path
        cache_dir = os.path.join(os.path.expanduser("~"), ".orbscanner", "cache")
        cache_file = os.path.join(cache_dir, "avg_volumes.json")

        # Check if cache file exists
        if not os.path.exists(cache_file):
            logging.info("Average volume cache file not found")
            return False

        try:
            # Load from file
            with open(cache_file, 'r') as f:
                cache_data = json.load(f)

            # Check if cache data is valid
            if "timestamp" not in cache_data or "volumes" not in cache_data:
                logging.warning("Invalid average volume cache data")
                return False

            # Store in avg_volumes dictionary
            self.avg_volumes = cache_data["volumes"]

            logging.info(f"Loaded {len(self.avg_volumes)} average volumes from cache: {cache_file}")
            return True
        except Exception as e:
            logging.error(f"Error loading average volumes from cache: {e}")
            return False

    def _is_cache_stale(self, max_age_hours=24):
        """
        Check if the volume cache is stale.

        Args:
            max_age_hours (int, optional): Maximum age of cache in hours

        Returns:
            bool: True if cache is stale, False otherwise
        """
        import json
        import os
        from datetime import datetime, timedelta

        # Create cache file path
        cache_dir = os.path.join(os.path.expanduser("~"), ".orbscanner", "cache")
        cache_file = os.path.join(cache_dir, "avg_volumes.json")

        # Check if cache file exists
        if not os.path.exists(cache_file):
            return True

        try:
            # Load from file
            with open(cache_file, 'r') as f:
                cache_data = json.load(f)

            # Check if cache data is valid
            if "timestamp" not in cache_data:
                return True

            # Parse timestamp
            cache_time = datetime.fromisoformat(cache_data["timestamp"])

            # Check if cache is stale
            max_age = timedelta(hours=max_age_hours)
            is_stale = datetime.now() - cache_time > max_age

            if is_stale:
                logging.info(f"Volume cache is stale: {cache_time.isoformat()} (max age: {max_age_hours} hours)")
            else:
                logging.info(f"Volume cache is fresh: {cache_time.isoformat()} (max age: {max_age_hours} hours)")

            return is_stale
        except Exception as e:
            logging.error(f"Error checking if volume cache is stale: {e}")
            return True

    def get_average_volume_from_historical_data(self, symbol, days=30):
        """
        Get average volume for a symbol from historical data.

        Args:
            symbol (str): Symbol to get average volume for
            days (int, optional): Number of days to use for average calculation

        Returns:
            float: Average volume for the symbol
        """
        # Import here to avoid circular imports
        from scanner_main import HISTORICAL_DATA_TIMEOUT

        # Check if we already have average volume for this symbol
        if hasattr(self, 'avg_volumes') and symbol in self.avg_volumes:
            avg_volume = self.avg_volumes[symbol]
            logging.info(f"Using cached average volume for {symbol}: {avg_volume:.0f}")
            return avg_volume

        # Log the request
        logging.info(f"Requesting historical daily data for {symbol} to calculate average volume")

        # Create contract
        contract = self.create_standard_contract(symbol)
        if not contract:
            logging.error(f"Failed to create contract for {symbol}")
            return 0.0

        # Generate request ID
        req_id = self.get_next_req_id()

        # Store mapping of request ID to symbol
        self.reqId_to_symbol[req_id] = symbol

        # Initialize buffer for this request
        if not hasattr(self, 'daily_data_buffer'):
            self.daily_data_buffer = {}
        self.daily_data_buffer[req_id] = []

        # Initialize finished flag for this request
        if not hasattr(self, 'daily_data_finished'):
            self.daily_data_finished = {}
        self.daily_data_finished[req_id] = False

        # Create event for this request
        if not hasattr(self, 'daily_data_events'):
            self.daily_data_events = {}
        self.daily_data_events[req_id] = threading.Event()

        # Get end time (now)
        end_time = datetime.now().strftime("%Y%m%d %H:%M:%S")

        # Request historical data
        logging.info(f"Requesting historical daily data for {symbol} (reqId: {req_id})")

        try:
            self.reqHistoricalData(
                req_id,
                contract,
                end_time,
                f"{days} D",  # Duration string
                "1 day",      # Bar size
                "TRADES",     # What to show
                1,            # Use regular trading hours
                1,            # Date format (1 = yyyymmdd)
                False,        # Keep up to date
                []            # Chart options
            )
        except Exception as e:
            logging.error(f"Error requesting historical daily data for {symbol}: {e}")
            return 0.0

        # Wait for request to complete
        timeout = HISTORICAL_DATA_TIMEOUT  # seconds
        if not self.daily_data_events[req_id].wait(timeout):
            logging.warning(f"Timeout waiting for historical daily data for {symbol}")
            return 0.0

        # Check if we have data
        if req_id not in self.daily_data_buffer or not self.daily_data_buffer[req_id]:
            logging.warning(f"No historical daily data received for {symbol}")
            return 0.0

        # Process historical volume data
        buffer = self.daily_data_buffer[req_id]
        avg_volume = self._process_historical_volume_data(symbol, buffer, days)

        # Return the average volume
        return avg_volume

    def analyze_historical_data_errors(self):
        """
        Analyze historical data errors and provide recommendations.

        This method analyzes the patterns in historical data errors and provides
        recommendations for improving data retrieval success rates.

        Returns:
            dict: Dictionary with analysis results and recommendations
        """

        # Check if we have historical data errors
        if not hasattr(self, 'historical_data_errors') or not self.historical_data_errors:
            logging.info("No historical data errors to analyze")
            return {
                'error_count': 0,
                'symbols_with_errors': 0,
                'recommendations': ['No errors to analyze']
            }

        # Count errors by type
        error_types = {}
        symbols_with_errors = set()
        exchange_errors = {
            'SMART': 0,
            'ARCA': 0,
            'NYSE': 0,
            'NASDAQ': 0
        }

        for symbol, errors in self.historical_data_errors.items():
            symbols_with_errors.add(symbol)

            for error in errors:
                error_type = error.get('error_type', 'Unknown')
                error_types[error_type] = error_types.get(error_type, 0) + 1

                # Count errors by exchange
                exchange = error.get('exchange')
                if exchange and exchange in exchange_errors:
                    exchange_errors[exchange] += 1

        # Calculate exchange success rates
        exchange_success_rates = {}
        best_exchange = None
        best_rate = -1

        if hasattr(self, 'exchange_success_rates'):
            for exchange, stats in self.exchange_success_rates.items():
                success = stats['success']
                total = success + stats['failure']
                rate = (success / total) * 100 if total > 0 else 0
                exchange_success_rates[exchange] = rate

                if rate > best_rate:
                    best_rate = rate
                    best_exchange = exchange

        # Generate recommendations
        recommendations = []

        # Add general recommendations
        recommendations.append(f"Total errors: {sum(error_types.values())} across {len(symbols_with_errors)} symbols")

        if 'TIMEOUT' in error_types and error_types['TIMEOUT'] > 0:
            recommendations.append(f"TIMEOUT errors ({error_types['TIMEOUT']}): Consider increasing HISTORICAL_DATA_TIMEOUT")

        if 'HMDS_ERROR' in error_types and error_types['HMDS_ERROR'] > 0:
            recommendations.append(f"HMDS errors ({error_types['HMDS_ERROR']}): Check market data permissions and subscription level")

        if 'DATA_QUALITY' in error_types and error_types['DATA_QUALITY'] > 0:
            recommendations.append(f"DATA_QUALITY issues ({error_types['DATA_QUALITY']}): Some symbols have low liquidity or trading volume")

        if 'EMPTY_RESPONSE' in error_types and error_types['EMPTY_RESPONSE'] > 0:
            recommendations.append(f"EMPTY_RESPONSE errors ({error_types['EMPTY_RESPONSE']}): No data available for requested time period")

        # Add exchange-specific recommendations
        if best_exchange and best_rate > 50:
            recommendations.append(f"Best performing exchange: {best_exchange} ({best_rate:.1f}% success rate)")
            recommendations.append(f"Consider using {best_exchange} as the primary exchange for historical data requests")

        # Check if SMART routing is performing poorly
        if 'SMART' in exchange_success_rates and exchange_success_rates['SMART'] < 50:
            recommendations.append(f"SMART routing has a low success rate ({exchange_success_rates['SMART']:.1f}%)")
            recommendations.append("Consider using direct exchange routing instead of SMART for historical data")

        # Log the analysis
        logging.info("=" * 80)
        logging.info("HISTORICAL DATA ERROR ANALYSIS")
        logging.info(f"Total errors: {sum(error_types.values())} across {len(symbols_with_errors)} symbols")
        logging.info(f"Error types: {error_types}")
        logging.info(f"Exchange errors: {exchange_errors}")
        logging.info(f"Exchange success rates: {exchange_success_rates}")
        logging.info("Recommendations:")
        for rec in recommendations:
            logging.info(f"  - {rec}")
        logging.info("=" * 80)

        return {
            'error_count': sum(error_types.values()),
            'symbols_with_errors': len(symbols_with_errors),
            'error_types': error_types,
            'exchange_errors': exchange_errors,
            'exchange_success_rates': exchange_success_rates,
            'recommendations': recommendations
        }

    def historicalData(self, reqId, bar):
        """
        Callback for historical data.

        Args:
            reqId (int): Request ID
            bar (BarData): Bar data
        """
        # Add to buffer
        if reqId in self.historical_data_buffer:
            self.historical_data_buffer[reqId].append(bar)

        # Log the bar
        logging.debug(f"Historical data for reqId {reqId}: {bar.date} - O:{bar.open}, H:{bar.high}, L:{bar.low}, C:{bar.close}, V:{bar.volume}")

    def historicalDataEnd(self, reqId, start, end):
        """
        Callback for end of historical data.

        Args:
            reqId (int): Request ID
            start (str): Start date
            end (str): End date
        """
        # Mark as finished
        self.historical_data_finished[reqId] = True

        # Log completion
        logging.info(f"Historical data request {reqId} completed: {start} to {end}")

        # Get symbol for this request ID
        symbol = self.reqId_to_symbol.get(reqId, f"Unknown-{reqId}")

        # Get buffer for this request
        buffer = self.historical_data_buffer.get(reqId, [])

        # Log bar count
        bar_count = len(buffer)
        logging.info(f"Received {bar_count} bars for {symbol}")

        # Signal completion
        if reqId in self.historical_data_events:
            self.historical_data_events[reqId].set()

    def contractDetails(self, reqId, contractDetails):
        """
        Callback for contract details.

        Args:
            reqId (int): Request ID
            contractDetails (ContractDetails): Contract details
        """
        # Get symbol for this request ID
        symbol = None
        if hasattr(self, 'contract_details_req_ids'):
            for sym, rid in self.contract_details_req_ids.items():
                if rid == reqId:
                    symbol = sym
                    break

        if not symbol and reqId in self.reqId_to_symbol:
            symbol = self.reqId_to_symbol[reqId]

        if not symbol:
            logging.warning(f"Received contract details for unknown reqId: {reqId}")
            return

        # Store contract details
        if not hasattr(self, 'contract_details'):
            self.contract_details = {}

        if symbol not in self.contract_details:
            self.contract_details[symbol] = []

        self.contract_details[symbol].append(contractDetails)

        # Log the details
        logging.info(f"Contract details for {symbol}: {contractDetails.contract.symbol} {contractDetails.contract.secType} {contractDetails.contract.exchange}")

    def contractDetailsEnd(self, reqId):
        """
        Callback for end of contract details.

        Args:
            reqId (int): Request ID
        """
        # Mark as received
        if hasattr(self, 'contract_details_received'):
            self.contract_details_received[reqId] = True

        # Get symbol for this request ID
        symbol = None
        if hasattr(self, 'contract_details_req_ids'):
            for sym, rid in self.contract_details_req_ids.items():
                if rid == reqId:
                    symbol = sym
                    break

        if not symbol and reqId in self.reqId_to_symbol:
            symbol = self.reqId_to_symbol[reqId]

        # Log completion
        if symbol:
            detail_count = len(self.contract_details.get(symbol, []))
            logging.info(f"Contract details request for {symbol} completed with {detail_count} results")
        else:
            logging.info(f"Contract details request {reqId} completed")

        # Signal completion
        if hasattr(self, 'contract_details_events') and reqId in self.contract_details_events:
            self.contract_details_events[reqId].set()
