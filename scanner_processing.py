#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Scanner Processing - Scanner subscription and processing for the ORB Scanner.

This module contains methods specifically related to scanner subscriptions, callbacks,
and processing scanner results, including priority scoring.
"""

import logging
import time
import traceback
import threading
from datetime import datetime, timedelta
import pytz

from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.scanner import ScannerSubscription
from ibapi.tag_value import TagValue
from ibapi.contract import Contract

# Import our modules from the orbscanner package
from orbscanner.utils.phase.manager import PhaseManager
from orbscanner.utils.phase.logging import log_to_phase
from orbscanner.utils.time.timezone import get_eastern_time

# Base class for IBApp that handles scanner processing
class IBAppScannerProcessing(EWrapper, EClient):
    def __init__(self):
        """
        Initialize the IBAppScannerProcessing instance.
        This method initializes attributes related to scanner processing.
        """
        # Initialize attributes that might be accessed before they're defined
        self.phase_manager = None
        self.scannerRequestIds = {}
        self.vix_value = None
        self.reqId = 0
        
        # Scanner-related attributes that might be accessed
        self.scannerResults = {"UNKNOWN": []}
        self.scannerFinished = {}
        self.allScannersFinished = False
        self.active_scanner_subscriptions = False
        self.combinedResults = []
        
        # Account information storage
        self.account_values = {}
        self.account_time = None

    def get_scanner_types(self, vix_level=None):
        """
        Get scanner types based on VIX level.
        This is a wrapper around the orbscanner.scanners.scanner_factory.get_scanner_types function.

        Args:
            vix_level (float, optional): Current VIX level

        Returns:
            list: List of scanner types to run
        """
        # Get the current phase if available
        phase = None
        if hasattr(self, 'phase_manager') and self.phase_manager:
            phase = self.phase_manager.current_phase

        # Import the function
        from orbscanner.scanners.scanner_factory import get_scanner_types as get_scanner_types_func

        # Call the imported function with self as the first argument
        return get_scanner_types_func(self, vix_level, phase)

    def get_scanner_filters(self, vix_level=None):
        """
        Get scanner filters based on VIX level.
        This is a wrapper around the orbscanner.scanners.scanner_factory.get_scanner_filters function.

        Args:
            vix_level (float, optional): Current VIX level

        Returns:
            dict: Dictionary of scanner filters
        """
        # Import the function
        from orbscanner.scanners.scanner_factory import get_scanner_filters as get_scanner_filters_func

        # Call the imported function with self as the first argument
        return get_scanner_filters_func(self, vix_level)

    def get_scanner_filter_options(self, vix_level=None, scanner_type=None):
        """
        Get scanner filter options based on VIX level and scanner type.
        This is a wrapper around the orbscanner.scanners.scanner_factory.get_scanner_filter_options function.

        Args:
            vix_level (float, optional): Current VIX level
            scanner_type (str, optional): Type of scanner to get filters for

        Returns:
            list: List of TagValue objects for scanner filter options
        """
        # Import the function
        from orbscanner.scanners.scanner_factory import get_scanner_filter_options as get_scanner_filter_options_func

        # Call the imported function with self as the first argument
        return get_scanner_filter_options_func(self, vix_level, scanner_type)

    def start_scanners(self, vix_level=None, is_continuous=False):
        """
        Start multiple scanner subscriptions based on VIX level.
        This is a wrapper around the orbscanner.scanners.scanner_factory.start_scanners function.

        Args:
            vix_level (float, optional): Current VIX level
            is_continuous (bool): Whether this is part of continuous scanning
        """
        # Get the current phase if available
        phase = None
        if hasattr(self, 'phase_manager') and self.phase_manager:
            phase = self.phase_manager.current_phase

        # Import the function
        from orbscanner.scanners.scanner_factory import start_scanners as start_scanners_func

        # Call the imported function with self as the first argument
        start_scanners_func(self, vix_level, is_continuous, phase)

    def stop_scanners(self):
        """
        Stop all active scanner subscriptions.
        This is a wrapper around the orbscanner.scanners.scanner_factory.stop_scanners function.
        """
        # Import the function
        from orbscanner.scanners.scanner_factory import stop_scanners as stop_scanners_func

        # Call the imported function with self as the first argument
        stop_scanners_func(self)

    def scannerParameters(self, xml):
        """
        Callback for scanner parameters.

        Args:
            xml (str): XML string containing scanner parameters
        """
        logging.info("Received scanner parameters")
        # Save to file for debugging if needed
        try:
            with open("scanner_parameters.xml", "w") as f:
                f.write(xml)
            logging.info("Saved scanner parameters to scanner_parameters.xml")
        except Exception as e:
            logging.error(f"Failed to save scanner parameters: {e}")

    def scannerData(self, reqId, rank, contractDetails, distance, benchmark, projection, legsStr):
        """
        Callback for scanner data.

        Args:
            reqId (int): Request ID
            rank (int): Rank of the result
            contractDetails (ContractDetails): Contract details
            distance (str): Distance value
            benchmark (str): Benchmark value
            projection (str): Projection value
            legsStr (str): Legs string
        """
        # Get scanner type from request ID
        scanner_type = "UNKNOWN"
        for stype, rid in self.scannerRequestIds.items():
            if rid == reqId:
                scanner_type = stype
                break

        # Create result dictionary
        result = {
            "symbol": contractDetails.contract.symbol,
            "rank": rank + 1,  # Convert to 1-based rank
            "distance": distance,
            "benchmark": benchmark,
            "projection": projection,
            "scanner_type": scanner_type,
            "timestamp": datetime.now().isoformat()
        }

        # Add to scanner results
        if scanner_type not in self.scannerResults:
            self.scannerResults[scanner_type] = []
        self.scannerResults[scanner_type].append(result)

        # Log the result
        logging.info(f"Scanner result: {scanner_type} - Rank {rank+1}: {contractDetails.contract.symbol}")

    def scannerDataEnd(self, reqId):
        """
        Callback for end of scanner data.

        Args:
            reqId (int): Request ID
        """
        # Mark this scanner as finished
        self.scannerFinished[reqId] = True

        # Get scanner type from request ID
        scanner_type = "UNKNOWN"
        for stype, rid in self.scannerRequestIds.items():
            if rid == reqId:
                scanner_type = stype
                break

        # Log completion
        result_count = len(self.scannerResults.get(scanner_type, []))
        logging.info(f"Scanner {scanner_type} (reqId: {reqId}) completed with {result_count} results")

        # Check if all scanners are finished
        all_finished = all(self.scannerFinished.get(rid, False) for rid in self.scannerRequestIds.values())
        if all_finished:
            self.allScannersFinished = True
            logging.info("All scanners completed")

            # Process scanner results
            self.process_scanner_results()

    def process_scanner_results(self):
        """
        Process scanner results to generate combined results and candidates.
        This is a wrapper around the orbscanner.scanners.result_processor.process_scanner_results function.
        """
        # Import the function
        from orbscanner.scanners.result_processor import process_scanner_results as process_scanner_results_func

        # Call the imported function with self as the first argument
        combined_results = process_scanner_results_func(self)

        # Handle the case where combined_results is None
        if combined_results is None:
            logging.warning("Scanner result processing returned None - possibly due to missing historical data")
            # Ensure we have a valid list for combinedResults
            combined_results = []
            
        # Store the combined results
        self.combinedResults = combined_results

        # Log the combined results
        logging.info(f"Processed {len(combined_results)} combined scanner results")

        # Return the combined results
        return combined_results

    def calculate_priority_score(self, symbol_data):
        """
        Calculate priority score for a symbol.
        This is a wrapper around the orbscanner.scanners.result_processor.calculate_priority_score function.

        Args:
            symbol_data (dict): Symbol data dictionary

        Returns:
            float: Priority score
        """
        # Import the function
        from orbscanner.scanners.result_processor import calculate_priority_score as calculate_priority_score_func

        # Call the imported function with self as the first argument
        return calculate_priority_score_func(self, symbol_data)

    def run_pre_market_phase(self):
        """
        Run the pre-market phase to pre-cache average volumes for top candidates.
        This phase runs before market open to prepare for the opening range building phase.
        """
        from orbscanner.utils.phase.manager import PhaseManager

        logging.info("Starting pre-market phase")
        log_to_phase(PhaseManager.PRE_MARKET, "Starting pre-market phase", level=logging.INFO)

        # Get VIX value for scanner configuration
        vix_value = getattr(self, 'vix_value', None)
        if vix_value:
            logging.info(f"Using VIX level: {vix_value:.2f} for pre-market scanners")
            log_to_phase(PhaseManager.PRE_MARKET, f"Using VIX level: {vix_value:.2f} for pre-market scanners", level=logging.INFO)
        else:
            logging.warning("No VIX level available, using default scanner configuration")
            log_to_phase(PhaseManager.PRE_MARKET, "No VIX level available, using default scanner configuration", level=logging.WARNING)

        # Run scanners to get pre-market candidates
        logging.info("Running pre-market scanners")
        log_to_phase(PhaseManager.PRE_MARKET, "Running pre-market scanners", level=logging.INFO)

        # Reset scanner results
        self.scannerResults = {"UNKNOWN": []}
        self.scannerFinished = {}
        self.allScannersFinished = False

        # Start scanners with pre-market phase
        self.start_scanners(vix_value, is_continuous=False)

        # Wait for scanner results
        scanner_timeout = 30  # seconds
        scanner_start_time = time.time()
        while not self.allScannersFinished and time.time() - scanner_start_time < scanner_timeout:
            time.sleep(0.5)

        # Check if scanners completed
        if not self.allScannersFinished:
            logging.warning(f"Pre-market scanners did not complete within {scanner_timeout} seconds")
            log_to_phase(PhaseManager.PRE_MARKET, f"Pre-market scanners did not complete within {scanner_timeout} seconds", level=logging.WARNING)
        else:
            logging.info("Pre-market scanners completed successfully")
            log_to_phase(PhaseManager.PRE_MARKET, "Pre-market scanners completed successfully", level=logging.INFO)

        # Select pre-market candidates
        self.select_pre_market_candidates()

        # Log completion
        logging.info("Pre-market phase completed")
        log_to_phase(PhaseManager.PRE_MARKET, "Pre-market phase completed", level=logging.INFO)

    def select_pre_market_candidates(self):
        """
        Select pre-market candidates from scanner results.
        These candidates will be used for pre-caching average volumes.
        """
        from orbscanner.utils.phase.manager import PhaseManager

        logging.info("Selecting pre-market candidates")
        log_to_phase(PhaseManager.PRE_MARKET, "Selecting pre-market candidates", level=logging.INFO)

        # Process scanner results if not already processed
        if not self.combinedResults:
            self.process_scanner_results()

        # Get combined results
        combined_results = self.combinedResults

        # Sort by rank (lower is better)
        sorted_results = sorted(combined_results, key=lambda x: x.get('rank', 999))

        # Take top candidates up to MAX_PRE_MARKET_CANDIDATES
        from scanner_main import MAX_PRE_MARKET_CANDIDATES
        candidates = sorted_results[:MAX_PRE_MARKET_CANDIDATES]

        # Extract symbols
        candidate_symbols = [c.get('symbol') for c in candidates]

        # Store pre-market candidates
        self.pre_market_candidates = candidate_symbols

        # Log selected candidates
        logging.info(f"Selected {len(candidate_symbols)} pre-market candidates")
        log_to_phase(PhaseManager.PRE_MARKET, f"Selected {len(candidate_symbols)} pre-market candidates", level=logging.INFO)
        logging.info(f"Pre-market candidates: {', '.join(candidate_symbols[:10])}{'...' if len(candidate_symbols) > 10 else ''}")
        log_to_phase(PhaseManager.PRE_MARKET, f"Pre-market candidates: {', '.join(candidate_symbols[:10])}{'...' if len(candidate_symbols) > 10 else ''}", level=logging.INFO)

    def get_vix_value(self):
        """
        Get the current VIX value.
        This method requests VIX data from IB and waits for the response.

        Returns:
            float: Current VIX value, or None if not available
        """
        # Create VIX contract
        contract = Contract()
        contract.symbol = "VIX"
        contract.secType = "IND"
        contract.exchange = "CBOE"
        contract.currency = "USD"

        # Generate request ID
        from scanner_main import id_gen, VIX_TIMEOUT
        self.vix_req_id = id_gen.next()

        # Reset event
        self.vix_received_event = threading.Event()

        # Request market data
        logging.info(f"Requesting VIX data (reqId: {self.vix_req_id})")
        self.reqMktData(self.vix_req_id, contract, "", False, False, [])

        # Wait for data
        vix_timeout = VIX_TIMEOUT  # seconds
        if not self.vix_received_event.wait(vix_timeout):
            logging.warning(f"VIX data not received within {vix_timeout} seconds")
            # Cancel the request
            self.cancelMktData(self.vix_req_id)
            return None

        # Return VIX value
        return self.vix_value

    def nextValidId(self, orderId):
        """
        Callback for next valid order ID.

        Args:
            orderId (int): Next valid order ID
        """
        self.nextOrderId = orderId
        logging.info(f"Next valid order ID: {orderId}")

    def updateAccountValue(self, key, val, currency, accountName):
        """
        Callback for account value updates.

        Args:
            key (str): Account value key
            val (str): Account value
            currency (str): Currency
            accountName (str): Account name
        """
        logging.debug(f"Account update: {key}={val} {currency} ({accountName})")

        # Store account values if needed
        if not hasattr(self, 'account_values'):
            self.account_values = {}
        if accountName not in self.account_values:
            self.account_values[accountName] = {}
        self.account_values[accountName][key] = {
            'value': val,
            'currency': currency,
            'timestamp': datetime.now().isoformat()
        }

    def accountDownloadEnd(self, accountName):
        """
        Callback for end of account download.

        Args:
            accountName (str): Account name
        """
        logging.info(f"Account download completed for {accountName}")

    def get_next_req_id(self):
        """
        Get the next request ID.

        Returns:
            int: Next request ID
        """
        self.reqId += 1
        return self.reqId

    def cancel_request_safely(self, req_id, request_type="unknown"):
        """
        Cancel a request safely, handling any exceptions.

        Args:
            req_id (int): Request ID to cancel
            request_type (str): Type of request (for logging)
        """
        try:
            if request_type == "market_data":
                logging.info(f"Canceling market data request {req_id}")
                self.cancelMktData(req_id)
            elif request_type == "historical_data":
                logging.info(f"Canceling historical data request {req_id}")
                self.cancelHistoricalData(req_id)
            elif request_type == "scanner":
                logging.info(f"Canceling scanner subscription {req_id}")
                self.cancelScannerSubscription(req_id)
            else:
                logging.info(f"Canceling request {req_id} (type: {request_type})")
                # Try to cancel based on request type
                if hasattr(self, f"cancel{request_type.capitalize()}"):
                    getattr(self, f"cancel{request_type.capitalize()}")(req_id)
        except Exception as e:
            logging.error(f"Error canceling request {req_id}: {e}")

    def stop_continuous_scanner(self):
        """
        Stop the continuous scanner.
        """
        logging.info("Stopping continuous scanner")
        self.continuous_scan_active = False
