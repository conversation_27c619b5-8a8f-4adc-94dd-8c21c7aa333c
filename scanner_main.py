#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Scanner Main - Main entry point for the ORB Scanner.

This module contains the main entry point, global configurations, constants,
the IDGenerator, the core IBApp class definition (inheriting from the new base classes),
and the main execution loop (run_continuous_scanner) and related top-level functions.
"""

import argparse
import logging
import time
import random
import os
import sys
import concurrent.futures
import traceback
import math
import inspect
from datetime import datetime, time as dt_time, timedelta
import pytz
import threading
import json
from collections import defaultdict
import io

from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.scanner import ScannerSubscription
from ibapi.tag_value import TagValue
from ibapi.contract import Contract
from ibapi.common import BarData

# Import our modules from the orbscanner package
from orbscanner.utils.time.timezone import (
    get_eastern_time,
    convert_to_eastern,
    get_time_diff_seconds,
    format_timestamp,
    get_market_open_time,
    get_market_close_time,
    is_market_open
)
from orbscanner.utils.phase.manager import PhaseManager
from orbscanner.utils.phase.watchdog import PhaseWatchdog
from orbscanner.utils.phase.logging import (
    log_phase_transition,
    log_phase_status,
    check_and_repair_phase_loggers,
    log_to_phase as standalone_log_to_phase_function
)
from orbscanner.utils.time.after_open import AfterOpenTimer
from orbscanner.utils.logging import (
    setup_logging,
    log_wrapper_call,
    set_wrapper_debug,
    set_wrapper_log_file,
    WRAPPER_DEBUG,
    WRAPPER_LOG_FILE
)

# Import the other components of the scanner
from app_logic import IBAppLogic
from scanner_processing import IBAppScannerProcessing
from data_handling import IBAppDataHandling

# Import scanner components
from orbscanner.scanners.scanner_factory import (
    get_scanner_types,
    get_scanner_filters,
    start_scanners,
    stop_scanners
)
from orbscanner.scanners.result_processor import (
    process_scanner_results,
    calculate_priority_score
)

# Import report generator
try:
    from orbscanner.reporting.report_generator import generate_markdown_report
except ImportError:
    logging.warning("Could not import report_generator module. Report generation will be disabled.")
    generate_markdown_report = None

# Import end-of-day trade analysis
try:
    from orbscanner.utils.reporting.eod_analysis import analyze_eod_trades
except ImportError:
    logging.warning("Could not import eod_trade_analysis module. End-of-day analysis will be disabled.")
    analyze_eod_trades = None

# Import SendGrid email module (preferred over standard email module)
try:
    from orbscanner.utils.reporting.sendgrid import email_report as sendgrid_email
    logging.info("Using SendGrid for email delivery")
    email_function = sendgrid_email
except ImportError:
    logging.warning("Could not import sendgrid_email module. Falling back to standard email module.")
    try:
        from orbscanner.utils.reporting.email import email_report as standard_email
        email_function = standard_email
    except ImportError:
        logging.warning("Could not import email_report module. Email functionality will be disabled.")
        email_function = None

# --- Configuration ---
TWS_HOST = "127.0.0.1"
TWS_PORT = 7497 # 7497 for TWS, 4001 for Gateway
CONNECT_TIMEOUT = 10 # seconds
SCANNER_TIMEOUT = 30 # seconds to wait for scanner results
SCAN_INTERVAL = 60 # seconds between continuous scans (increased from 30s to 60s to comply with rate limits)
OPENING_RANGE_MINUTES = 5 # minutes to track opening range
MAX_TRACKED_SYMBOLS = 25 # maximum number of symbols to track for opening range
HISTORICAL_DATA_TIMEOUT = 30 # seconds to wait for historical data (increased from 15 to 30 seconds)
SANITY_CHECK_TIMEOUT = 45 # seconds to wait for sanity check historical data (increased from 30 to 45 seconds)
USE_HISTORICAL_DATA = False # whether to use historical data for opening ranges
HISTORICAL_DATA_RETRIES = 1 # number of retries for historical data requests (reduced from 2 to limit API calls)
ENABLE_FAILSAFE = True # whether to enable the fail-safe mechanism that exits the program if no valid opening ranges are found
ENABLE_TEST_CANDIDATE = False # whether to enable the test candidate (AAPL) when no candidates are found
TEST_CANDIDATE_SYMBOL = "AAPL" # symbol to use for test candidate

# --- Wrapper Logging Configuration ---
# Using WRAPPER_DEBUG and WRAPPER_LOG_FILE from orbscanner.utils.logging
set_wrapper_debug(True)  # Enable wrapper logging by default
# Ensure logs/live directory exists
os.makedirs("logs/live", exist_ok=True)
set_wrapper_log_file(os.path.join("logs", "live", "scanner_wrapper.log"))  # Set log file in logs/live directory

# --- Scanner Rate Limiting Configuration ---
MAX_SCANNER_REQUESTS_PER_10MIN = 50  # IBKR limit: 50 requests per 10 minutes
MAX_SCANNER_REQUESTS_PER_MINUTE = 5  # Conservative limit: 5 requests per minute
SCANNER_BATCH_SIZE = 4  # Maximum simultaneous scanner subscriptions (reduced from 8)
SCANNER_BATCH_DELAY = 3.0  # Seconds to wait between scanner batches
# --- Volume Data Collection Configuration ---
# Set this flag to True to disable all volume data collection and use IBKR's built-in filters instead
# This helps avoid API rate limits and timeouts when testing or running before market open
# When True, the scanner will use IBKR's built-in relative volume filter instead of collecting historical volume data
SKIP_VOLUME_DATA = True  # Hard-coded flag to disable volume data collection

# --- Volume Fallback Configuration ---
# SAFETY MODE: Enable volume baseline fallback when ADV data is missing
# Set to False to maintain current behavior (0.0x ratios when volume data missing)
# Set to True to enable conservative fallback calculations
ENABLE_VOLUME_FALLBACK = True  # Enabled by default to fix 0.0x volume issue

# --- Trend Data Pre-Screening Configuration ---
REQUIRED_DAYS = 200 # days of historical data requested
MIN_DAYS_PERCENTAGE = 0.9 # minimum percentage of required days
MIN_DAYS = int(REQUIRED_DAYS * MIN_DAYS_PERCENTAGE) # 180 days
MAX_TIMEOUTS = 2 # maximum number of timeouts before excluding a symbol
MIN_VOLUME = 500000 # minimum average daily volume
MAX_SPREAD = 0.001 # maximum bid-ask spread (0.1%)
MIN_DOLLAR_VOLUME = 5000000 # minimum dollar volume ($5M)

# --- Phase Duration Constants ---
PRE_MARKET_PHASE_DURATION = timedelta(minutes=10)  # 10 minutes before market open
ORB_PHASE_DURATION = timedelta(minutes=7)  # Opening Range Building phase (7 minutes)
# SCANNING_PHASE_DURATION is now ORB_PHASE_DURATION
# This line is kept for backward compatibility with external scripts
SCANNING_PHASE_DURATION = ORB_PHASE_DURATION
FINALIZING_PHASE_DURATION = timedelta(minutes=2, seconds=57)  # Exactly 2:57
TREND_DATA_COLLECTION_DURATION = timedelta(minutes=2, seconds=30)  # 2:30 for data collection
BATCH_PROCESSING_DELAY = 1.0  # Increased to 1.0 second for IBKR compliance
BATCH_SIZE = 5  # Reduced from 20 to 5 symbols for IBKR compliance
MAX_PRE_MARKET_CANDIDATES = 30  # Maximum symbols to pre-cache during pre-market phase

# Use dynamic request IDs instead of hardcoded ones
class IDGenerator:
    def __init__(self):
        self._id = random.randint(1000, 50000)  # Random start

    def next(self):
        self._id += 1
        return self._id

# Global variable to store the main app instance
_APP_INSTANCE = None

def get_app_instance():
    """
    Get the main application instance.

    This function provides access to the main IBApp instance from anywhere in the code.
    It's used by utility functions that need to access the main app but don't have a direct reference.

    Returns:
        IBApp: The main application instance, or None if not set
    """
    global _APP_INSTANCE
    if _APP_INSTANCE is None:
        logging.critical("CRITICAL ERROR: App instance not set or not available when requested")

        # ENHANCED RELIABILITY: Try to find app instance in main module globals
        import sys
        if 'app' in sys.modules['__main__'].__dict__:
            _APP_INSTANCE = sys.modules['__main__'].__dict__['app']
            logging.critical("CRITICAL FIX: Found app instance in main module globals")

        # If still None, try to search in stack frames
        if _APP_INSTANCE is None:
            import inspect
            import traceback
            logging.critical("CRITICAL FIX: Searching for app instance in stack frames")

            # Log stack trace for debugging
            stack_trace = traceback.format_stack()
            for i, frame in enumerate(stack_trace[-5:]):  # Show last 5 frames
                logging.critical(f"Frame {i}: {frame.strip()}")

            # Search in stack frames
            frames = inspect.stack()
            for frame_info in frames:
                frame_locals = frame_info.frame.f_locals
                if 'self' in frame_locals:
                    obj = frame_locals['self']
                    if obj.__class__.__name__ == 'IBApp':
                        _APP_INSTANCE = obj
                        logging.critical(f"CRITICAL FIX: Found IBApp instance in stack frame")
                        break
                if 'app' in frame_locals:
                    obj = frame_locals['app']
                    if hasattr(obj, '__class__') and obj.__class__.__name__ == 'IBApp':
                        _APP_INSTANCE = obj
                        logging.critical(f"CRITICAL FIX: Found app variable in stack frame")
                        break

        # Final check if we found an instance
        if _APP_INSTANCE is None:
            logging.critical("CRITICAL ERROR: Could not find app instance after exhaustive search")
        else:
            logging.critical(f"CRITICAL FIX: Successfully recovered app instance: {_APP_INSTANCE}")

    return _APP_INSTANCE

def set_app_instance(app):
    """
    Set the main application instance.

    This function stores a reference to the main IBApp instance for later retrieval.

    Args:
        app (IBApp): The main application instance
    """
    global _APP_INSTANCE
    _APP_INSTANCE = app
    logging.critical("CRITICAL FIX: Main application instance registered for global access")

    # Validate the instance was properly set
    if _APP_INSTANCE is None:
        logging.critical("CRITICAL ERROR: Failed to set app instance - still None after assignment")
    elif _APP_INSTANCE is not app:
        logging.critical("CRITICAL ERROR: App instance mismatch after assignment")
    else:
        logging.critical("CRITICAL FIX: App instance successfully registered and validated")

id_gen = IDGenerator() # Dedicated request ID for VIX
VIX_TIMEOUT = 15 # seconds to wait for VIX data

# Import the setup_logging function from orbscanner.utils.logging
from orbscanner.utils.logging import setup_logging

# Import the log_to_phase function from orbscanner.utils.phase.logging
from orbscanner.utils.phase.logging import log_to_phase

# Main IBApp class that combines all components
class IBApp(IBAppLogic, IBAppScannerProcessing, IBAppDataHandling):
    def __init__(self):
        """
        Initialize the IBApp instance.
        This method initializes all components of the IBApp class.
        """
        # Initialize base classes
        IBAppLogic.__init__(self)
        IBAppScannerProcessing.__init__(self)
        IBAppDataHandling.__init__(self)
        # Initialize EClient and EWrapper
        EClient.__init__(self, self)

        # Initialize request ID counter
        self.nextOrderId = None
        self.reqId = 0  # Simple request ID counter

        # Scanner results storage
        self.scannerResults = {"UNKNOWN": []}  # Dictionary to store results by scanner type
        self.scannerFinished = {}  # Track completion status by request ID
        self.scannerRequestIds = {}  # Map scanner types to request IDs
        self.allScannersFinished = False  # Flag for all scanners completion
        self.active_scanner_subscriptions = False  # Flag to track active scanner subscriptions

        # Combined results after processing
        self.combinedResults = []
        # Note: longCandidates and shortCandidates are already initialized in IBAppLogic

        # Active requests tracking
        self.active_requests = {}  # Track active requests

        # CDG TRACKING: Initialize attribute access tracking
        self._enable_attribute_tracking = True
        self._attribute_tracking_history = []

        # VIX data initialization
        self.__dict__['vix_value'] = None  # Use __dict__ to avoid triggering __setattr__
        self.vix_received_event = threading.Event()  # To signal VIX data arrival
        self.vix_req_id = None  # Will be set dynamically when requesting VIX data

        # Track market data farm connections
        self.connected_farms = set()
        self.required_farms = {"usfarm", "usfarm.nj"}  # Add other farms if needed
        self.farms_connection_event = threading.Event()

        # Opening range tracking
        self.tracked_symbols = {}  # Dictionary to store symbols being tracked for opening range
        self.opening_range_data = {}  # Dictionary to store opening range data
        self.opening_range_alt_filter_used = {}  # Track symbols where alternative filtering was used
        self.market_data_req_ids = {}  # Map symbols to market data request IDs
        self.continuous_scan_active = False  # Flag for continuous scanning
        self.scan_count = 0  # Counter for number of scans performed

        # CRITICAL FIX: Add valid_symbols attribute to prevent AttributeError during phase transitions
        self.valid_symbols = []  # List to store symbols with valid opening ranges

        # Historical data tracking
        self.historical_data_buffer = {}  # Buffer to store historical data bars
        self.historical_data_finished = {}  # Track completion status by request ID
        self.historical_data_events = {}  # Events to signal historical data completion

        # Daily trend data tracking
        self.daily_data_buffer = {}  # Buffer to store daily historical data bars
        self.daily_data_finished = {}  # Track completion status by request ID
        self.daily_data_events = {}  # Events to signal daily data completion
        self.daily_trend_data = {}  # Dictionary to store daily trend data (VWAP, SMA200)

        # Priority scoring
        self.symbol_priorities = {}  # Dictionary to store priority scores for symbols
        self.pre_market_highs = {}  # Dictionary to store pre-market highs
        self.avg_volumes = {}  # Dictionary to store average volumes
        self.pre_market_candidates = []  # List to store pre-market candidates

        # Contract details tracking
        self.contract_details_req_ids = {}  # Map symbols to contract details request IDs
        self.contract_details_received = {}  # Track completion status by request ID
        self.contract_details_events = {}  # Events to signal contract details completion

        # Scan history for reporting
        self.scan_history = []  # List to store scan results history
        self.last_scan_time = 0  # Track the last scan time for frequency control

        # Market open synchronization flag
        self.sync_with_market_open = True  # Default to True, can be overridden

        # Daily trend alignment flag
        self.use_trend_alignment = True  # Default to True, can be disabled

        # Symbol data tracking for pre-screening
        self.symbol_data = {}  # Dictionary to store symbol-specific data
        self.reqId_to_symbol = {}  # Map request IDs to symbols
        self.timeout_history = {}  # Track timeout history by symbol

        # Initialize subscription tracking
        self.subscription_required_symbols = set()  # Set of symbols that require subscriptions

        # Historical data error tracking
        self.sanity_check_errors = {}  # Track errors during sanity checks
        self.sanity_check_req_ids = set()  # Track request IDs used for sanity checks
        self.historical_data_errors = {}  # Track historical data errors by symbol
        self.exchange_success_rates = {  # Track success rates for different exchanges
            'SMART': {'success': 0, 'failure': 0},
            'ARCA': {'success': 0, 'failure': 0},
            'NYSE': {'success': 0, 'failure': 0},
            'NASDAQ': {'success': 0, 'failure': 0}
        }

        # Filtering statistics tracking
        self.filtering_stats = {}  # Dictionary to store filtering statistics
        self.rejected_candidates = {
            'range_width_rejected': {},
            'volume_rejected': {},
            'liquidity_rejected': {},
            'trend_rejected': {}
        }  # Dictionary to store rejected candidates and reasons
        self.trend_alignment_stats = {
            'LONG': {'aligned': 0, 'rejected': 0, 'missing': 0},
            'SHORT': {'aligned': 0, 'rejected': 0, 'missing': 0}
        }  # Dictionary to store trend alignment statistics

        # Initialize historical data rate limiter
        try:
            from orbscanner.utils.data.rate_limiter import HistoricalDataRateLimiter
            self.rate_limiter = HistoricalDataRateLimiter()
            logging.info("Historical data rate limiter initialized with default settings (50 requests per 10 minutes)")
        except ImportError:
            logging.warning("Rate limiter module not found. Rate limiting will not be applied.")
            logging.warning("Consider adding orbscanner/utils/data/rate_limiter.py to enable rate limiting.")

        # Initialize scanner rate limiter
        self.scanner_request_times = []  # List to track scanner request timestamps
        self.last_scanner_cleanup_time = time.time()  # Last time we cleaned up old timestamps
        logging.info(f"Scanner rate limiter initialized with limits: {MAX_SCANNER_REQUESTS_PER_MINUTE}/min, {MAX_SCANNER_REQUESTS_PER_10MIN}/10min")

        # Phase management (will be initialized in run_continuous_scanner)
        self.phase_manager = None
        self.phase_watchdog = None
        self.after_open_timer = None

        # Historical data processing flags
        self.historical_data_processed_for_orb = False  # Track if historical data was processed for ORB

        # Priority score calculation tracking
        self.valid_priority_score_count = 0  # Count of valid priority score calculations
        self.total_priority_score_attempts = 0  # Count of priority score calculation attempts
        self.priority_score_stats = {  # Statistics for priority score calculations
            'success': 0,
            'failure': 0,
            'failure_reasons': {},
            'success_rate': 0.0
        }

        # Initialize selection logger (will be properly set up later)
        try:
            from orbscanner.utils.reporting.selection import SelectionLogger
            self.selection_logger = SelectionLogger()
            logging.info(f"Initialized selection logger in app constructor")
        except (ImportError, Exception) as e:
            # Will be created later when needed
            logging.debug(f"Selection logger will be initialized when needed: {str(e)}")

    def ensure_symbol_string(self, symbol):
        """
        Utility function to ensure a symbol is a valid string.
        This is a wrapper around the extracted function in orbscanner.data.historical_data.

        Args:
            symbol: The symbol to validate and convert if needed

        Returns:
            str: The validated symbol as a string, or "UNKNOWN" if conversion fails
        """
        from orbscanner.data.historical_data import ensure_symbol_string as ensure_symbol_string_func
        return ensure_symbol_string_func(self, symbol)

    def get_historical_opening_range(self, symbol, market_open_time=None):
        """
        Get historical opening range data for a symbol.
        This is a wrapper around the extracted function in orbscanner.data.historical_data.

        Args:
            symbol (str): The symbol to get opening range for
            market_open_time (datetime, optional): Market open time. If None, uses 9:30 AM today.

        Returns:
            dict: Dictionary with high, low, open, and volume data, or None if data retrieval failed
        """
        from orbscanner.data.historical_data import get_historical_opening_range as get_historical_opening_range_func
        return get_historical_opening_range_func(self, symbol, market_open_time)

    def get_daily_trend_data(self, symbol):
        """
        Get daily trend data (VWAP, SMA200) for a symbol.
        This is a wrapper around the extracted function in orbscanner.analysis.trend_analyzer.

        Args:
            symbol (str): The symbol to get trend data for

        Returns:
            dict: Dictionary with trend data, or None if data retrieval failed
        """
        from orbscanner.analysis.trend_analyzer import get_daily_trend_data as get_daily_trend_data_func
        return get_daily_trend_data_func(self, symbol)

    def calculate_priority_score(self, symbol_data):
        """
        Calculate priority score for a symbol.
        This is a wrapper around the orbscanner.scanners.result_processor.calculate_priority_score function.

        Args:
            symbol_data (dict): Symbol data dictionary

        Returns:
            float: Priority score
        """
        from orbscanner.scanners.result_processor import calculate_priority_score as calculate_priority_score_func
        return calculate_priority_score_func(self, symbol_data)

def run_continuous_scanner(app, args):
    """
    Run the continuous scanner.

    This function runs the continuous scanner, which periodically scans the market
    for potential ORB candidates, tracks opening ranges, and generates reports.

    Args:
        app (IBApp): The IBApp instance
        args (argparse.Namespace): Command-line arguments

    Returns:
        None
    """
    # Store args in app for access from other methods
    app.args = args

    # Determine logging level
    log_level = logging.DEBUG if args.debug else logging.INFO

    # Determine data tracing setting
    enable_data_tracing = args.enable_data_tracing  # Use the value from args directly

    # Set up logging
    log_base_path = os.path.join(args.output_dir, args.log_base)
    setup_logging(log_base_path, level=log_level, enable_data_tracing=enable_data_tracing)
    app.log_base_path = log_base_path

    # Initialize phase manager
    app.phase_manager = PhaseManager(log_base_path, args.after_open)
    app.phase_watchdog = PhaseWatchdog(app.phase_manager)

    # Initialize after-open timer if needed
    if args.after_open:
        app.after_open_timer = AfterOpenTimer(args.after_open_minutes)
        logging.info(f"Initialized after-open timer with {args.after_open_minutes} minutes after market open")
    else:
        app.after_open_timer = None

    # Connect to IB API
    logging.info(f"Connecting to IB API at {TWS_HOST}:{TWS_PORT}")
    app.connect(TWS_HOST, TWS_PORT, clientId=random.randint(1, 10000))

    # Start API connection thread
    api_thread = threading.Thread(target=app.run)
    api_thread.start()

    # Wait for connection
    logging.info(f"Waiting for connection (timeout: {CONNECT_TIMEOUT} seconds)")
    connect_deadline = time.time() + CONNECT_TIMEOUT
    while time.time() < connect_deadline and not app.isConnected():
        time.sleep(0.1)

    if not app.isConnected():
        logging.error(f"Failed to connect to IB API within {CONNECT_TIMEOUT} seconds")
        return

    logging.info("Connected to IB API")

    # Register app instance for global access
    set_app_instance(app)

    # Wait for next valid ID
    logging.info("Waiting for next valid ID")
    while app.nextOrderId is None:
        time.sleep(0.1)

    logging.info(f"Next valid ID: {app.nextOrderId}")

    # Get VIX value for scanner configuration
    logging.info("Getting VIX value")
    vix_value = app.get_vix_value()
    if vix_value:
        logging.info(f"VIX value: {vix_value:.2f}")
    else:
        logging.warning("Failed to get VIX value, using default scanner configuration")

    # Perform sanity checks
    if hasattr(app, 'perform_sanity_checks'):
        app.perform_sanity_checks()

    # Determine if we should sync with market open
    sync_with_market_open = args.sync_with_market_open and not args.after_open
    app.sync_with_market_open = sync_with_market_open

    # Get current time in Eastern timezone
    eastern_now = get_eastern_time()
    logging.info(f"Current time (Eastern): {eastern_now.strftime('%Y-%m-%d %H:%M:%S')}")

    # Get market open time (use command line argument if provided)
    if hasattr(args, 'market_open_time') and args.market_open_time:
        try:
            # Parse HH:MM format
            hour, minute = map(int, args.market_open_time.split(':'))
            market_open_time = dt_time(hour, minute)
            logging.info(f"Using custom market open time: {market_open_time.strftime('%H:%M:%S')}")
        except (ValueError, TypeError) as e:
            logging.warning(f"Invalid market open time format: {args.market_open_time}. Using default.")
            market_open_time = get_market_open_time()
            logging.info(f"Default market open time: {market_open_time.strftime('%H:%M:%S')}")
    else:
        market_open_time = get_market_open_time()
        logging.info(f"Market open time: {market_open_time.strftime('%H:%M:%S')}")

    # Get market close time
    market_close_time = get_market_close_time()
    logging.info(f"Market close time: {market_close_time.strftime('%H:%M:%S')}")

    # Check if market is open
    market_open = is_market_open()
    logging.info(f"Market is {'open' if market_open else 'closed'}")

    # If after-open mode is enabled, skip waiting for market open
    if args.after_open:
        logging.info(f"After-open mode enabled with {args.after_open_minutes} minutes after market open")
        # Note: AfterOpenTimer starts automatically when initialized, no need to call start()
        sync_with_market_open = False
        app.sync_with_market_open = False
        # Log the timer's start time for better debugging
        if app.after_open_timer:
            logging.info(f"After-open timer started at: {format_timestamp(app.after_open_timer.start_time)}")
    else:
        app.after_open_timer = None

    # Wait for market open if needed
    if sync_with_market_open and not market_open:
        # Calculate time until market open
        market_open_dt = eastern_now.replace(
            hour=market_open_time.hour,
            minute=market_open_time.minute,
            second=market_open_time.second,
            microsecond=0
        )

        # If market open time is in the past, use next day
        if market_open_dt < eastern_now:
            market_open_dt = market_open_dt + timedelta(days=1)

        # Calculate seconds until market open
        seconds_until_open = (market_open_dt - eastern_now).total_seconds()

        # Log waiting message
        logging.info(f"Waiting for market open in {seconds_until_open:.0f} seconds ({seconds_until_open/60:.1f} minutes)")

        # Wait until 10 minutes before market open to start pre-market phase
        pre_market_start_time = market_open_dt - PRE_MARKET_PHASE_DURATION
        seconds_until_pre_market = (pre_market_start_time - eastern_now).total_seconds()

        if seconds_until_pre_market > 0:
            logging.info(f"Waiting for pre-market phase to start in {seconds_until_pre_market:.0f} seconds ({seconds_until_pre_market/60:.1f} minutes)")
            time.sleep(seconds_until_pre_market)

            # Start pre-market phase
            logging.info("Starting pre-market phase")
            app.phase_manager.transition_to(PhaseManager.PRE_MARKET, "Scheduled transition")
            app.run_pre_market_phase()

            # Calculate remaining time until market open
            eastern_now = get_eastern_time()
            seconds_until_open = (market_open_dt - eastern_now).total_seconds()

            if seconds_until_open > 0:
                logging.info(f"Pre-market phase completed. Waiting for market open in {seconds_until_open:.0f} seconds ({seconds_until_open/60:.1f} minutes)")
                time.sleep(seconds_until_open)
        else:
            # If we're already past pre-market start time but before market open, start pre-market phase now
            if seconds_until_open > 0:
                logging.info("Starting pre-market phase immediately (already within pre-market window)")
                app.phase_manager.transition_to(PhaseManager.PRE_MARKET, "Scheduled transition")
                app.run_pre_market_phase()

                # Calculate remaining time until market open
                eastern_now = get_eastern_time()
                seconds_until_open = (market_open_dt - eastern_now).total_seconds()

                if seconds_until_open > 0:
                    logging.info(f"Pre-market phase completed. Waiting for market open in {seconds_until_open:.0f} seconds ({seconds_until_open/60:.1f} minutes)")
                    time.sleep(seconds_until_open)

    # Start ORB phase
    logging.info("Starting ORB phase")
    app.phase_manager.transition_to(PhaseManager.ORB, "Scheduled transition")

    # Set continuous scan flag
    app.continuous_scan_active = True

    # Calculate end time for continuous scanning
    if args.scan_duration:
        end_time = time.time() + args.scan_duration * 60
        logging.info(f"Continuous scanning will run for {args.scan_duration} minutes")
    else:
        # Run until market close
        eastern_now = get_eastern_time()
        market_close_dt = eastern_now.replace(
            hour=market_close_time.hour,
            minute=market_close_time.minute,
            second=market_close_time.second,
            microsecond=0
        )

        # If market close time is in the past, use next day
        if market_close_dt < eastern_now:
            market_close_dt = market_close_dt + timedelta(days=1)

        # Calculate seconds until market close
        seconds_until_close = (market_close_dt - eastern_now).total_seconds()
        end_time = time.time() + seconds_until_close
        logging.info(f"Continuous scanning will run until market close ({seconds_until_close/60:.1f} minutes)")

    # Run continuous scanning
    scan_count = 0
    orb_phase_end_time = time.time() + ORB_PHASE_DURATION.total_seconds()

    while app.continuous_scan_active and time.time() < end_time:
        scan_start_time = time.time()
        scan_count += 1

        # Log scan start
        logging.info(f"Starting scan #{scan_count}")

        try:
            # Get VIX value for scanner configuration
            vix_value = getattr(app, 'vix_value', None)

            # CRITICAL FIX: Only start scanners if we're in the ORB phase
            # This prevents scanner subscriptions from being restarted after transitioning to MONITORING phase
            if hasattr(app, 'phase_manager') and app.phase_manager.current_phase == PhaseManager.ORB:
                logging.info("Running scanners in ORB phase")
                app.start_scanners(vix_value, is_continuous=True)
            else:
                current_phase = getattr(app.phase_manager, 'current_phase', 'UNKNOWN') if hasattr(app, 'phase_manager') else 'UNKNOWN'
                logging.info(f"Skipping scanner execution - not in ORB phase (current phase: {current_phase})")

            # Wait for scanner results
            scanner_timeout = SCANNER_TIMEOUT  # seconds
            scanner_start_time = time.time()
            while not app.allScannersFinished and time.time() - scanner_start_time < scanner_timeout:
                time.sleep(0.5)

            # Check if scanners completed
            if not app.allScannersFinished:
                logging.warning(f"Scanners did not complete within {scanner_timeout} seconds")

            # Update opening ranges
            app.update_opening_ranges()

            # Check if ORB phase is complete
            if time.time() >= orb_phase_end_time and app.phase_manager.current_phase == PhaseManager.ORB:
                logging.info("ORB phase complete, transitioning to FINALIZING phase")

                # Log scanner subscription status before stopping
                scanner_active_status = getattr(app, 'active_scanner_subscriptions', None)
                logging.info(f"SCANNER STATUS [before_orb_transition]: active_scanner_subscriptions={scanner_active_status}")

                # CRITICAL FIX: Stop scanner subscriptions before transitioning to FINALIZING phase
                # This ensures all scanner subscriptions are properly canceled to avoid resource leaks
                # and unnecessary API calls to IBKR that could lead to rate limit issues
                logging.info("CRITICAL FIX: Stopping scanner subscriptions before transitioning to FINALIZING phase")
                app.stop_scanners()  # This will cancel all active scanner subscriptions

                # Verify scanner subscription status after stopping
                scanner_active_status = getattr(app, 'active_scanner_subscriptions', None)
                logging.info(f"SCANNER STATUS [after_stop_before_finalizing]: active_scanner_subscriptions={scanner_active_status}")

                # CRITICAL FIX: Explicitly ensure flag is False before phase transition
                app.active_scanner_subscriptions = False
                logging.info("CRITICAL FIX: Explicitly set active_scanner_subscriptions to False before FINALIZING phase")

                app.phase_manager.transition_to(PhaseManager.FINALIZING, "Scheduled transition")

                # Finalize opening ranges
                app.finalize_opening_ranges()

                # Log scanner status before monitoring transition
                scanner_active_status = getattr(app, 'active_scanner_subscriptions', None)
                logging.info(f"SCANNER STATUS [before_monitoring_transition]: active_scanner_subscriptions={scanner_active_status}")

                # Transition to monitoring phase
                app.transition_to_monitoring_phase()

                # Final verification of scanner status after full transition sequence
                scanner_active_status = getattr(app, 'active_scanner_subscriptions', None)
                logging.info(f"SCANNER STATUS [after_complete_transition]: active_scanner_subscriptions={scanner_active_status}")

                # Force flag to False if still not False
                if scanner_active_status is not False:
                    logging.warning("CRITICAL ISSUE: Scanner flag still not False after complete transition sequence")
                    app.active_scanner_subscriptions = False
                    logging.info(f"CRITICAL FIX: Forcibly set active_scanner_subscriptions to False")

            # Calculate time for next scan
            scan_end_time = time.time()
            scan_duration = scan_end_time - scan_start_time
            next_scan_time = scan_start_time + args.scan_interval
            sleep_time = max(0, next_scan_time - scan_end_time)

            # Log scan completion
            logging.info(f"Scan #{scan_count} completed in {scan_duration:.1f} seconds, next scan in {sleep_time:.1f} seconds")

            # Sleep until next scan
            if sleep_time > 0 and app.continuous_scan_active and time.time() < end_time:
                time.sleep(sleep_time)

        except Exception as e:
            logging.error(f"Error during scan #{scan_count}: {e}")
            logging.error(f"Traceback: {traceback.format_exc()}")

            # Sleep before next scan
            time.sleep(args.scan_interval)

    # Log completion
    logging.info(f"Continuous scanning completed with {scan_count} scans")

    # Stop monitoring loop if active
    if hasattr(app, 'monitoring_loop') and app.monitoring_loop is not None:
        logging.info("Stopping monitoring loop")
        app.monitoring_loop.stop()
        logging.info("Monitoring loop stopped")

    # CRITICAL FIX: Final cleanup of any remaining scanner subscriptions
    # This ensures all scanner subscriptions are properly canceled before disconnecting
    scanner_active_status = getattr(app, 'active_scanner_subscriptions', None)
    logging.info(f"SCANNER STATUS [before_final_cleanup]: active_scanner_subscriptions={scanner_active_status}")

    if scanner_active_status is not False:
        logging.warning("CRITICAL FIX: Scanner subscriptions may still be active, performing final cleanup")
        app.stop_scanners()  # This will cancel all active scanner subscriptions
        app.active_scanner_subscriptions = False
        logging.info("CRITICAL FIX: Final scanner cleanup completed")

    # Generate report if enabled
    if args.generate_report and generate_markdown_report:
        logging.info("Generating report")
        
        # Get VIX value for report generation
        vix_value = getattr(app, 'vix_value', None)
        
        # Call the report generator with the correct arguments
        report_path = generate_markdown_report(app, log_base_path, vix_value, args.email_report)
        
        if report_path:
            logging.info(f"Report generated: {report_path}")
        else:
            logging.error("Failed to generate report - report path is None")

        # Email report if enabled - handled by generate_markdown_report now
        if args.email_report and email_function and report_path:
            logging.info("Email delivery handled by report generator")
            
        # Generate end-of-day trade analysis report
        if analyze_eod_trades is not None:
            try:
                logging.info("Generating end-of-day trade analysis report...")
                eod_report_path = analyze_eod_trades(log_base_path, email_report=args.email_report)
                
                if eod_report_path:
                    logging.info(f"End-of-day trade analysis report generated: {eod_report_path}")
                else:
                    logging.warning("Failed to generate end-of-day trade analysis report")
            except Exception as e:
                logging.error(f"Error generating end-of-day trade analysis report: {e}", exc_info=True)
        else:
            logging.warning("End-of-day trade analysis module not available")

    # Disconnect from IB API
    logging.info("Disconnecting from IB API")
    app.disconnect()

    # Wait for API thread to exit
    api_thread.join(timeout=5)

    logging.info("Continuous scanner completed")

def main():
    """
    Main entry point for the ORB Scanner.

    This function parses command-line arguments, creates the IBApp instance,
    and runs the continuous scanner.

    Returns:
        int: Exit code
    """
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="ORB Scanner")
    parser.add_argument("--output-dir", default="logs/live", help="Output directory for logs and reports")
    parser.add_argument("--log-base", default=f"scan_orb_{datetime.now().strftime('%Y%m%d_%H%M%S')}", help="Base name for log files")
    parser.add_argument("--client-id", type=int, default=random.randint(3000, 6000), help="TWS Client ID (random 3000-6000 default)")
    parser.add_argument("--host", default=TWS_HOST, help=f"TWS host address (default: {TWS_HOST})")
    parser.add_argument("--port", type=int, default=TWS_PORT, help=f"TWS port (default: {TWS_PORT})")
    parser.add_argument("--continuous-scan", action="store_true", help="Run continuous scanning")
    parser.add_argument("--scan-interval", type=int, default=SCAN_INTERVAL, help="Interval between scans in seconds")
    parser.add_argument("--scan-duration", type=int, help="Duration of continuous scanning in minutes")
    parser.add_argument("--generate-report", action="store_true", help="Generate report after scanning")
    parser.add_argument("--email-report", action="store_true", help="Email report after scanning")
    parser.add_argument("--no-email-report", action="store_false", dest="email_report", help="Disable emailing the report")
    parser.add_argument("--email-recipients", help="Comma-separated list of email recipients")

    # After-open mode options
    parser.add_argument("--after-open", action="store_true", help="Run in after-open mode")
    parser.add_argument("--after-open-minutes", type=int, default=7, help="Minutes after market open in after-open mode")

    # Sync options (support both --no-sync and --no-sync-with-market-open for backward compatibility)
    parser.add_argument("--sync-with-market-open", action="store_true", default=True,
                        help="Synchronize with market open at 9:30 AM (default: enabled)")
    parser.add_argument("--no-sync", action="store_false", dest="sync_with_market_open",
                        help="Don't sync with market open")
    parser.add_argument("--no-sync-with-market-open", action="store_false", dest="sync_with_market_open",
                        help="Don't sync with market open (alias for --no-sync)")

    # Historical data options
    parser.add_argument("--use-historical-data", action="store_true", help="Use historical data for opening ranges")
    parser.add_argument("--historical-data-timeout", type=int, default=30,
                        help="Timeout for historical data requests in seconds (default: 30)")
    parser.add_argument("--market-open-date", type=str,
                        help="Market open date for historical data (format: YYYY-MM-DD, default: today)")

    # Market time options
    parser.add_argument("--market-open-time", default="09:30", help="Market open time (HH:MM)")

    # Trend alignment options
    parser.add_argument("--use-trend-alignment", action="store_true", default=True,
                        help="Use daily trend alignment (default: enabled)")
    parser.add_argument("--no-trend-alignment", action="store_false", dest="use_trend_alignment",
                        help="Disable trend alignment")

    # Failsafe options
    parser.add_argument("--enable-fail-safe", action="store_true", default=True,
                        help="Enable fail-safe mode (exit if no valid symbols) (default: enabled)")
    parser.add_argument("--disable-fail-safe", action="store_false", dest="enable_fail_safe",
                        help="Disable fail-safe mode")

    # Tracing options
    parser.add_argument("--enable-data-tracing", action="store_true", default=True,
                        help="Enable data request tracing (default: enabled)")
    parser.add_argument("--disable-data-tracing", action="store_false", dest="enable_data_tracing",
                        help="Disable data request tracing")

    # Test candidate options
    parser.add_argument("--enable-test-candidate", action="store_true", default=False,
                        help="Enable test candidate mode (default: disabled)")
    parser.add_argument("--test-candidate-symbol", type=str, default="AAPL",
                        help="Symbol to use for test candidate (default: AAPL)")

    # Test options
    parser.add_argument("--test-phase-manager", action="store_true",
                        help="Run a test of the PhaseManager instead of the full scanner")

    # Debug options
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")

    args = parser.parse_args()

    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)

    # Set up logging
    setup_logging(args.log_base, level=logging.DEBUG if args.debug else logging.INFO,
                 enable_data_tracing=args.enable_data_tracing)
    logging.info("--- Starting ORB Scanner --- ")
    logging.info(f"Arguments: {args}")

    # Log data tracing status
    if args.enable_data_tracing:
        logging.info("Data request tracing is ENABLED - detailed request/response data will be logged")
    else:
        logging.info("Data request tracing is DISABLED")

    # If test-phase-manager is specified, run the phase manager test instead of the full scanner
    if args.test_phase_manager:
        logging.info("Running PhaseManager test")

        # Import and run the test_phase_manager module
        try:
            # Import from the orbscanner.tests.phase package
            from orbscanner.tests.phase.test_phase_manager import main as test_phase_manager_main
            test_phase_manager_main()
            return 0  # Exit after running the test
        except ImportError:
            logging.error("Could not import test_phase_manager module. Make sure orbscanner/tests/phase/test_phase_manager.py exists.")
            return 1
        except Exception as e:
            logging.error(f"Error running PhaseManager test: {e}", exc_info=True)
            return 1

    # Set global flags based on arguments
    global USE_HISTORICAL_DATA, ENABLE_FAILSAFE, ENABLE_TEST_CANDIDATE, TEST_CANDIDATE_SYMBOL

    # Auto-enable historical data when after-open is used
    if args.after_open and not args.use_historical_data:
        logging.info("Auto-enabling historical data mode because --after-open flag is used")
        args.use_historical_data = True

    # Historical data settings
    USE_HISTORICAL_DATA = args.use_historical_data

    # Update historical data timeout
    if hasattr(args, 'historical_data_timeout') and args.historical_data_timeout is not None:
        global HISTORICAL_DATA_TIMEOUT
        HISTORICAL_DATA_TIMEOUT = args.historical_data_timeout

    # Test candidate settings
    ENABLE_TEST_CANDIDATE = args.enable_test_candidate
    TEST_CANDIDATE_SYMBOL = args.test_candidate_symbol

    # Failsafe settings
    ENABLE_FAILSAFE = args.enable_fail_safe

    # Log configuration settings
    logging.info(f"Historical data mode: {'Enabled' if USE_HISTORICAL_DATA else 'Disabled'}")
    logging.info(f"Historical data timeout: {HISTORICAL_DATA_TIMEOUT} seconds")
    logging.info(f"Test candidate mode: {'Enabled' if ENABLE_TEST_CANDIDATE else 'Disabled'}")

    if ENABLE_TEST_CANDIDATE:
        logging.warning(f"⚠️ TEST MODE ACTIVE: Will use {TEST_CANDIDATE_SYMBOL} as test candidate if no candidates are found")
        logging.warning(f"⚠️ WARNING: TEST MODE IS FOR DEBUGGING ONLY. DO NOT USE FOR REAL TRADING!")

    # Create IBApp instance
    app = IBApp()

    # Store command-line args in the app instance for later use
    app.args = args

    # Set trend alignment flag
    app.use_trend_alignment = args.use_trend_alignment

    # Set sync with market open flag
    app.sync_with_market_open = args.sync_with_market_open

    # Set after-open flag
    app.after_open = args.after_open

    # Run single scan or continuous scanner
    if args.continuous_scan:
        run_continuous_scanner(app, args)
    else:
        # For single scan, just run the continuous scanner with a duration of 1 minute
        args.scan_duration = 1
        run_continuous_scanner(app, args)

    return 0

if __name__ == "__main__":
    sys.exit(main())
