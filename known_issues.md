# Known Issues

- [CR<PERSON><PERSON><PERSON>] App Instance Management and Recovery: The main application instance (app) is not consistently accessible through its intended global accessor (get_app_instance()). The code resorts to searching stack frames to find it, which is a high-risk workaround. This indicates a fundamental issue in how the application's core instance is managed or passed between modules. If this recovery mechanism fails, it can lead to NoneType errors, inconsistent state, and unpredictable behavior throughout the application. The problem likely lies in the interaction between scanner_main.py (where get_app_instance is defined) and modules that call it, such as orbscanner/utils/phase/manager.py during phase transitions.
- [CRITICAL] Data loss may occur when the system is under heavy load. Investigating root cause.
- [MAJOR] API rate limits are not respected in some edge cases, leading to temporary bans. Workaround: Implement manual rate limiting.
- [MINOR] Scanner may request historical data for the same symbol multiple times if deduplication is not performed before batching. Fix includes deduplication and skipping already-processed symbols. Needs later testing.
- [MINOR] UI does not refresh automatically after certain actions. Workaround: Refresh manually.

# Recently Fixed Issues

- [FIXED] Symbol tracking information not appearing in monitoring logs. Fixed by implementing a dedicated monitoring loop (monitoring_loop.py) that regularly calls report_detailed_status() to log price updates and symbol tracking information.