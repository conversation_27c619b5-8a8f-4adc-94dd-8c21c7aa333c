#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Interactive Brokers API News Data Test Script

This script tests various ways to retrieve news data from Interactive Brokers API
for a predefined list of stock symbols.
"""

import time
import threading
import datetime
import logging
import random
import sys
import traceback
import struct  # For binary data handling
import socket
import queue
import json
import os
import re  # Regular expression module for parsing metadata
import pytz
import shutil
import fnmatch  # Added missing import for pattern matching

# Try to import yfinance, and if not available, we'll install it later
try:
    import yfinance as yf  # For fetching basic symbol information
except ImportError:
    yf = None  # Will be installed in check_and_install_yfinance()
from dateutil import parser
from dateutil.relativedelta import relativedelta
from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract
from ibapi.ticktype import TickTypeEnum
from ibapi.common import *
from ibapi.tag_value import TagValue
from ibapi.utils import (current_fn_name, BadMessage)  # Required for error handling

# Setup log directory structure and manage old logs
def setup_log_directories():
    """
    Set up log directory structure and move old logs to archive directory.
    Returns a tuple of (logs_dir, timestamp) for further use in the program.
    """
    # Define log directories
    home_dir = os.path.expanduser("~")
    logs_dir = os.path.join(home_dir, "Scan7", "logs", "live")
    logs_old_dir = os.path.join(logs_dir, "old")

    # Create log directories if they don't exist
    os.makedirs(logs_dir, exist_ok=True)
    os.makedirs(logs_old_dir, exist_ok=True)

    # Get current timestamp for new log files
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

    # Move existing log files to the old directory
    try:
        # Look for logs in the logs_dir, not the script directory
        # List of log files to check and move
        log_patterns = [
            "ib_news_*.log",
            "ib_news.log",
            "news_data_test.log",
            "news_test_summary_*.log"
        ]

        # First log the action we're about to take
        print(f"Looking for old log files in {logs_dir} to move to {logs_old_dir}")

        for pattern in log_patterns:
            for log_file in [f for f in os.listdir(logs_dir) if fnmatch.fnmatch(f, pattern)]:
                old_path = os.path.join(logs_dir, log_file)
                new_path = os.path.join(logs_old_dir, log_file)
                try:
                    # Check if the file exists and isn't a symbolic link
                    if os.path.isfile(old_path) and not os.path.islink(old_path):
                        print(f"Moving old log file: {log_file} to {logs_old_dir}")
                        shutil.move(old_path, new_path)
                    elif os.path.islink(old_path):
                        # Just remove symlinks, don't move them
                        print(f"Removing old symbolic link: {log_file}")
                        os.remove(old_path)
                except Exception as e:
                    print(f"Could not move log file {log_file}: {e}")

    except Exception as e:
        # Use print instead of logging since logging might not be initialized yet
        print(f"Error while moving old log files: {e}")
        print(f"Traceback: {traceback.format_exc()}")

    # Log a summary of what happened
    print(f"Log directory setup complete. New logs will be saved to: {logs_dir}")
    print(f"Timestamp for this run: {timestamp}")

    return logs_dir, timestamp

# Function to extract monitored symbols from the log file
def extract_monitored_symbols(log_file_path):
    """
    Extract symbols from monitoring log file that have MONITOR_STATUS entries
    Returns a list of unique symbols found in the log
    """
    try:
        if not os.path.exists(log_file_path):
            logging.warning(f"Monitoring log file not found: {log_file_path}")
            return []

        # Pattern to match monitor status lines and extract symbol
        # Example: "2025-05-21 23:33:23,104 - INFO - MONITOR_STATUS: XPEV(L)"
        pattern = r'MONITOR_STATUS: ([A-Z]+)\('

        # Also look for symbols in other common patterns in case MONITOR_STATUS isn't available
        alternative_patterns = [
            r'Tracking ([A-Z]+) for',
            r'Symbol ([A-Z]+) is now',
            r'Added ([A-Z]+) to watchlist'
        ]

        symbols = set()
        try:
            with open(log_file_path, 'r') as f:
                for line in f:
                    processed_line_for_symbol = False

                    # First check for MONITOR_STATUS
                    # Example: "2025-05-21 23:33:23,104 - INFO - MONITOR_STATUS: XPEV(L)"
                    if 'MONITOR_STATUS:' in line:
                        logging.debug(f"Processing line for MONITOR_STATUS: {line.strip()}")
                        match = re.search(pattern, line)  # 'pattern' is r'MONITOR_STATUS: ([A-Z]+)\('
                        if match:
                            symbol = match.group(1)
                            logging.debug(f"MONITOR_STATUS regex matched. Raw symbol: '{symbol}'")
                            if symbol.isupper() and symbol.isalpha() and 1 <= len(symbol) <= 7:
                                symbols.add(symbol)
                                logging.debug(f"Symbol '{symbol}' passed validation and was added.")
                            else:
                                logging.debug(f"Symbol '{symbol}' failed validation (isupper: {symbol.isupper()}, isalpha: {symbol.isalpha()}, len: {len(symbol)})")
                            processed_line_for_symbol = True
                        else:
                            logging.debug(f"MONITOR_STATUS regex did NOT match line containing 'MONITOR_STATUS:': {line.strip()}")
                    
                    # If not found by MONITOR_STATUS, check for TRANSITION candidate symbols
                    # Example: TRANSITION: Long candidate symbols: ['AAP', 'DOMO', 'KDLY']
                    # Example: TRANSITION: Short candidate symbols: ['QBTS', 'GOOGL', 'RGTI']
                    if not processed_line_for_symbol:
                        transition_match = re.search(r"TRANSITION: (?:Long|Short) candidate symbols: \[(.*?)\]", line)
                        if transition_match:
                            symbols_str = transition_match.group(1)
                            if symbols_str:  # Ensure not empty string from an empty list []
                                extracted_symbols_from_list = [s.strip().replace("'", "") for s in symbols_str.split(',') if s.strip()]
                                for sym_in_list in extracted_symbols_from_list:
                                    if sym_in_list.isupper() and sym_in_list.isalpha() and 1 <= len(sym_in_list) <= 7:
                                        symbols.add(sym_in_list)
                            processed_line_for_symbol = True  # Mark as processed

                    # If not found by above, try other alternative patterns
                    if not processed_line_for_symbol:
                        for alt_pattern in alternative_patterns:
                            match = re.search(alt_pattern, line)
                            if match:
                                symbol = match.group(1)
                                if symbol.isupper() and symbol.isalpha() and 1 <= len(symbol) <= 7:
                                    symbols.add(symbol)
                                    break  # Found a symbol with one of the alt_patterns
        except UnicodeDecodeError:
            logging.warning(f"Encoding issue with log file, trying with error handling: {log_file_path}")
        # If UnicodeDecodeError occurs, symbols might be empty or partially filled.
        # The function will then proceed to the return statement below.

        return list(symbols) # ADDED: Ensure symbols are returned even if no major exception occurred

    except Exception as e:
        logging.error(f"Error extracting symbols from monitoring log: {e}")
        return []

# Get the latest monitoring log file
def get_latest_monitoring_log():
    """
    Find the most recent monitoring log file in the logs directory
    Returns the path to the latest log file or None if not found
    """
    try:
        home_dir = os.path.expanduser("~")
        logs_dir = os.path.join(home_dir, "Scan7", "logs", "live")

        # Find all monitoring logs
        monitoring_logs = []
        for filename in os.listdir(logs_dir):
            if filename.startswith("scan_orb_") and "_monitoring.log" in filename:
                file_path = os.path.join(logs_dir, filename)
                monitoring_logs.append((file_path, os.path.getmtime(file_path)))

        # Sort by modification time (newest first)
        monitoring_logs.sort(key=lambda x: x[1], reverse=True)

        if monitoring_logs:
            latest_log = monitoring_logs[0][0]
            logging.info(f"Latest monitoring log: {os.path.basename(latest_log)}")
            return latest_log
        else:
            logging.warning("No monitoring logs found")

            # Try checking if there are any active scanner logs,
            # even if they don't have "_monitoring.log" in the name yet
            # This can help when a new scan has started but hasn't reached the monitoring phase
            active_logs = []
            for filename in os.listdir(logs_dir):
                if filename.startswith("scan_orb_") and filename.endswith(".log") and "_" not in filename:
                    file_path = os.path.join(logs_dir, filename)
                    # Only consider files being actively written (modified in last 5 minutes)
                    if time.time() - os.path.getmtime(file_path) < 300:  # 5 minutes
                        active_logs.append((file_path, os.path.getmtime(file_path)))

            if active_logs:
                active_logs.sort(key=lambda x: x[1], reverse=True)
                latest_active = active_logs[0][0]
                logging.info(f"Found active scanner log: {os.path.basename(latest_active)}")
                return latest_active
            return None
    except Exception as e:
        logging.error(f"Error finding latest monitoring log: {e}")
        return None

# Get symbol information using yfinance
def get_symbol_info(symbol):
    """
    Fetch basic information about a symbol using yfinance.
    Returns a dictionary with key information or None if not found.
    """
    global yf

    # If yfinance is not available, return basic info
    if yf is None:
        logging.warning(f"yfinance module not available, using basic symbol info for {symbol}")
        return {
            'symbol': symbol,
            'name': symbol,  # Using symbol as name
            'sector': 'N/A (yfinance not available)',
            'industry': 'N/A (yfinance not available)',
            'market_cap': 'N/A',
            'average_volume': 'N/A',
            'description': 'No description available - yfinance module not installed'
        }

    try:
        ticker = yf.Ticker(symbol)
        info = ticker.info

        # Extract the most useful information
        key_info = {
            'symbol': symbol,
            'name': info.get('shortName', 'N/A'),
            'sector': info.get('sector', 'N/A'),
            'industry': info.get('industry', 'N/A'),
            'market_cap': info.get('marketCap', 'N/A'),
            'average_volume': info.get('averageVolume', 'N/A'),
            'description': info.get('longBusinessSummary', 'N/A')[:200] + '...' if info.get('longBusinessSummary') and len(info.get('longBusinessSummary')) > 200 else info.get('longBusinessSummary', 'N/A')
        }

        logging.info(f"Symbol info retrieved for {symbol}: {key_info['name']} ({key_info['sector']})")
        return key_info
    except Exception as e:
        logging.warning(f"Could not retrieve info for {symbol}: {e}")
        return {
            'symbol': symbol,
            'name': symbol,  # Using symbol as name
            'sector': 'N/A',
            'industry': 'N/A',
            'market_cap': 'N/A',
            'average_volume': 'N/A',
            'description': f'Error retrieving info: {str(e)}'
        }

# Setup log directories and get timestamp
logs_dir, timestamp = setup_log_directories()

# Configure main logging
main_log_path = os.path.join(logs_dir, "news_data_test.log")
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.FileHandler(main_log_path),
        logging.StreamHandler()
    ]
)

# Define the default symbol and initialize TEST_SYMBOLS
DEFAULT_SYMBOL = "NVDA"  # Always monitor NVDA
TEST_SYMBOLS = [DEFAULT_SYMBOL]  # Will be updated after logging is set up

# Create a dedicated news logger that only captures news items
news_logger = logging.getLogger('news')
news_logger.setLevel(logging.INFO)

# Create a timestamped log filename to get a new log file for each run
news_log_filename = f"ib_news_{timestamp}.log"

# Create a symbolic link to the latest log for easy access
news_log_path = os.path.join(logs_dir, news_log_filename)
latest_link_path = os.path.join(logs_dir, "ib_news.log")

# Create the log handler with the timestamped filename
news_handler = logging.FileHandler(news_log_path)
news_handler.setFormatter(logging.Formatter('%(message)s'))  # Remove timestamp prefix
news_logger.addHandler(news_handler)

# Prevent news_logger from propagating to root logger to avoid duplicate entries
news_logger.propagate = False

# Initialize storage for initial symbol information
INITIAL_SYMBOLS_INFO = []

# Create/overwrite symbolic link to the latest log file
try:
    # Remove old link if it exists (Windows might need different handling)
    if os.path.exists(latest_link_path):
        os.remove(latest_link_path)
    # Create a new symbolic link (or copy on Windows)
    if hasattr(os, 'symlink'):
        os.symlink(news_log_path, latest_link_path)
    else:
        # On Windows, we might need to use a different approach
        import shutil
        shutil.copy2(news_log_path, latest_link_path)

    print(f"News will be logged to: {news_log_path}")
    print(f"A link to the latest log is available at: {latest_link_path}")
except Exception as e:
    print(f"Warning: Could not create symbolic link to news log: {e}")
    print(f"News will be logged to: {news_log_path}")

# Now that logger is set up, extract symbols from the monitoring log
latest_monitoring_log = get_latest_monitoring_log()
if latest_monitoring_log:
    log_symbols = extract_monitored_symbols(latest_monitoring_log)
    TEST_SYMBOLS = list(set([DEFAULT_SYMBOL] + log_symbols))  # Combine with default symbol
else:
    TEST_SYMBOLS = [DEFAULT_SYMBOL]  # Default symbol if no log is found

print(f"Will monitor {len(TEST_SYMBOLS)} symbols: {', '.join(TEST_SYMBOLS)}")
REALTIME_SYMBOLS = TEST_SYMBOLS.copy()

# Fetch and log initial symbol information
print(f"Fetching and logging information for {len(TEST_SYMBOLS)} initial symbols...")
news_logger.info("\n=== INITIAL SYMBOLS INFORMATION ===\n")
for symbol in TEST_SYMBOLS:
    symbol_info = get_symbol_info(symbol)
    if symbol_info:
        info_text = f"Symbol: {symbol}\n"
        info_text += f"Company: {symbol_info['name']}\n"
        info_text += f"Sector: {symbol_info['sector']}\n"
        info_text += f"Industry: {symbol_info['industry']}\n"
        info_text += f"Market Cap: {'{:,}'.format(symbol_info['market_cap']) if isinstance(symbol_info['market_cap'], (int, float)) else 'N/A'}\n"
        info_text += f"Avg Volume: {'{:,}'.format(symbol_info['average_volume']) if isinstance(symbol_info['average_volume'], (int, float)) else 'N/A'}\n"
        info_text += f"Description: {symbol_info['description']}"

        print(f"Information for {symbol}: {symbol_info['name']} ({symbol_info['sector']})")
        # Log the information right away
        news_logger.info(f"\n=== SYMBOL: {symbol} ===")
        news_logger.info(f"COMPANY INFO:\n{info_text}\n")
    else:
        print(f"Could not retrieve information for {symbol}")
        news_logger.info(f"\n=== SYMBOL: {symbol} ===")
        news_logger.info(f"COMPANY INFO: No information available\n")
news_logger.info("=== END OF INITIAL SYMBOLS INFORMATION ===\n")

# Configuration
TWS_HOST = "127.0.0.1"
TWS_PORT = 7497  # Using 7497 as shown in the output (paper trading port)
CLIENT_ID = random.randint(1000, 3000)  # Random client ID between 1000 and 3000
CONNECT_TIMEOUT = 15  # seconds - increased for more reliable connection
REQUEST_TIMEOUT = 30  # seconds
REALTIME_NEWS_DURATION = 60  # seconds to listen for real-time news

# Store the last time we checked for new symbols
LAST_SYMBOL_CHECK_TIME = time.time()
# How often to check for new symbols (in seconds)
SYMBOL_CHECK_INTERVAL = 60  # Check every minute

# Subset for real-time news - use all symbols for real-time monitoring
REALTIME_SYMBOLS = TEST_SYMBOLS.copy()


class NewsDataTester(EWrapper, EClient):
    """Class to test news data retrieval from IB API"""

    def __init__(self):
        EWrapper.__init__(self)
        EClient.__init__(self, self)

        # Request ID management
        self.next_req_id = 1000  # Start with a safe ID to avoid conflicts
        self.reqId_to_symbol = {}  # Map request IDs to symbols

        # Debug flag for raw message processing
        self.debug_raw_messages = False  # Set to False to reduce API message verbosity

        # Socket timeout configuration - increase for more stability
        self.socket_timeout = 5.0  # Set socket timeout to 5 seconds (default is 1 second)

        # Events for synchronization
        self.connected_event = threading.Event()
        self.news_providers_event = threading.Event()
        self.contract_details_events = {}
        self.historical_news_events = {}
        self.news_article_events = {}

        # Data storage
        self.news_providers = []
        self.contract_details = {}
        self.historical_news = {}
        self.news_articles = {}
        self.realtime_news = {}

        # Active subscriptions for cleanup
        self.active_market_data_reqs = set()

        # Error tracking
        self.errors = []

        # Debug mode
        self.debug_mode = True  # Set to True to enable more verbose logging

    def get_next_req_id(self):
        """Get the next available request ID"""
        req_id = self.next_req_id
        self.next_req_id += 1
        return req_id

    def connect(self, host, port, clientId):
        """Override connect method to set socket timeout after connection"""
        # Log attempt to connect with configured timeout
        logging.info(f"Connecting to {host}:{port} with client ID {clientId} (timeout: {self.socket_timeout}s)")

        # Call the parent class's connect method
        result = super().connect(host, port, clientId)

        # In newer versions of IBKR API, the socket connection may be encapsulated differently
        # Let's try to find where the socket object is accessible
        try:
            # Check if we have a socket attribute in the reader instance
            if hasattr(self, 'reader') and hasattr(self.reader, 'socket') and self.reader.socket is not None:
                logging.info(f"Setting socket timeout to {self.socket_timeout} seconds via reader.socket")
                self.reader.socket.settimeout(self.socket_timeout)
                logging.info(f"Socket timeout set successfully to {self.socket_timeout} seconds")
            # Check if we have a socket attribute directly
            elif hasattr(self, 'socket') and self.socket is not None:
                logging.info(f"Setting socket timeout to {self.socket_timeout} seconds via self.socket")
                self.socket.settimeout(self.socket_timeout)
                logging.info(f"Socket timeout set successfully to {self.socket_timeout} seconds")
            else:
                logging.warning("Could not find a socket object to set timeout configuration")
        except Exception as e:
            logging.error(f"Failed to set socket timeout: {e}")
            logging.error(f"Stack trace: {traceback.format_exc()}")

        return result

    def create_stock_contract(self, symbol):
        """Create a standard stock contract"""
        contract = Contract()
        contract.symbol = symbol
        contract.secType = "STK"
        contract.exchange = "SMART"
        contract.currency = "USD"
        return contract

    def create_news_contract(self, provider_code):
        """Create a contract for broadtape news"""
        contract = Contract()
        contract.symbol = f"{provider_code}:{provider_code}_ALL"
        contract.secType = "NEWS"
        contract.exchange = provider_code
        return contract

    def decoder(self, msg):
        """Override decoder to intercept and debug raw messages"""
        if self.debug_raw_messages:
            try:
                # Log the entire message for debugging
                logging.debug(f"RAW API MESSAGE LENGTH: {len(msg)} TYPE: {type(msg)}")

                # Check if message is binary and try to decode it
                if isinstance(msg, bytes):
                    try:
                        decoded = msg.decode('utf-8', errors='replace')

                        # Always log a sample of the decoded message for debugging
                        if len(decoded) > 200:
                            logging.debug(f"DECODED MESSAGE (first 200 chars): {decoded[:200]}...")
                        else:
                            logging.debug(f"DECODED MESSAGE: {decoded}")

                        # Log information that might help identify news data
                        if any(keyword in decoded for keyword in ["news", "News", "headline", "article", "DJNL", "BRFG", "DJ-N"]):
                            logging.info(f"POSSIBLE NEWS DATA FOUND: {decoded}")

                        # Look for specific message types with more exact patterns
                        if "85" in decoded and any(p in decoded for p in ["BRFG", "DJ-N", "DJNL"]):
                            logging.info(f"NEWSAPI: News providers response detected: {decoded}")
                            # Extract news provider data and manually process it if needed
                            try:
                                parts = decoded.split('-')
                                if len(parts) > 4:  # Make sure we have enough parts
                                    providers = []
                                    i = 2  # Start from index 2 where provider data begins
                                    while i < len(parts) - 1:
                                        if i+1 < len(parts):
                                            code = parts[i].strip()
                                            name = parts[i+1].strip()
                                            if code and name:
                                                providers.append((code, name))
                                                logging.info(f"Extracted provider: {code}: {name}")
                                            i += 2

                                    # If we found providers, manually set them
                                    if providers:
                                        self.news_providers = providers
                                        self.news_providers_event.set()
                                        logging.info(f"Manually processed {len(providers)} news providers")
                            except Exception as e:
                                logging.error(f"Failed to manually process news providers: {e}")
                        elif "tickNews" in decoded:
                            logging.info(f"NEWSAPI: Possible tickNews data: {decoded}")
                            # Try to extract tickNews data
                            try:
                                if "headline" in decoded:
                                    logging.info(f"NEWS HEADLINE FOUND: {decoded}")
                            except Exception as e:
                                logging.error(f"Error processing tickNews: {e}")

                        # Look for specific news-related numbers in the message
                        if any(code in decoded for code in ["!85", "!10", "!52", "tickNews"]):
                            logging.info(f"IMPORTANT NEWS-RELATED MESSAGE: {decoded}")
                    except Exception as e:
                        logging.debug(f"Could not decode as utf-8: {e}")
            except Exception as e:
                logging.error(f"Error logging raw message: {e}")

        # Call parent decoder
        result = super().decoder(msg)
        return result

    # Connection and initialization callbacks
    def connectAck(self):
        """Called when connection is established"""
        logging.info("Connection acknowledged by TWS/Gateway")
        # Set the connected event here - don't wait for nextValidId which may not come
        self.connected_event.set()

    def connectionClosed(self):
        """Called when connection is closed"""
        logging.warning("Connection to TWS/Gateway closed")

    def nextValidId(self, orderId):
        """Called when connection is ready for requests"""
        logging.info(f"Connection fully established. Next valid order ID: {orderId}")
        self.next_req_id = orderId
        # The connected_event is already set in connectAck, but set it again just in case
        self.connected_event.set()

        # Removed direct AAPL news subscription. Only NVDA (or TEST_SYMBOLS) will be monitored.
        # If you want to subscribe to a default symbol, ensure NVDA is in your TEST_SYMBOLS list.

        # Also try broadtape news directly using a known valid provider
        broadtape_req_id = orderId + 1
        self.reqId_to_symbol[broadtape_req_id] = "BROADTAPE_DJNL_DIRECT" # Changed from DJ-N
        # Using DJNL as it was listed as a valid provider in error messages
        broadtape_contract = self.create_news_contract("DJNL") 
        logging.info(f"Contract for broadtape news (DJNL): {broadtape_contract.symbol}, {broadtape_contract.secType}, {broadtape_contract.exchange}")
        self.reqMktData(
            reqId=broadtape_req_id,
            contract=broadtape_contract,
            genericTickList="mdoff,292",  # Generic tick for news
            snapshot=False,
            regulatorySnapshot=False,
            mktDataOptions=[]
        )
        self.active_market_data_reqs.add(broadtape_req_id)
        self.next_req_id = broadtape_req_id + 1

    def managedAccounts(self, accountsList):
        """Called when account information is received"""
        logging.info(f"Managed accounts: {accountsList}")
        # This is another confirmation that we're properly connected

    # Error handling
    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """Called when an error occurs"""
        # Format the error message
        error_msg = f"Error {errorCode}: {errorString}"

        # Check for connection-related errors
        if errorCode in [501, 502, 503, 504, 1100, 1101, 1102, 1300]:
            logging.error(f"Connection error: {error_msg}")
            # These are connection-related errors, set the connected event to prevent hanging
            self.connected_event.set()
            return

        # Check for API permission errors
        if errorCode in [100, 162, 200, 201]:
            logging.error(f"API permission error: {error_msg}")
            # These are permission-related errors, set the connected event to prevent hanging
            self.connected_event.set()
            return

        # Handle request-specific errors
        if reqId != -1:  # -1 is for system messages
            error_msg = f"Request {reqId}: {error_msg}"

            # Check if this is a request we're tracking
            symbol = self.reqId_to_symbol.get(reqId)
            if symbol:
                error_msg = f"Symbol {symbol}: {error_msg}"

                # Set events to prevent hanging on timeouts
                if symbol in self.contract_details_events:
                    self.contract_details_events[symbol].set()
                if symbol in self.historical_news_events:
                    self.historical_news_events[symbol].set()
                if symbol in self.news_article_events:
                    self.news_article_events[symbol].set()

        # Log the error
        logging.error(error_msg)
        if advancedOrderRejectJson:
            logging.error(f"Advanced reject info: {advancedOrderRejectJson}")

        # Store the error
        self.errors.append((reqId, errorCode, errorString))

    # News providers callbacks
    def newsProviders(self, newsProviders):
        """Called when news providers are received"""
        logging.info("News providers received:")

        if self.debug_mode:
            logging.debug(f"Raw news providers: {newsProviders}")

        try:
            # Print the raw data to help debug
            logging.debug(f"News providers type: {type(newsProviders)}")

            # Log the full raw data for detailed debugging
            logging.info(f"FULL NEWS PROVIDERS DATA: {newsProviders}")

            for provider in newsProviders:
                logging.info(f"  {provider.code}: {provider.name}")
                self.news_providers.append((provider.code, provider.name))

            if not newsProviders:
                logging.warning("Received empty news providers list")
                # Even if list is empty, use DJ-N as default
                self.news_providers.append(("DJ-N", "Dow Jones News Service"))
                logging.info("Added DJ-N as default news provider")

            # Set the event even if the list is empty
            self.news_providers_event.set()
        except Exception as e:
            logging.error(f"Error processing news providers: {e}")
            logging.error(f"Traceback: {traceback.format_exc()}")
            # Add default provider if we had an error
            self.news_providers.append(("DJ-N", "Dow Jones News Service"))
            logging.info("Added DJ-N as default news provider due to error")
            # Set the event to prevent hanging
            self.news_providers_event.set()

    # Contract details callbacks
    def contractDetails(self, reqId, contractDetails):
        """Called when contract details are received"""
        logging.info(f"Received contract details for reqId: {reqId}")
        if self.debug_mode:
            logging.debug(f"Raw contract details: {contractDetails}")

        symbol = self.reqId_to_symbol.get(reqId)
        if symbol:
            conId = contractDetails.contract.conId
            logging.info(f"Contract details for {symbol}: conId={conId}")
            self.contract_details[symbol] = contractDetails
        else:
            logging.warning(f"Received contract details for unknown reqId: {reqId}")

    def contractDetailsEnd(self, reqId):
        """Called when all contract details are received"""
        logging.info(f"Contract details request completed for reqId: {reqId}")

        symbol = self.reqId_to_symbol.get(reqId)
        if symbol:
            logging.info(f"Contract details request completed for {symbol}")
            if symbol in self.contract_details_events:
                self.contract_details_events[symbol].set()
        else:
            logging.warning(f"Contract details request completed for unknown reqId: {reqId}")

    # Historical news callbacks
    def historicalNews(self, reqId, time, providerCode, articleId, headline):
        """Called when historical news is received"""
        symbol = self.reqId_to_symbol.get(reqId)
        if symbol:
            if symbol not in self.historical_news:
                self.historical_news[symbol] = []

            # Format the time with human-readable relative age
            formatted_time = self.format_news_time(time)

            news_item = {
                'time': time,
                'formatted_time': formatted_time,
                'providerCode': providerCode,
                'articleId': articleId,
                'headline': headline
            }
            self.historical_news[symbol].append(news_item)
            logging.info(f"Historical news for {symbol}: {headline}")  # Existing log of raw headline

            # Check for sentiment information in the headline
            sentiment_marker = "NEUTRAL"
            confidence_value = 0.0
            headline_text = headline  # Default to full headline

            logging.debug(f"Processing headline for sentiment (historical): '{headline}'")  # ADDED

            # Extract the metadata if it exists
            # Pattern to match both simple and complex metadata formats
            metadata_match = re.search(r'{A:[^:]*:L:[^:]*:K:([^:]+):C:([0-9.]+)}(.*)', headline)
            if metadata_match:
                logging.debug("Primary sentiment regex MATCHED (historical)")  # ADDED
                # With updated regex, we only capture sentiment, confidence, and text (3 groups)
                sentiment_key_from_headline, confidence, news_text = metadata_match.groups()  # Renamed 'sentiment' to 'sentiment_key_from_headline'
                try:
                    confidence_value = float(confidence)
                    # Determine sentiment label - ALIGNED WITH tickNews LOGIC
                    if sentiment_key_from_headline == "n/a":
                        sentiment_marker = "NEUTRAL"
                    else:
                        try:
                            s_val = float(sentiment_key_from_headline)
                            if s_val > 0:
                                sentiment_marker = "BULL"
                            elif s_val < 0:
                                sentiment_marker = "BEAR"
                            else:  # s_val == 0.0
                                sentiment_marker = "NEUTRAL"
                        except ValueError:
                            logging.warning(f"Unrecognized non-numeric sentiment_key '{sentiment_key_from_headline}' in headline metadata for {symbol} (historical). Defaulting to NEUTRAL.")
                            sentiment_marker = "NEUTRAL"

                    # Use only the news text without metadata
                    headline_text = news_text.strip()
                except (ValueError, TypeError):
                    logging.warning(f"Could not parse confidence from '{confidence}' or process sentiment_key '{sentiment_key_from_headline}' (historical)")
                    headline_text = headline  # Fallback if conversion fails
            else:
                logging.debug("Primary sentiment regex FAILED (historical). Trying simple match.")  # ADDED
                # If no metadata match, check for simpler metadata format
                simple_metadata = re.search(r'{.*?}(.*)', headline)
                if simple_metadata:
                    logging.debug("Simple sentiment regex MATCHED (historical)")  # ADDED
                    headline_text = simple_metadata.group(1).strip()
                else:
                    logging.debug("Simple sentiment regex FAILED (historical)")  # ADDED
                    headline_text = headline  # Ultimate fallback

            # Extract just the relative age portion from the formatted time
            relative_age = self.extract_relative_age(formatted_time)

            # Check if news is older than 48 hours before logging
            if self.is_news_within_48_hours(time):
                # Add emoji for BULL/BEAR
                emoji = ""
                if sentiment_marker == "BULL":
                    emoji = " 🚀"
                elif sentiment_marker == "BEAR":
                    emoji = " 🐻"
                # Get current Eastern time (hour and minute only)
                eastern = pytz.timezone('US/Eastern')
                current_time_eastern = datetime.datetime.now(eastern)
                current_time_str = current_time_eastern.strftime("%I:%M %p")
                
                # Format first line with current time before relative age
                first_line = f"{symbol} {current_time_str} ({relative_age})  {sentiment_marker} (confidence: {confidence_value:.2f}){emoji}"
                # Indent news text
                indented_headline = f"    {headline_text}"
                news_logger.info(f"{first_line}\n{indented_headline}")
            else:
                logging.debug(f"Skipping news older than 48 hours for {symbol}: {headline_text}")

    def historicalNewsEnd(self, reqId, hasMore):
        """Called when all historical news is received"""
        symbol = self.reqId_to_symbol.get(reqId)
        if symbol:
            logging.info(f"Historical news request completed for {symbol} (has more: {hasMore})")
            if symbol in self.historical_news_events:
                self.historical_news_events[symbol].set()

    # News article callbacks
    def newsArticle(self, reqId, articleType, articleText):
        """Called when a news article is received"""
        symbol = self.reqId_to_symbol.get(reqId)
        if symbol:
            self.news_articles[symbol] = {
                'articleType': articleType,
                'articleText': articleText
            }
            logging.info(f"News article received for {symbol} (type: {articleType})")
            if symbol in self.news_article_events:
                self.news_article_events[symbol].set()

    # Real-time news callbacks
    def tickNews(self, tickerId, timeStamp, providerCode, articleId, headline, extraData):
        """Called when real-time news is received"""
        # Log all raw data to help with debugging
        logging.info("========== NEWS TICK RECEIVED ==========")
        logging.info(f"tickerId: {tickerId}")
        logging.info(f"timeStamp: {timeStamp}")
        logging.info(f"providerCode: {providerCode}")
        logging.info(f"articleId: {articleId}")
        logging.info(f"headline: {headline}")
        logging.info(f"extraData: {extraData}")
        logging.info("=======================================")

        symbol = self.reqId_to_symbol.get(tickerId)
        if symbol:
            logging.info(f"News is for symbol: {symbol}")
        else:
            logging.info(f"News is broadtape or unknown tickerId: {tickerId}")
            # If we don't have a mapping, try to find the symbol in the headline
            for sym in TEST_SYMBOLS:
                if sym in headline:
                    symbol = sym
                    logging.info(f"Found symbol {sym} in headline")
                    break

            # If still no symbol, mark as broadtape
            if not symbol:
                symbol = "BROADTAPE"

        # Format timestamp with human-readable relative age in Eastern Time
        try:
            formatted_time = self.format_news_time(timeStamp)
        except Exception as e:
            logging.warning(f"Error converting timestamp {timeStamp}: {e}")
            formatted_time = f"Raw timestamp: {timeStamp}"

        # Initialize sentiment and headline text
        sentiment_marker = "NEUTRAL"
        confidence_value = 0.0
        headline_text = headline  # Use the original headline directly, assuming metadata is in extraData

        logging.debug(f"Attempting to parse sentiment from extraData (tickNews): '{extraData}'")

        if extraData:
            # Regex for extraData: A:articleId:L:language:K:sentimentKey:C:confidence
            # Example extraData: "A:800015:L:en:K:n/a:C:0.9956571459770203"
            # No curly braces are expected in the extraData string itself.
            extradata_sentiment_match = re.search(r'A:[^:]*:L:[^:]*:K:([^:]+):C:([0-9.]+)', extraData)
            if extradata_sentiment_match:
                logging.debug("Sentiment regex MATCHED extraData (tickNews)")
                sentiment_key_from_extra, confidence_str_from_extra = extradata_sentiment_match.groups()
                try:
                    confidence_value = float(confidence_str_from_extra)
                    # Determine sentiment label based on sentiment_key_from_extra
                    if sentiment_key_from_extra == "n/a":
                        sentiment_marker = "NEUTRAL"
                    else:
                        try:
                            s_val = float(sentiment_key_from_extra) # Try to convert K value to float
                            if s_val > 0:
                                sentiment_marker = "BULL"
                            elif s_val < 0:
                                sentiment_marker = "BEAR"
                            else: # s_val == 0.0
                                sentiment_marker = "NEUTRAL"
                        except ValueError:
                            # If K value is not "n/a" and not a float (e.g. some other text)
                            logging.warning(f"Unrecognized non-numeric sentiment_key '{sentiment_key_from_extra}' in extraData. Defaulting to NEUTRAL.")
                            sentiment_marker = "NEUTRAL"
                except (ValueError, TypeError) as e:
                    logging.warning(f"Could not parse confidence '{confidence_str_from_extra}' or process sentiment_key '{sentiment_key_from_extra}' from extraData: {e}")
                    # sentiment_marker and confidence_value remain default NEUTRAL, 0.0
            else:
                logging.debug("Sentiment regex FAILED to match extraData (tickNews). No sentiment parsed from extraData.")
        else:
            logging.debug("extraData is empty or None. No sentiment parsed from extraData.")

        # Extract just the relative age portion from the formatted time
        relative_age = self.extract_relative_age(formatted_time)

        # Check if news is within 48 hours before logging
        if self.is_news_within_48_hours(timeStamp):
            # Add emoji for BULL/BEAR
            emoji = ""
            if sentiment_marker == "BULL":
                emoji = " 🚀"
            elif sentiment_marker == "BEAR":
                emoji = " 🐻"
            # Format first line (remove ===, keep rest)
            # Get current Eastern time (hour and minute only)
            eastern = pytz.timezone('US/Eastern')
            current_time_eastern = datetime.datetime.now(eastern)
            current_time_str = current_time_eastern.strftime("%I:%M %p")
            
            first_line = f"{symbol} {current_time_str} ({relative_age})  {sentiment_marker} (confidence: {confidence_value:.2f}){emoji}"
            # Indent news text
            indented_headline = f"    {headline_text}"
            # Log to the dedicated news log
            if symbol == "BROADTAPE":
                news_logger.info(f"{first_line}\n{indented_headline}")
            else:
                news_logger.info(f"{first_line}\n{indented_headline}")
        else:
            logging.debug(f"Skipping news older than 48 hours for {symbol if symbol != 'BROADTAPE' else 'BROADTAPE'}: {headline_text}")

        # Create a standardized news item dictionary
        news_item = {
            'time': formatted_time,
            'providerCode': providerCode,
            'articleId': articleId,
            'headline': headline,
            'extraData': extraData
        }

        # Store the news item
        if symbol not in self.realtime_news:
            self.realtime_news[symbol] = []
        self.realtime_news[symbol].append(news_item)

        # Log the news based on whether it's for a specific symbol or broadtape
        if symbol == "BROADTAPE":
            logging.info(f"Broadtape news: {headline} (Provider: {providerCode})")
        else:
            logging.info(f"Real-time news for {symbol}: {headline}")

        # Also check if this is actually a historical news item
        # Sometimes historical news comes through the tickNews path
        if "historical" in extraData.lower() or "history" in extraData.lower():
            if symbol not in self.historical_news:
                self.historical_news[symbol] = []
            self.historical_news[symbol].append(news_item)
            logging.info(f"Stored as historical news for {symbol}")

        # Request the full article text for interesting headlines
        if any(keyword in headline.lower() for keyword in ["earnings", "launch", "release", "major", "announces"]):
            req_id = self.get_next_req_id()
            self.reqId_to_symbol[req_id] = f"{symbol}_ARTICLE"
            logging.info(f"Requesting full article for interesting headline: {headline}")
            self.reqNewsArticle(req_id, providerCode, articleId, [])

    def tickString(self, reqId, tickType, value):
        """Called for string tick types"""
        symbol = self.reqId_to_symbol.get(reqId, "Unknown")
        if tickType == TickTypeEnum.NEWS:
            logging.info(f"NEWS TICK received for {symbol}: {value}")
            try:
                parts = value.split(";")
                if len(parts) >= 3:
                    time_str = parts[0]
                    provider_code = parts[1]
                    headline = parts[2]
                    logging.info(f"Parsed NEWS TICK: Time: {time_str}, Provider: {provider_code}, Headline: {headline}")  # Existing log of raw headline
                    formatted_time = self.format_news_time(time_str)
                    sentiment_marker = "NEUTRAL"
                    confidence_value = 0.0
                    headline_text = headline  # Default

                    logging.debug(f"Processing headline for sentiment (tickString): '{headline}'")  # ADDED
                    # Try to extract the metadata if it exists
                    metadata_match = re.search(r'{A:[^:]*:L:[^:]*:K:([^:]+):C:([0-9.]+)}(.*)', headline)
                    if metadata_match:
                        logging.debug("Primary sentiment regex MATCHED (tickString)")  # ADDED
                        sentiment_key_from_headline, confidence, news_text = metadata_match.groups()  # Renamed 'sentiment' to 'sentiment_key_from_headline'
                        try:
                            confidence_value = float(confidence)
                            # Determine sentiment label - ALIGNED WITH tickNews LOGIC
                            if sentiment_key_from_headline == "n/a":
                                sentiment_marker = "NEUTRAL"
                            else:
                                try:
                                    s_val = float(sentiment_key_from_headline)
                                    if s_val > 0:
                                        sentiment_marker = "BULL"
                                    elif s_val < 0:
                                        sentiment_marker = "BEAR"
                                    else:  # s_val == 0.0
                                        sentiment_marker = "NEUTRAL"
                                except ValueError:
                                    logging.warning(f"Unrecognized non-numeric sentiment_key '{sentiment_key_from_headline}' in headline metadata for {symbol} (tickString). Defaulting to NEUTRAL.")
                                    sentiment_marker = "NEUTRAL"
                            headline_text = news_text.strip()
                        except (ValueError, TypeError):
                            logging.warning(f"Could not parse confidence from '{confidence}' or process sentiment_key '{sentiment_key_from_headline}' (tickString)")
                            headline_text = headline  # Fallback
                    else:
                        logging.debug("Primary sentiment regex FAILED (tickString). Trying simple match.")  # ADDED
                        # If no metadata match, check for simpler metadata format
                        simple_metadata = re.search(r'{.*?}(.*)', headline)
                        if simple_metadata:
                            logging.debug("Simple sentiment regex MATCHED (tickString)")  # ADDED
                            headline_text = simple_metadata.group(1).strip()
                        else:
                            logging.debug("Simple sentiment regex FAILED (tickString)")  # ADDED
                            headline_text = headline  # Ultimate fallback

                    # Extract just the relative age portion from the formatted time
                    relative_age = self.extract_relative_age(formatted_time)

                    if self.is_news_within_48_hours(time_str):
                        # Add emoji for BULL/BEAR
                        emoji = ""
                        if sentiment_marker == "BULL":
                            emoji = " 🚀"
                        elif sentiment_marker == "BEAR":
                            emoji = " 🐻"
                        # Get current Eastern time (hour and minute only)
                        eastern = pytz.timezone('US/Eastern')
                        current_time_eastern = datetime.datetime.now(eastern)
                        current_time_str = current_time_eastern.strftime("%I:%M %p")
                        
                        # Format first line with current time before relative age
                        first_line = f"{symbol} {current_time_str} ({relative_age})  {sentiment_marker} (confidence: {confidence_value:.2f}){emoji}"
                        # Indent news text
                        indented_headline = f"    {headline_text}"
                        news_logger.info(f"{first_line}\n{indented_headline}")
                    else:
                        logging.debug(f"Skipping news older than 48 hours for {symbol}: {headline_text}")
                    if symbol not in self.realtime_news:
                        self.realtime_news[symbol] = []
                    news_item = {
                        'time': formatted_time,
                        'providerCode': provider_code,
                        'articleId': "UNKNOWN",
                        'headline': headline,
                        'extraData': value
                    }
                    self.realtime_news[symbol].append(news_item)
            except Exception as e:
                logging.error(f"Error parsing news tick: {e}")
        elif self.debug_mode:
            tick_type_str = TickTypeEnum.to_str(tickType)
            logging.debug(f"Tick String for {symbol}: Type={tick_type_str} Value={value}")

    def extract_relative_age(self, formatted_time):
        """Extract just the relative age portion from a formatted time string

        For example, from "2025-05-21 10:30:00 PM ET (2 hours ago)" returns "2 hours ago"
        """
        try:
            # Extract the portion between parentheses
            match = re.search(r'\((.*?)\)', formatted_time)
            if match:
                return match.group(1)
            return "recent"
        except:
            return "recent"

    def is_news_within_48_hours(self, time_str):
        """Check if news timestamp is within the last 48 hours

        Args:
            time_str: Timestamp in any format handled by format_news_time

        Returns:
            bool: True if the news is less than 48 hours old, False otherwise
        """
        try:
            # Parse the timestamp using the same logic as in format_news_time
            eastern = pytz.timezone('US/Eastern')
            now_eastern = datetime.datetime.now(eastern)

            # Try to parse the timestamp - IB has different formats
            parsed_time = None

            # Case 1: YYYYMMDD-HHMMSS numerical format (common in tickNews)
            if isinstance(time_str, int) or (isinstance(time_str, str) and str(time_str).isdigit()):
                time_str = str(time_str)
                if len(time_str) >= 14:  # YYYYMMDD-HHMMSS
                    year = int(time_str[0:4])
                    month = int(time_str[4:6])
                    day = int(time_str[6:8])
                    hour = int(time_str[8:10])
                    minute = int(time_str[10:12])
                    second = int(time_str[12:14])
                    parsed_time = datetime.datetime(year, month, day, hour, minute, second, tzinfo=eastern)
            elif isinstance(time_str, str):
                # Case 2: String timestamp in various formats
                parsed_time = parser.parse(time_str)
                if parsed_time.tzinfo is None:
                    parsed_time = parsed_time.replace(tzinfo=eastern)

            if parsed_time is None:
                # If we couldn't parse, assume it's recent to be safe
                return True

            # Calculate the difference
            time_difference = now_eastern - parsed_time

            # Check if it's less than 48 hours old
            return time_difference.total_seconds() < (48 * 3600)  # 48 hours in seconds
        except Exception as e:
            logging.warning(f"Error checking news age: {e}, assuming within 48 hours")
            return True  # Default to keeping the news if we can't parse the time

    def format_news_time(self, time_str):
        """Format a news timestamp into human-readable format with relative age in Eastern Time"""
        eastern = pytz.timezone('US/Eastern')
        now_eastern = datetime.datetime.now(eastern)
        try:
            parsed_time = None
            if isinstance(time_str, int) or (isinstance(time_str, str) and str(time_str).isdigit()):
                time_str = str(time_str)
                if len(time_str) >= 13:
                    try:
                        timestamp_sec = int(time_str) / 1000
                        parsed_time = datetime.datetime.fromtimestamp(timestamp_sec, pytz.UTC)
                        if "1747" in time_str[:4] or time_str.startswith("17"):
                            parsed_time = datetime.datetime.now(pytz.UTC) - datetime.timedelta(minutes=5)
                    except (ValueError, OverflowError):
                        try:
                            date_str = time_str[:8]
                            tstr = time_str[8:14]
                            year = int(date_str[:4])
                            month = int(date_str[4:6])
                            day = int(date_str[6:8])
                            hour = int(tstr[:2]) if len(tstr) >= 2 else 0
                            minute = int(tstr[2:4]) if len(tstr) >= 4 else 0
                            second = int(tstr[4:6]) if len(tstr) >= 6 else 0
                            parsed_time = datetime.datetime(year, month, day, hour, minute, second)
                        except Exception:
                            pass
                elif len(time_str) >= 8:
                    try:
                        if '-' in time_str:
                            date_part, time_part = time_str.split('-')
                        else:
                            date_part = time_str[:8]
                            time_part = time_str[8:] if len(time_str) > 8 else "000000"
                        year = int(date_part[:4])
                        month = int(date_part[4:6])
                        day = int(date_part[6:8])
                        hour = int(time_part[:2]) if len(time_part) >= 2 else 0
                        minute = int(time_part[2:4]) if len(time_part) >= 4 else 0
                        second = int(time_part[4:6]) if len(time_part) >= 6 else 0
                        parsed_time = datetime.datetime(year, month, day, hour, minute, second)
                    except Exception:
                        pass
            elif isinstance(time_str, str):
                try:
                    parsed_time = parser.parse(time_str)
                except Exception:
                    pass
            if parsed_time:
                if not parsed_time.tzinfo:
                    parsed_time = pytz.UTC.localize(parsed_time)
                parsed_time_eastern = parsed_time.astimezone(eastern)
                time_diff = now_eastern - parsed_time_eastern
                if time_diff.total_seconds() < 60:
                    relative_age = "just now"
                elif time_diff.total_seconds() < 3600:
                    minutes = int(time_diff.total_seconds() / 60)
                    relative_age = f"{minutes} {'minute' if minutes == 1 else 'minutes'} ago"
                elif time_diff.total_seconds() < 86400:
                    hours = int(time_diff.total_seconds() / 3600)
                    relative_age = f"{hours} {'hour' if hours == 1 else 'hours'} ago"
                else:
                    days = int(time_diff.total_seconds() / 86400)
                    relative_age = f"{days} {'day' if days == 1 else 'days'} ago"
                formatted_time = parsed_time_eastern.strftime("%Y-%m-%d %I:%M:%S %p ET")
                return f"{formatted_time} ({relative_age})"
            offset_time = now_eastern - datetime.timedelta(minutes=5)
            formatted = offset_time.strftime("%Y-%m-%d %I:%M:%S %p ET")
            return f"{formatted} (recent)"
        except Exception as e:
            logging.debug(f"Error formatting timestamp '{time_str}': {e}")
            offset_time = now_eastern - datetime.timedelta(minutes=5)
            formatted = offset_time.strftime("%Y-%m-%d %I:%M:%S %p ET")
            return f"{formatted} (recent)"

    def check_for_new_symbols(self):
        """Check for new symbols in the monitoring log and update subscriptions if needed"""
        # Use global variables but declare them properly
        global LAST_SYMBOL_CHECK_TIME
        global CURRENT_MONITORING_LOG

        # Check if we're connected first
        if not self.isConnected():
            logging.warning("Cannot check for new symbols: Not connected to TWS/Gateway")
            return False

        current_time = time.time()
        # Only check if enough time has passed since the last check
        if current_time - LAST_SYMBOL_CHECK_TIME < SYMBOL_CHECK_INTERVAL:
            return False

        LAST_SYMBOL_CHECK_TIME = current_time
        logging.info("Checking for new symbols in monitoring log...")

        try:
            # Get the latest monitoring log
            latest_log = get_latest_monitoring_log()

            # If we can't find the latest log, try to recover by searching for a new file
            if not latest_log:
                logging.warning("No monitoring log found to check for new symbols. Attempting recovery...")
                home_dir = os.path.expanduser("~")
                logs_dir = os.path.join(home_dir, "Scan7", "logs", "live")
                monitoring_logs = []
                for filename in os.listdir(logs_dir):
                    if filename.startswith("scan_orb_") and "_monitoring.log" in filename:
                        file_path = os.path.join(logs_dir, filename)
                        monitoring_logs.append((file_path, os.path.getmtime(file_path)))
                monitoring_logs.sort(key=lambda x: x[1], reverse=True)
                if monitoring_logs:
                    latest_log = monitoring_logs[0][0]
                    logging.info(f"Recovered new monitoring log: {os.path.basename(latest_log)}")
                    CURRENT_MONITORING_LOG = latest_log
                else:
                    logging.error("Recovery failed: No monitoring log found after swap.")
                    return False

            # Check if the monitoring log has changed since last check
            if 'CURRENT_MONITORING_LOG' in globals() and CURRENT_MONITORING_LOG != latest_log:
                logging.info(f"Monitoring log has changed! Previous: {os.path.basename(CURRENT_MONITORING_LOG)}, Current: {os.path.basename(latest_log)}")
                news_logger.info(f"\n=== MONITORING LOG CHANGED: {os.path.basename(latest_log)} ===\n")

            # Update the current log reference
            CURRENT_MONITORING_LOG = latest_log

            # Try to get symbols from the log
            log_symbols = extract_monitored_symbols(latest_log)

            # If no symbols found in the primary log, try looking at other recent logs
            if not log_symbols:
                logging.info("No symbols found in primary monitoring log, checking other recent logs...")
                try:
                    home_dir = os.path.expanduser("~")
                    logs_dir = os.path.join(home_dir, "Scan7", "logs", "live")
                    recent_logs = []
                    for filename in os.listdir(logs_dir):
                        if filename.endswith(".log"):
                            file_path = os.path.join(logs_dir, filename)
                            if time.time() - os.path.getmtime(file_path) < 3600:  # 1 hour
                                recent_logs.append(file_path)
                    for log_path in recent_logs:
                        if log_path != latest_log:
                            backup_symbols = extract_monitored_symbols(log_path)
                            if backup_symbols:
                                logging.info(f"Found {len(backup_symbols)} symbols in backup log: {os.path.basename(log_path)}")
                                log_symbols = backup_symbols
                                break
                except Exception as backup_err:
                    logging.error(f"Error while searching backup logs: {backup_err}")

            if not log_symbols:
                logging.info("No symbols found in any monitoring logs")
                return False

            # Combine with default symbol
            new_symbols = list(set([DEFAULT_SYMBOL] + log_symbols))

            # Check if any new symbols were found
            new_additions = [sym for sym in new_symbols if sym not in TEST_SYMBOLS]
            if not new_additions:
                logging.info("No new symbols found in monitoring log")
                return False

            # Update the symbol lists
            logging.info(f"Found {len(new_additions)} new symbols to monitor: {', '.join(new_additions)}")
            global TEST_SYMBOLS, REALTIME_SYMBOLS
            TEST_SYMBOLS = new_symbols
            REALTIME_SYMBOLS = new_symbols.copy()

            # Get additional information for the new symbols
            for symbol in new_additions:
                # Fetch company information for the new symbol
                news_logger.info(f"\n=== NEW SYMBOL ADDED: {symbol} ===")
                logging.info(f"Fetching company information for new symbol: {symbol}")

                try:
                    symbol_info = get_symbol_info(symbol)
                    if symbol_info:
                        info_text = f"Symbol: {symbol}\n"
                        info_text += f"Company: {symbol_info['name']}\n"
                        info_text += f"Sector: {symbol_info['sector']}\n"
                        info_text += f"Industry: {symbol_info['industry']}\n"
                        info_text += f"Market Cap: {'{:,}'.format(symbol_info['market_cap']) if isinstance(symbol_info['market_cap'], (int, float)) else 'N/A'}\n"
                        info_text += f"Avg Volume: {'{:,}'.format(symbol_info['average_volume']) if isinstance(symbol_info['average_volume'], (int, float)) else 'N/A'}\n"
                        info_text += f"Description: {symbol_info['description']}"

                        # Log to both regular log and news log
                        logging.info(f"Company information for {symbol}:\n{info_text}")
                        news_logger.info(f"COMPANY INFO:\n{info_text}\n")
                    else:
                        logging.warning(f"Could not retrieve company information for {symbol}")
                        news_logger.info(f"No company information available for {symbol}\n")
                except Exception as e:
                    logging.error(f"Error retrieving company information for {symbol}: {e}")
                    news_logger.info(f"Error retrieving company information for {symbol}: {e}\n")

            # Only proceed with these steps if we're still connected
            if not self.isConnected():
                logging.warning("Lost connection while processing new symbols")
                return False

            # Get contract details for the new symbols
            for symbol in new_additions:
                try:
                    reqId = self.get_next_req_id()
                    self.reqId_to_symbol[reqId] = symbol
                    contract = self.create_stock_contract(symbol)

                    logging.info(f"Requesting contract details for new symbol: {symbol} (reqId: {reqId})")
                    self.reqContractDetails(reqId, contract)

                    # Create an event for this contract details request
                    self.contract_details_events[reqId] = threading.Event()

                    # Wait for a short timeout for contract details
                    self.contract_details_events[reqId].wait(timeout=2)
                except Exception as e:
                    logging.error(f"Error requesting contract details for {symbol}: {e}")

            # Only proceed with subscriptions if we're still connected
            if not self.isConnected():
                logging.warning("Lost connection while getting contract details")
                return False

            # Subscribe to news for the new symbols
            try:
                self.test_realtime_news(new_symbols=new_additions)
            except Exception as e:
                logging.error(f"Error subscribing to news for new symbols: {e}")

            return True

        except Exception as e:
            logging.error(f"Error in check_for_new_symbols: {e}")
            logging.error(f"Traceback: {traceback.format_exc()}")
            return False

    def test_realtime_news(self, new_symbols=None):
        """Subscribe to real-time news for the specified symbols or all TEST_SYMBOLS"""
        if not self.isConnected():
            logging.error("Cannot subscribe to real-time news: Not connected to TWS/Gateway")
            return

        # Determine which symbols to use
        symbols_to_use = new_symbols if new_symbols else REALTIME_SYMBOLS
        if not symbols_to_use:
            logging.warning("No symbols to subscribe for real-time news")
            return

        logging.info(f"Subscribing to real-time news for {len(symbols_to_use)} symbols: {', '.join(symbols_to_use)}")

        # Subscribe to news for each symbol
        for symbol in symbols_to_use:
            try:
                # Create a contract for the symbol
                contract = self.create_stock_contract(symbol)

                # Get a request ID and map it to the symbol
                req_id = self.get_next_req_id()
                self.reqId_to_symbol[req_id] = symbol

                # Subscribe to market data with news generic tick
                logging.info(f"Requesting market data with news for {symbol} (reqId: {req_id})")
                self.reqMktData(
                    reqId=req_id,
                    contract=contract,
                    genericTickList="mdoff,292",  # 292 is the generic tick for news
                    snapshot=False,
                    regulatorySnapshot=False,
                    mktDataOptions=[]
                )

                # Add to active requests for cleanup later
                self.active_market_data_reqs.add(req_id)

                # Small delay to avoid overwhelming the API
                time.sleep(0.1)
            except Exception as e:
                logging.error(f"Error subscribing to news for {symbol}: {e}")

        # Also subscribe to broadtape news from major providers
        try:
            # Wait for news providers if we don't have them yet
            if not self.news_providers:
                logging.info("Requesting news providers...")
                self.reqNewsProviders()
                self.news_providers_event.wait(timeout=5)

            # If we still don't have providers, use a list of known valid ones
            if not self.news_providers:
                logging.warning("No news providers received, using default list of known valid providers.")
                # Valid providers based on error message: [BRFG, BRFUPDN, DJ, DJNL, BZ, DJTOP]
                self.news_providers = [
                    ("DJNL", "Dow Jones Newsletters"),
                    ("BRFG", "Briefing.com"),
                    ("BZ", "Benzinga Pro") # Added BZ as an example, can add others
                ]
            else:
                # Filter current providers to only include known valid ones if possible
                valid_provider_codes = ["BRFG", "BRFUPDN", "DJ", "DJNL", "BZ", "DJTOP"]
                original_provider_count = len(self.news_providers)
                self.news_providers = [p for p in self.news_providers if p[0] in valid_provider_codes]
                if len(self.news_providers) < original_provider_count:
                    logging.info(f"Filtered news providers to include only known valid codes. Original: {original_provider_count}, Filtered: {len(self.news_providers)}")
                if not self.news_providers: # If filtering removed all, fall back to default list
                    logging.warning("Filtering removed all existing providers. Falling back to default list of known valid providers.")
                    self.news_providers = [
                        ("DJNL", "Dow Jones Newsletters"),
                        ("BRFG", "Briefing.com"),
                        ("BZ", "Benzinga Pro")
                    ]

            # Subscribe to broadtape news for each provider
            for provider_code, provider_name in self.news_providers:
                try:
                    # Create a news contract for the provider
                    news_contract = self.create_news_contract(provider_code)

                    # Get a request ID
                    req_id = self.get_next_req_id()
                    self.reqId_to_symbol[req_id] = f"BROADTAPE_{provider_code}"

                    # Subscribe to market data for the news contract
                    logging.info(f"Subscribing to broadtape news from {provider_name} ({provider_code})")
                    self.reqMktData(
                        reqId=req_id,
                        contract=news_contract,
                        genericTickList="mdoff,292",
                        snapshot=False,
                        regulatorySnapshot=False,
                        mktDataOptions=[]
                    )

                    # Add to active requests
                    self.active_market_data_reqs.add(req_id)

                    # Small delay
                    time.sleep(0.1)
                except Exception as e:
                    logging.error(f"Error subscribing to broadtape news from {provider_name}: {e}")
        except Exception as e:
            logging.error(f"Error setting up broadtape news subscriptions: {e}")

    def cleanup(self):
        """Clean up all active subscriptions and disconnect"""
        logging.info("Cleaning up active subscriptions...")

        # Cancel all active market data requests
        for req_id in self.active_market_data_reqs:
            try:
                logging.info(f"Canceling market data request {req_id}")
                self.cancelMktData(req_id)
            except Exception as e:
                logging.error(f"Error canceling market data request {req_id}: {e}")

        # Clear the set of active requests
        self.active_market_data_reqs.clear()

        # Disconnect from TWS/Gateway
        if self.isConnected():
            logging.info("Disconnecting from TWS/Gateway")
            self.disconnect()


# Function to check and install yfinance if needed
def check_and_install_yfinance():
    """Check if yfinance is installed and install it if needed"""
    global yf

    if yf is None:
        try:
            logging.info("yfinance module not found, attempting to install...")
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "yfinance"])
            import yfinance as yf
            logging.info("Successfully installed yfinance")
        except Exception as e:
            logging.error(f"Failed to install yfinance: {e}")
            logging.error("Will continue without yfinance functionality")


# Main function to run the news data tester
def run_news_tester():
    """Main function to run the news data tester"""
    # Initialize variables for the main loop
    app = None
    running = True
    last_check_time = time.time()
    check_interval = 60  # Check for new symbols every 60 seconds

    try:
        # Check if yfinance is installed
        check_and_install_yfinance()

        # Create and initialize the app
        app = NewsDataTester()

        # Connect to TWS/Gateway
        logging.info(f"Connecting to TWS/Gateway at {TWS_HOST}:{TWS_PORT} (client ID: {CLIENT_ID})...")
        app.connect(TWS_HOST, TWS_PORT, CLIENT_ID)

        # Wait for connection to be established
        if not app.connected_event.wait(timeout=CONNECT_TIMEOUT):
            logging.error(f"Failed to connect to TWS/Gateway within {CONNECT_TIMEOUT} seconds")
            return

        logging.info("Successfully connected to TWS/Gateway")

        # Request news providers
        logging.info("Requesting news providers...")
        app.reqNewsProviders()

        # Wait for news providers
        if not app.news_providers_event.wait(timeout=REQUEST_TIMEOUT):
            logging.warning(f"Did not receive news providers within {REQUEST_TIMEOUT} seconds")
            # Continue anyway, we'll use default providers

        # Subscribe to real-time news for all symbols
        app.test_realtime_news()

        # Main loop to keep the program running and check for new symbols
        logging.info("Entering main loop to monitor news...")
        news_logger.info("\n=== NEWS MONITORING STARTED ===\n")

        print("\nNews monitoring is now active. Press Ctrl+C to exit.")
        print("News will be logged to:", news_log_path)

        while running:
            try:
                # Process messages from TWS/Gateway
                app.run()

                # Check for new symbols periodically
                current_time = time.time()
                if current_time - last_check_time > check_interval:
                    app.check_for_new_symbols()
                    last_check_time = current_time

                # Small delay to prevent high CPU usage
                time.sleep(0.1)

            except KeyboardInterrupt:
                logging.info("Keyboard interrupt received, exiting...")
                running = False
            except Exception as e:
                logging.error(f"Error in main loop: {e}")
                logging.error(f"Traceback: {traceback.format_exc()}")
                # Continue running despite errors
                time.sleep(1)  # Add a delay to prevent rapid error loops

    except KeyboardInterrupt:
        logging.info("Keyboard interrupt received during initialization, exiting...")
    except Exception as e:
        logging.error(f"Error during initialization: {e}")
        logging.error(f"Traceback: {traceback.format_exc()}")
    finally:
        # Clean up and disconnect
        if app and app.isConnected():
            try:
                app.cleanup()
                logging.info("Cleanup completed")
            except Exception as e:
                logging.error(f"Error during cleanup: {e}")

        logging.info("News monitoring terminated")
        news_logger.info("\n=== NEWS MONITORING ENDED ===\n")
        print("\nNews monitoring has ended. Check the logs for details.")


# Entry point
if __name__ == "__main__":
    # Initialize global variable for current monitoring log
    CURRENT_MONITORING_LOG = None

    # Run the news tester
    run_news_tester()
