# Enhanced Symbol Price Data Format

## Overview
This document describes the enhanced `symbol_prices` format that now supports bid/ask data for improved breakout logging.

## Enhanced Format

### Before (Original)
```python
symbol_prices = {
    'AAPL': {
        'price': 150.25,
        'volume': 1000000
    }
}
```

### After (Enhanced - Backward Compatible)
```python
symbol_prices = {
    'AAPL': {
        'price': 150.25,          # Required
        'volume': 1000000,        # Required
        'bid': 150.24,            # Optional - NEW
        'ask': 150.26,            # Optional - NEW
        'bid_size': 500,          # Optional - NEW
        'ask_size': 300           # Optional - NEW
    }
}
```

## Implementation Guide

### Low Risk Implementation
1. **All new fields are optional** - existing code continues to work
2. **Graceful degradation** - if bid/ask not available, CSV logs empty values
3. **Backward compatible** - old data feeds work without changes

### Data Source Enhancement Examples

#### Option 1: From API Response
```python
def parse_tick_data(api_response):
    """Parse enhanced tick data from your data provider"""
    symbol_prices = {}
    
    for symbol, data in api_response.items():
        symbol_prices[symbol] = {
            'price': data['last'],           # Required
            'volume': data['volume'],        # Required
            'bid': data.get('bid'),          # Optional - NEW
            'ask': data.get('ask'),          # Optional - NEW
            'bid_size': data.get('bid_size'), # Optional - NEW
            'ask_size': data.get('ask_size')  # Optional - NEW
        }
    
    return symbol_prices
```

#### Option 2: Gradual Enhancement
```python
def enhance_existing_data(basic_symbol_prices, bid_ask_data=None):
    """Enhance existing symbol_prices with bid/ask if available"""
    enhanced_prices = {}
    
    for symbol, data in basic_symbol_prices.items():
        enhanced_prices[symbol] = data.copy()  # Start with existing data
        
        # Add bid/ask if available
        if bid_ask_data and symbol in bid_ask_data:
            ba_data = bid_ask_data[symbol]
            enhanced_prices[symbol].update({
                'bid': ba_data.get('bid'),
                'ask': ba_data.get('ask'),
                'bid_size': ba_data.get('bid_size'),
                'ask_size': ba_data.get('ask_size')
            })
    
    return enhanced_prices
```

## CSV Output Enhancement

### New CSV Columns Added
The enhanced TradeLogger now includes these additional columns in trades.csv:

**Volume Analytics:**
- `volume_ratio` - Volume surge ratio (e.g., 2.5x)
- `volume_baseline` - Baseline volume used for calculation
- `baseline_source` - Source of baseline (opening/avg/fallback)
- `avg_volume` - Average daily volume

**Price Action:**
- `range_width_pct` - Opening range width percentage
- `distance_from_threshold` - How far past breakout threshold
- `breakout_buffer_pct` - Buffer percentage used

**Market Microstructure:**
- `bid_price` - Bid price at breakout
- `ask_price` - Ask price at breakout  
- `spread` - Bid-ask spread

**Timing:**
- `elapsed_minutes` - Minutes since market open

### Sample CSV Output
```csv
trade_id,timestamp,symbol,direction,entry_price,opening_high,opening_low,volume,scanners,priority_score,vix,sector,status,volume_ratio,volume_baseline,baseline_source,range_width_pct,elapsed_minutes,breakout_buffer_pct,distance_from_threshold,bid_price,ask_price,spread,avg_volume,current_price,current_volume,last_update,max_price,min_price,pnl_pct
T1703123456-001,2024-01-15 10:30:15,AAPL,LONG,150.25,150.10,149.80,1250000,momentum_scanner,85.5,18.2,Technology,ACTIVE,2.3,543210,opening,2.1,60,0.1,0.15,150.24,150.26,0.02,1200000,150.25,1250000,2024-01-15 10:30:15,150.25,150.25,0.0
```

## Risk Mitigation

### Defensive Programming
- All new fields use `.get()` for safe extraction
- Empty string defaults for missing data in CSV
- No changes to core breakout logic
- Existing calls continue to work unchanged

### Testing Strategy
1. **Compatibility Test**: Run with existing data format - should work unchanged
2. **Enhancement Test**: Add bid/ask data - should capture additional fields
3. **Mixed Test**: Some symbols with bid/ask, others without - should handle gracefully

## Migration Steps

1. **Phase 1**: Deploy enhanced TradeLogger (DONE)
2. **Phase 2**: Deploy enhanced BreakoutDetector calls (DONE)  
3. **Phase 3**: Enhance data source to include bid/ask (YOUR CHOICE)
4. **Phase 4**: Monitor CSV output and validate data quality

## Notes
- The enhanced format is fully backward compatible
- Bid/Ask data improves trade quality analysis
- Spread data helps assess execution conditions
- Volume analytics provide better breakout confirmation
- All enhancements are optional and fail-safe