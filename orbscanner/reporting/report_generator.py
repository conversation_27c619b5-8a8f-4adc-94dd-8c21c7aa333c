#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Report Generator for ORB Scanner

This module provides functions for generating various reports for the ORB Scanner.
"""

import logging
import os  # Used for file path operations
from datetime import datetime
import pytz

logger = logging.getLogger(__name__)

def generate_markdown_report(app, log_base, vix_level=None, email_report=True):
    """
    Generate a comprehensive Markdown report with scanner results.
    This is a wrapper around the finalizing report generator.

    Args:
        app: The scanner application instance
        log_base: Base path for log files
        vix_level: Current VIX level
        email_report: Whether to email the report after generation (default: True)

    Returns:
        str: Path to the generated report
    """
    # Import the finalizing report generator
    try:
        from orbscanner.utils.reporting.finalizing import generate_finalizing_report
        return generate_finalizing_report(app, log_base, vix_level, email_report)
    except ImportError:
        logger.error("Could not import finalizing report generator")
        return None

def generate_summary_report(app, log_base):
    """
    Generate a simple summary report with key statistics.

    Args:
        app: The scanner application instance
        log_base: Base path for log files

    Returns:
        str: Path to the generated report
    """
    logger.info("Generating summary report...")

    # Create report path
    report_path = f"{log_base}_summary_report.md"

    # Get current time in Eastern timezone
    eastern_tz = pytz.timezone('US/Eastern')
    current_time = datetime.now(eastern_tz)

    # Extract data from app
    long_candidates = getattr(app, 'longCandidates', [])
    short_candidates = getattr(app, 'shortCandidates', [])
    combined_results = getattr(app, 'combinedResults', [])

    # Generate the report
    with open(report_path, 'w') as f:
        # Report header
        f.write(f"# ORB Scanner Summary Report\n\n")
        f.write(f"**Date:** {current_time.strftime('%Y-%m-%d')}\n")
        f.write(f"**Time:** {current_time.strftime('%H:%M:%S %Z')}\n\n")

        # Summary statistics
        f.write("## Summary Statistics\n\n")
        f.write(f"- **Long Candidates:** {len(long_candidates)}\n")
        f.write(f"- **Short Candidates:** {len(short_candidates)}\n")
        f.write(f"- **Total Unique Symbols:** {len(combined_results)}\n\n")

        # Top candidates
        if long_candidates or short_candidates:
            f.write("## Top Candidates\n\n")

            # Combine and sort all candidates
            all_candidates = []
            for candidate in long_candidates:
                candidate_copy = candidate.copy()
                candidate_copy['direction'] = 'LONG'
                all_candidates.append(candidate_copy)

            for candidate in short_candidates:
                candidate_copy = candidate.copy()
                candidate_copy['direction'] = 'SHORT'
                all_candidates.append(candidate_copy)

            # Sort by priority score and take top 5
            sorted_candidates = sorted(all_candidates, key=lambda x: x.get('priority_score', 0), reverse=True)
            top_candidates = sorted_candidates[:5]

            f.write("| Symbol | Direction | Priority |\n")
            f.write("|--------|-----------|----------:|\n")

            for candidate in top_candidates:
                symbol = candidate.get('symbol', 'N/A')
                direction = candidate.get('direction', 'N/A')
                priority = candidate.get('priority_score', candidate.get('priority', 0))

                f.write(f"| {symbol} | {direction} | {priority:.2f} |\n")
        else:
            f.write("*No candidates found*\n\n")

    logger.info(f"Summary report generated: {report_path}")
    return report_path

def generate_data_quality_report(app, log_base):
    """
    Generate a report focused on data quality metrics.

    Args:
        app: The scanner application instance
        log_base: Base path for log files

    Returns:
        str: Path to the generated report
    """
    logger.info("Generating data quality report...")

    # Create report path
    report_path = f"{log_base}_data_quality_report.md"

    # Get current time in Eastern timezone
    eastern_tz = pytz.timezone('US/Eastern')
    current_time = datetime.now(eastern_tz)

    # Import data quality metrics generator
    try:
        from orbscanner.reporting.data_quality import generate_data_quality_metrics, analyze_data_quality

        # Generate metrics
        quality_scores = generate_data_quality_metrics(app)
        quality_stats = analyze_data_quality(app)

        # Generate the report
        with open(report_path, 'w') as f:
            # Report header
            f.write(f"# ORB Scanner Data Quality Report\n\n")
            f.write(f"**Date:** {current_time.strftime('%Y-%m-%d')}\n")
            f.write(f"**Time:** {current_time.strftime('%H:%M:%S %Z')}\n\n")

            # Summary statistics
            f.write("## Data Quality Summary\n\n")

            total_symbols = quality_stats.get('total_symbols', 0)
            symbols_with_complete_data = quality_stats.get('symbols_with_complete_data', 0)
            symbols_with_partial_data = quality_stats.get('symbols_with_partial_data', 0)
            symbols_with_minimal_data = quality_stats.get('symbols_with_minimal_data', 0)

            f.write(f"- **Total Symbols:** {total_symbols}\n")
            complete_pct = symbols_with_complete_data/total_symbols*100 if total_symbols > 0 else 0
            partial_pct = symbols_with_partial_data/total_symbols*100 if total_symbols > 0 else 0
            minimal_pct = symbols_with_minimal_data/total_symbols*100 if total_symbols > 0 else 0

            f.write(f"- **Symbols with Complete Data:** {symbols_with_complete_data} ({complete_pct:.1f}%)\n")
            f.write(f"- **Symbols with Partial Data:** {symbols_with_partial_data} ({partial_pct:.1f}%)\n")
            f.write(f"- **Symbols with Minimal Data:** {symbols_with_minimal_data} ({minimal_pct:.1f}%)\n")
            f.write(f"- **Average Range Percentage:** {quality_stats.get('avg_range_pct', 0):.2f}%\n\n")

            # Top symbols by quality score
            if quality_scores:
                f.write("## Top Symbols by Data Quality\n\n")
                f.write("| Symbol | Score | Updates | Tick Types | Has Volume |\n")
                f.write("|--------|------:|--------:|-----------:|:-----------|\n")

                sorted_scores = sorted(quality_scores.items(), key=lambda x: x[1]['score'], reverse=True)

                for symbol, metrics in sorted_scores[:10]:  # Show top 10
                    score = metrics.get('score', 0)
                    updates = metrics.get('update_count', 0)
                    tick_types = metrics.get('tick_types', 0)
                    has_volume = "Yes" if metrics.get('has_volume', False) else "No"

                    f.write(f"| {symbol} | {score:.1f} | {updates} | {tick_types}/5 | {has_volume} |\n")
            else:
                f.write("*No quality scores available*\n\n")

        logger.info(f"Data quality report generated: {report_path}")
        return report_path
    except ImportError:
        logger.error("Could not import data quality metrics generator")
        return None
    except Exception as e:
        logger.error(f"Error generating data quality report: {e}")
        return None
