#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Data Quality Metrics for ORB Scanner

This module provides functions for generating data quality metrics and
validating data integrity for the ORB Scanner.
"""

import logging
import random
from datetime import datetime

def generate_data_quality_metrics(app):
    """
    Generate data quality metrics for opening range data.
    This helps identify symbols with suspicious data patterns or incomplete data.
    
    Args:
        app: The scanner application instance with opening_range_data
        
    Returns:
        dict: Dictionary containing quality scores by symbol
    """
    if not hasattr(app, 'opening_range_data') or not app.opening_range_data:
        logging.warning("No opening range data available for quality metrics")
        return {}

    logging.info("Generating data quality metrics for opening range data")

    # Initialize counters
    total_symbols = len(app.opening_range_data)
    symbols_with_quality_data = 0
    symbols_with_all_tick_types = 0
    symbols_with_volume = 0
    symbols_with_unchanging_prices = 0
    symbols_with_few_updates = 0

    # Define required tick types for complete data
    required_tick_types = {4, 6, 7, 8, 14}  # Last, High, Low, Volume, Open

    # Track data quality by symbol
    quality_scores = {}

    # Process each symbol
    for symbol, data in app.opening_range_data.items():
        # Skip if data_quality is not present
        if 'data_quality' not in data:
            continue

        quality_data = data['data_quality']

        # Calculate basic metrics
        price_updates = quality_data.get('price_updates', 0)
        volume_updates = quality_data.get('volume_updates', 0)
        tick_types_received = quality_data.get('tick_types_received', set())
        update_count = data.get('update_count', 0)

        # Check if we have volume data
        if data.get('volume', 0) > 0:
            symbols_with_volume += 1

        # Check if we have all required tick types
        has_all_tick_types = all(tick in tick_types_received for tick in required_tick_types)
        if has_all_tick_types:
            symbols_with_all_tick_types += 1

        # Check for unchanging prices
        if price_updates <= 1:
            symbols_with_unchanging_prices += 1

        # Check for few updates
        if update_count < 3:
            symbols_with_few_updates += 1

        # Calculate quality score (0-100)
        # 50% weight: Percentage of required tick types received
        # 30% weight: Number of price updates (capped at 10)
        # 20% weight: Has volume data

        tick_type_score = len(tick_types_received) / len(required_tick_types) * 50
        update_score = min(price_updates, 10) / 10 * 30
        volume_score = 20 if data.get('volume', 0) > 0 else 0

        total_score = tick_type_score + update_score + volume_score

        # Store quality score
        quality_scores[symbol] = {
            'score': total_score,
            'tick_types': len(tick_types_received),
            'price_updates': price_updates,
            'volume_updates': volume_updates,
            'has_volume': data.get('volume', 0) > 0,
            'update_count': update_count
        }

        # Count symbols with good quality data (score > 70)
        if total_score > 70:
            symbols_with_quality_data += 1

    # Log summary statistics
    logging.info(f"DATA QUALITY SUMMARY for {total_symbols} symbols:")
    logging.info(f"  - Symbols with quality data (score > 70): {symbols_with_quality_data} ({symbols_with_quality_data/total_symbols*100:.1f}%)")
    logging.info(f"  - Symbols with all required tick types: {symbols_with_all_tick_types} ({symbols_with_all_tick_types/total_symbols*100:.1f}%)")
    logging.info(f"  - Symbols with volume data: {symbols_with_volume} ({symbols_with_volume/total_symbols*100:.1f}%)")
    logging.info(f"  - Symbols with unchanging prices: {symbols_with_unchanging_prices} ({symbols_with_unchanging_prices/total_symbols*100:.1f}%)")
    logging.info(f"  - Symbols with few updates (<3): {symbols_with_few_updates} ({symbols_with_few_updates/total_symbols*100:.1f}%)")

    # Log top 5 and bottom 5 symbols by quality score
    if quality_scores:
        sorted_scores = sorted(quality_scores.items(), key=lambda x: x[1]['score'], reverse=True)

        logging.info("TOP 5 SYMBOLS BY DATA QUALITY:")
        for symbol, metrics in sorted_scores[:5]:
            logging.info(f"  - {symbol}: Score {metrics['score']:.1f}, Updates: {metrics['update_count']}, Tick Types: {metrics['tick_types']}/5")

        logging.info("BOTTOM 5 SYMBOLS BY DATA QUALITY:")
        for symbol, metrics in sorted_scores[-5:]:
            logging.info(f"  - {symbol}: Score {metrics['score']:.1f}, Updates: {metrics['update_count']}, Tick Types: {metrics['tick_types']}/5")

    # Store quality scores in app for later use
    app.data_quality_scores = quality_scores
    
    return quality_scores

def analyze_data_quality(app):
    """
    Analyze data quality and provide statistics on opening range data.
    
    Args:
        app: The scanner application instance with opening_range_data
        
    Returns:
        dict: Dictionary containing data quality statistics
    """
    if not hasattr(app, 'opening_range_data') or not app.opening_range_data:
        logging.warning("No opening range data available for quality analysis")
        return {}
    
    # Initialize counters and lists
    total_symbols = len(app.opening_range_data)
    symbols_with_complete_data = 0
    symbols_with_partial_data = 0
    symbols_with_minimal_data = 0
    range_percentages = []
    good_symbols = []
    
    # Process each symbol
    for symbol, data in app.opening_range_data.items():
        high = data.get('high', 0)
        low = data.get('low', 0)
        open_price = data.get('open', 0)
        volume = data.get('volume', 0)
        update_count = data.get('update_count', 0)

        # Calculate range percentage
        if open_price > 0 and high > 0 and low > 0:
            range_pct = ((high - low) / open_price) * 100
            range_percentages.append(range_pct)

            # Classify data quality
            if volume > 0 and update_count >= 5 and range_pct > 0.1:
                symbols_with_complete_data += 1
                good_symbols.append(symbol)
            elif update_count >= 3:
                symbols_with_partial_data += 1
            else:
                symbols_with_minimal_data += 1
    
    # Calculate statistics
    avg_range_pct = sum(range_percentages) / len(range_percentages) if range_percentages else 0
    
    # Log summary statistics
    logging.info(f"DATA QUALITY ANALYSIS for {total_symbols} symbols:")
    logging.info(f"  - Symbols with complete data: {symbols_with_complete_data} ({symbols_with_complete_data/total_symbols*100:.1f}%)")
    logging.info(f"  - Symbols with partial data: {symbols_with_partial_data} ({symbols_with_partial_data/total_symbols*100:.1f}%)")
    logging.info(f"  - Symbols with minimal data: {symbols_with_minimal_data} ({symbols_with_minimal_data/total_symbols*100:.1f}%)")
    logging.info(f"  - Average range percentage: {avg_range_pct:.2f}%")
    
    # Log sample symbols with good data
    if good_symbols:
        sample_size = min(3, len(good_symbols))
        samples = random.sample(good_symbols, sample_size)

        logging.info("Sample symbols with good data:")
        for symbol in samples:
            data = app.opening_range_data[symbol]
            high = data.get('high', 0)
            low = data.get('low', 0)
            open_price = data.get('open', 0)
            volume = data.get('volume', 0)
            range_pct = ((high - low) / open_price) * 100 if open_price > 0 else 0

            logging.info(f"  - {symbol}: Range {range_pct:.2f}%, High ${high:.2f}, Low ${low:.2f}, Volume {volume:,}")
    else:
        logging.warning("No valid range data available for any symbols")
    
    # Return statistics
    return {
        'total_symbols': total_symbols,
        'symbols_with_complete_data': symbols_with_complete_data,
        'symbols_with_partial_data': symbols_with_partial_data,
        'symbols_with_minimal_data': symbols_with_minimal_data,
        'avg_range_pct': avg_range_pct,
        'good_symbols': good_symbols
    }
