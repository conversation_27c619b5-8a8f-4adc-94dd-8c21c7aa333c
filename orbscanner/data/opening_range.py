"""
Opening range calculation utilities for the ORB Scanner.

This module contains functions for calculating and updating opening ranges,
both from real-time data and historical data.
"""

import logging
import os
import time
import traceback
from datetime import datetime, timedelta
import pytz

from orbscanner.utils.time.timezone import get_eastern_time
from orbscanner.utils.phase.manager import PhaseManager
from orbscanner.utils.data.tracing_integration import trace_data_request
from orbscanner.data.historical_data import ensure_symbol_string, get_historical_opening_range
from orbscanner.utils.phase.logging import log_to_phase

# Constants
OPENING_RANGE_MINUTES = 7  # Default opening range duration in minutes
BATCH_SIZE = 10  # Number of symbols to process in a batch
BATCH_PROCESSING_DELAY = 2  # Delay between batches in seconds

# Logging utilities for opening range calculations
def log_range_calculation(symbol, message, **kwargs):
    """Log opening range calculation details"""
    details = ", ".join(f"{k}={v}" for k, v in kwargs.items())
    logging.debug(f"RANGE_CALC [{symbol}]: {message} - {details}")

def log_range_validation(symbol, message, **kwargs):
    """Log opening range validation details"""
    details = ", ".join(f"{k}={v}" for k, v in kwargs.items())
    logging.debug(f"RANGE_VALID [{symbol}]: {message} - {details}")

def log_range_warning(symbol, message, **kwargs):
    """Log opening range warning details"""
    details = ", ".join(f"{k}={v}" for k, v in kwargs.items())
    logging.warning(f"RANGE_WARN [{symbol}]: {message} - {details}")

def log_range_error(symbol, message, **kwargs):
    """Log opening range error details"""
    details = ", ".join(f"{k}={v}" for k, v in kwargs.items())
    logging.error(f"RANGE_ERROR [{symbol}]: {message} - {details}")

def log_range_data_collection(symbol, message, **kwargs):
    """Log opening range data collection details"""
    details = ", ".join(f"{k}={v}" for k, v in kwargs.items())
    logging.debug(f"RANGE_DATA [{symbol}]: {message} - {details}")

def log_wrapper_call(method_name, *args, **kwargs):
    """Log wrapper method calls for debugging"""
    # Check if wrapper logging is enabled
    if not hasattr(log_wrapper_call, 'WRAPPER_DEBUG'):
        log_wrapper_call.WRAPPER_DEBUG = True
        # Ensure logs/live directory exists
        os.makedirs(os.path.join("logs", "live"), exist_ok=True)
        log_wrapper_call.WRAPPER_LOG_FILE = os.path.join("logs", "live", "scanner_wrapper.log")

    if not log_wrapper_call.WRAPPER_DEBUG:
        return

    # Format arguments for logging
    args_str = ", ".join([str(arg) for arg in args])
    kwargs_str = ", ".join([f"{k}={v}" for k, v in kwargs.items()])
    all_args = ", ".join(filter(None, [args_str, kwargs_str]))

    # Log the method call
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
    log_message = f"{timestamp} - WRAPPER - {method_name}({all_args})"

    # Write to the wrapper log file
    with open(log_wrapper_call.WRAPPER_LOG_FILE, 'a') as f:
        f.write(log_message + "\n")

@trace_data_request(request_type="update_opening_ranges_historical")
def update_opening_ranges_historical(app, market_open_time=None):
    """
    Update opening ranges using historical data - must be called BEFORE priority scoring
    This is a new method that processes historical data for filtered symbols before priority scoring

    Args:
        app: The IBApp instance
        market_open_time (datetime, optional): Market open time. If None, uses 9:30 AM today.

    Returns:
        bool: True if successful, False otherwise
    """
    # Log wrapper call for method extraction verification
    log_wrapper_call("update_opening_ranges_historical", market_open_time)

    current_phase = app.phase_manager.current_phase if hasattr(app, 'phase_manager') else "UNKNOWN"
    current_time = get_eastern_time()

    logging.info(f"Requesting historical opening range data for filtered symbols")
    logging.info(f"PHASE_INFO: Current phase: {current_phase} | Current time: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"PHASE_INFO: Market open time: {market_open_time.strftime('%Y-%m-%d %H:%M:%S') if market_open_time else 'None'}")

    # DIAGNOSTIC LOGGING: Log tracked and filtered symbols before processing
    if hasattr(app, 'tracked_symbols'):
        # Log the first 5 symbols and their types to avoid excessive logging
        sample_symbols = list(app.tracked_symbols.keys())[:5]
        logging.debug(f"Tracked symbols before processing in update_opening_ranges_historical: { {s: type(s).__name__ for s in sample_symbols} }...")
        logging.debug(f"Total tracked symbols: {len(app.tracked_symbols)}")

        # Check for non-string keys
        non_string_keys = [k for k in app.tracked_symbols.keys() if not isinstance(k, str)]
        if non_string_keys:
            logging.warning(f"Found {len(non_string_keys)} non-string keys in tracked_symbols")
            for k in non_string_keys[:3]:  # Log first 3 only
                logging.warning(f"Non-string key: {str(k)}, type: {type(k).__name__}")

    if hasattr(app, 'filtered_symbols'):
        # Log the first 5 filtered symbols and their types
        sample_filtered = app.filtered_symbols[:5] if app.filtered_symbols else []
        logging.debug(f"Filtered symbols before processing in update_opening_ranges_historical: { {s: type(s).__name__ for s in sample_filtered} }...")
        logging.debug(f"Total filtered symbols: {len(app.filtered_symbols) if hasattr(app, 'filtered_symbols') else 0}")

        # Check for non-string elements
        if app.filtered_symbols:
            non_string_elements = [s for s in app.filtered_symbols if not isinstance(s, str)]
            if non_string_elements:
                logging.warning(f"Found {len(non_string_elements)} non-string elements in filtered_symbols")
                for s in non_string_elements[:3]:  # Log first 3 only
                    logging.warning(f"Non-string element: {str(s)}, type: {type(s).__name__}")

    # CRITICAL FIX: Ensure we have tracked symbols before requesting historical data
    # This breaks the circular dependency between tracking symbols and updating opening ranges
    if not hasattr(app, 'tracked_symbols') or not app.tracked_symbols:
        logging.error("CRITICAL ERROR: No tracked symbols available for historical data processing")
        logging.error("This indicates a sequencing issue - symbols must be tracked before requesting historical data")
        logging.error("Check that scannerDataEnd is properly tracking symbols when scanner data is received")
        return False

    # Check if we have filtered symbols
    if not hasattr(app, 'filtered_symbols') or not app.filtered_symbols:
        logging.warning("No filtered symbols available for historical data processing")
        # CRITICAL FIX: Fall back to tracked symbols if no filtered symbols are available
        logging.info(f"CRITICAL FIX: Falling back to {len(app.tracked_symbols)} tracked symbols for historical data processing")

        # CRITICAL FIX: Ensure we don't include self or other non-string objects in filtered_symbols
        valid_keys = [k for k in app.tracked_symbols.keys() if isinstance(k, str)]

        # Check if we found any non-string keys
        if len(valid_keys) < len(app.tracked_symbols):
            logging.critical(f"CRITICAL ERROR: Found {len(app.tracked_symbols) - len(valid_keys)} non-string keys in tracked_symbols")
            logging.critical("Filtering out non-string keys to prevent errors")

            # Check specifically for self
            if app in app.tracked_symbols:
                logging.critical("CRITICAL ERROR: Found 'self' (IBApp object) in tracked_symbols!")
                logging.critical("This is a programming error - the IBApp object cannot be used as a symbol")

                # Remove self from tracked_symbols
                del app.tracked_symbols[app]
                logging.critical("CRITICAL FIX: Removed 'self' from tracked_symbols")

        app.filtered_symbols = valid_keys

    # CRITICAL FIX: Validate and normalize filtered_symbols to ensure they're all strings
    valid_symbols = []
    invalid_symbols = []
    normalized_symbols = []

    for symbol in app.filtered_symbols:
        # Use ensure_symbol_string to normalize the symbol
        normalized_symbol = ensure_symbol_string(app, symbol)

        if normalized_symbol == "UNKNOWN":
            invalid_symbols.append((symbol, type(symbol)))
        else:
            valid_symbols.append(normalized_symbol)
            normalized_symbols.append(normalized_symbol)

    if invalid_symbols:
        logging.error("=" * 80)
        logging.error(f"⚠️ CRITICAL ERROR: Found {len(invalid_symbols)} non-string symbols in filtered_symbols")
        for symbol, symbol_type in invalid_symbols[:10]:  # Show first 10 only
            logging.error(f"⚠️ Invalid symbol type: {symbol} (type: {symbol_type})")

            # Log the call stack to help identify where this is happening
            stack_trace = ''.join(traceback.format_stack())
            logging.error(f"Call stack for invalid symbol in process_historical_data_for_filtered_symbols:\n{stack_trace}")

        logging.error(f"⚠️ These symbols will be skipped to prevent errors")
        logging.error("=" * 80)

        # Replace filtered_symbols with normalized valid symbols
        app.filtered_symbols = valid_symbols
        logging.info(f"Filtered symbols list updated to include only normalized string symbols ({len(valid_symbols)} symbols)")

    elif len(normalized_symbols) > 0 and normalized_symbols != app.filtered_symbols:
        # If we normalized any symbols but didn't have any invalid ones, still update the list
        logging.info(f"Normalized {len(normalized_symbols)} symbols in filtered_symbols")
        app.filtered_symbols = normalized_symbols

    # CRITICAL FIX: Add additional check to ensure no self references in filtered_symbols
    if app in app.filtered_symbols:
        logging.critical("CRITICAL ERROR: Found 'self' (IBApp object) in filtered_symbols list!")
        logging.critical("This is a programming error - the IBApp object cannot be used as a symbol")
        app.filtered_symbols.remove(app)
        logging.critical("CRITICAL FIX: Removed 'self' from filtered_symbols list")

    # CRITICAL FIX: Double-check all symbols are strings
    non_string_symbols = [s for s in app.filtered_symbols if not isinstance(s, str)]
    if non_string_symbols:
        logging.critical(f"CRITICAL ERROR: Found {len(non_string_symbols)} non-string objects in filtered_symbols after normalization")
        for s in non_string_symbols[:5]:  # Show first 5 only
            logging.critical(f"Bad symbol type: {type(s)}, Value: {s}")

        # Remove non-string symbols
        app.filtered_symbols = [s for s in app.filtered_symbols if isinstance(s, str)]
        logging.critical(f"CRITICAL FIX: Filtered out non-string objects from filtered_symbols")

    # CRITICAL FIX: Deduplicate symbols before batching to avoid redundant requests
    # Use dict.fromkeys() to preserve order while removing duplicates
    deduplicated_symbols = list(dict.fromkeys(app.filtered_symbols))
    logging.info(f"Deduplicated symbols list: {len(app.filtered_symbols)} -> {len(deduplicated_symbols)} symbols")

    # Process in batches to avoid API rate limits
    batch_size = BATCH_SIZE
    symbol_batches = [deduplicated_symbols[i:i+batch_size] for i in range(0, len(deduplicated_symbols), batch_size)]

    # Track successful and failed symbols
    successful_symbols = []
    failed_symbols = []
    # Track processed symbols to avoid redundant processing
    processed_symbols = set()

    # Validate market_open_time to prevent future data requests
    if market_open_time is None:
        est_tz = pytz.timezone('US/Eastern')
        now = datetime.now(est_tz)

        # If in after-open mode, use the simulated market open time
        if hasattr(app, 'after_open') and app.after_open and hasattr(app, 'simulated_market_open_time'):
            market_open_time = app.simulated_market_open_time
            logging.info(f"AFTER-OPEN MODE: Using simulated market open time: {market_open_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        else:
            market_open_time = now.replace(hour=9, minute=30, second=0, microsecond=0)
            logging.info(f"Using default market open time: {market_open_time.strftime('%Y-%m-%d %H:%M:%S')}")

    # Calculate end time (market open + opening range minutes)
    end_time = market_open_time + timedelta(minutes=OPENING_RANGE_MINUTES)

    # Get current time in Eastern timezone for comparison
    est_tz = pytz.timezone('US/Eastern')
    current_time = datetime.now(est_tz)

    # Check if we're requesting future data (critical error)
    if end_time > current_time:
        time_diff = (end_time - current_time).total_seconds() / 60.0
        logging.error("=" * 80)
        logging.error(f"⚠️ CRITICAL TIME ERROR IN BATCH PROCESSING ⚠️")
        logging.error(f"⚠️ Requesting FUTURE data for {len(app.filtered_symbols)} symbols")
        logging.error(f"⚠️ Current time: {current_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        logging.error(f"⚠️ Requested end time: {end_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        logging.error(f"⚠️ Time difference: {time_diff:.1f} minutes in the FUTURE")
        logging.error(f"⚠️ ALL HISTORICAL DATA REQUESTS WILL FAIL - DATA FROM THE FUTURE DOESN'T EXIST")
        logging.error(f"⚠️ To fix: Use --market-open-date with a past date or wait until after market open")
        logging.error("=" * 80)
        return False

    for batch_idx, symbol_batch in enumerate(symbol_batches):
        logging.info(f"Processing historical data batch {batch_idx+1}/{len(symbol_batches)} ({len(symbol_batch)} symbols)")

        # Process each symbol in the batch
        for symbol in symbol_batch:
            # CRITICAL FIX: Skip already processed symbols
            if symbol in processed_symbols:
                logging.debug(f"Symbol {symbol} already processed, skipping")
                continue

            # Mark symbol as processed
            processed_symbols.add(symbol)

            # CRITICAL FIX: Double-check symbol type before calling get_historical_opening_range
            if not isinstance(symbol, str):
                logging.error(f"CRITICAL ERROR: Skipping non-string symbol: {symbol} (type: {type(symbol)})")
                failed_symbols.append(symbol)
                continue

            # Get historical opening range
            try:
                range_data = get_historical_opening_range(app, symbol, market_open_time)

                if range_data and validate_historical_data_quality(range_data):
                    # Add data source tracking for better diagnostics
                    range_data['source'] = 'historical'
                    range_data['data_source_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                    app.opening_range_data[symbol] = range_data
                    successful_symbols.append(symbol)
                    logging.info(f"Valid historical data for {symbol}: High={range_data['high']:.2f}, Low={range_data['low']:.2f}")

                    # Add specific logging for after-open mode
                    if hasattr(app, 'after_open') and app.after_open:
                        logging.info(f"AFTER-OPEN MODE: Successfully populated opening range for {symbol} using historical data")
                else:
                    failed_symbols.append(symbol)
                    logging.warning(f"Invalid or missing historical data for {symbol} - excluding from candidates")
            except Exception as e:
                failed_symbols.append(symbol)
                logging.error(f"Exception while getting historical data for {symbol}: {e}")
                logging.error(f"Symbol type: {type(symbol)}")
                logging.error(f"Traceback: {traceback.format_exc()}")

        # Small delay between batches to avoid overwhelming the API
        if batch_idx < len(symbol_batches) - 1:
            logging.debug(f"Waiting {BATCH_PROCESSING_DELAY} seconds before processing next batch...")
            time.sleep(BATCH_PROCESSING_DELAY)

    # Log summary of historical data processing
    logging.info(f"Historical data processing complete: {len(successful_symbols)}/{len(app.filtered_symbols)} symbols have valid data")

    # Log failed symbols for debugging
    if failed_symbols:
        logging.warning(f"Failed to get historical data for {len(failed_symbols)} symbols")
        logging.warning(f"First 10 failed symbols: {', '.join(str(s) for s in failed_symbols[:10])}")

    # Update valid symbols list - ONLY these will be used for priority scoring
    app.valid_symbols = successful_symbols

    # Update phase manager with correct counts
    if hasattr(app, 'phase_manager'):
        app.phase_manager.update_symbol_validation(PhaseManager.ORB, len(successful_symbols), len(app.filtered_symbols))

    # Strict validation - if no symbols have valid data, raise an error
    if len(successful_symbols) == 0:
        logging.error("No symbols have valid historical data. Cannot proceed with trading.")
        return False

    return True

def update_opening_ranges(app, use_historical_data=None, market_open_time=None):
    """
    Update opening ranges for all tracked symbols

    This method updates opening ranges for all tracked symbols, either using
    real-time data or historical data depending on the current time and settings.

    Args:
        app: The IBApp instance
        use_historical_data (bool, optional): Whether to use historical data. If None,
                                             will determine based on current time.
        market_open_time (datetime, optional): Market open time. If None, uses 9:30 AM today.
    """
    # Log wrapper call for method extraction verification
    log_wrapper_call("update_opening_ranges", use_historical_data, market_open_time)

    # Get current time in Eastern timezone
    est_tz = pytz.timezone('US/Eastern')
    current_time = datetime.now(est_tz)

    # Get current phase if phase manager exists
    current_phase = app.phase_manager.current_phase if hasattr(app, 'phase_manager') else None

    # Determine market open time if not provided
    if market_open_time is None:
        # If in after-open mode, use the simulated market open time
        if hasattr(app, 'after_open') and app.after_open and hasattr(app, 'simulated_market_open_time'):
            market_open_time = app.simulated_market_open_time
            logging.info(f"AFTER-OPEN MODE: Using simulated market open time: {market_open_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        else:
            market_open_time = current_time.replace(hour=9, minute=30, second=0, microsecond=0)
            logging.info(f"Using default market open time: {market_open_time.strftime('%Y-%m-%d %H:%M:%S')}")

    # Calculate opening range end time
    opening_range_end = market_open_time + timedelta(minutes=OPENING_RANGE_MINUTES)

    # Validate tracked_symbols
    if hasattr(app, 'tracked_symbols'):
        logging.debug(f"tracked_symbols count at start of update_opening_ranges: {len(app.tracked_symbols)}")
        sample_symbols = list(app.tracked_symbols.keys())[:5]
        logging.debug(f"Sample tracked symbols: { {s: type(s).__name__ for s in sample_symbols} }")

        # Check for non-string keys
        non_string_keys = [k for k in app.tracked_symbols.keys() if not isinstance(k, str)]
        if non_string_keys:
            logging.critical(f"CRITICAL ERROR: Found {len(non_string_keys)} non-string keys in tracked_symbols at start of update_opening_ranges")
            for k in non_string_keys[:3]:  # Log first 3 only
                logging.critical(f"Non-string key: {str(k)}, type: {type(k).__name__}")

            # Check specifically for self
            if app in app.tracked_symbols:
                logging.critical("CRITICAL ERROR: Found 'self' (IBApp object) in tracked_symbols!")
                logging.critical("This is a programming error - the IBApp object cannot be used as a symbol")

                # Remove self from tracked_symbols
                del app.tracked_symbols[app]
                logging.critical("CRITICAL FIX: Removed 'self' from tracked_symbols")

    # Check if we should use historical data
    use_historical = use_historical_data
    if use_historical is None:
        # If in after-open mode, always use historical data
        if hasattr(app, 'after_open') and app.after_open:
            logging.info(f"AFTER-OPEN MODE: Forcing historical data usage for opening ranges")
            use_historical = True
        # If we're past the opening range window, use historical data
        elif current_time > opening_range_end:
            logging.info(f"Past {OPENING_RANGE_MINUTES}-minute opening range window, using historical data")
            use_historical = True
        else:
            use_historical = False

    # Log the decision for clarity
    if use_historical:
        if hasattr(app, 'after_open') and app.after_open:
            logging.info(f"AFTER-OPEN MODE: Using historical data for opening ranges as required in after-open mode")
        else:
            logging.info(f"Using historical data for opening ranges based on configuration or timing")
    else:
        logging.info(f"Using real-time data for opening ranges")

    if use_historical:
        # Check if we're in the ORB phase and have filtered symbols
        if current_phase == PhaseManager.ORB and hasattr(app, 'filtered_symbols') and app.filtered_symbols:
            # CRITICAL FIX: Ensure filtered_symbols doesn't contain self or other non-string objects
            if any(not isinstance(s, str) for s in app.filtered_symbols):
                logging.critical("CRITICAL ERROR: Found non-string objects in filtered_symbols before calling update_opening_ranges_historical")
                # Fix filtered_symbols by removing non-string objects
                original_count = len(app.filtered_symbols)
                app.filtered_symbols = [s for s in app.filtered_symbols if isinstance(s, str)]
                logging.critical(f"CRITICAL FIX: Removed {original_count - len(app.filtered_symbols)} non-string objects from filtered_symbols")

            # Use the new method for historical data processing
            logging.info("Using new historical data processing method for filtered symbols")
            success = update_opening_ranges_historical(app, market_open_time)

            if not success:
                # Fall back to the original method if the new one fails
                logging.warning("New historical data processing method failed, falling back to original method")
                _update_opening_ranges_historical(app, market_open_time)
        else:
            # Use the original method for historical data processing
            logging.info("Using original historical data processing method for tracked symbols")
            _update_opening_ranges_historical(app, market_open_time)
        return
    else:
        # Using real-time data, check if we're in the opening range window
        if current_time < market_open_time:
            logging.info("Market not open yet, skipping opening range update")
            return

        if current_time > opening_range_end:
            logging.info(f"Past {OPENING_RANGE_MINUTES}-minute opening range window, skipping update")
            return

    # If we get here, we're using real-time data during the opening range window
    logging.info(f"Updating opening ranges for {len(app.tracked_symbols)} symbols using real-time data")

    # Add a warning if we're in after-open mode but still using real-time data
    if hasattr(app, 'after_open') and app.after_open:
        logging.warning(f"AFTER-OPEN MODE WARNING: Using real-time data for opening ranges despite being in after-open mode")
        logging.warning(f"This is unexpected and may indicate a configuration issue - after-open mode should use historical data")

    logging.info("CRITICAL FIX: Requesting real-time market data for opening range calculation")

    # Initialize market_data_req_ids if not already done
    if not hasattr(app, 'market_data_req_ids'):
        app.market_data_req_ids = {}

    # Define helper function for type safety
    def ensure_float(value, default=0.0, min_value=0.0):
        """Ensure value is a float and >= min_value"""
        try:
            result = float(value)
            return max(result, min_value)
        except (TypeError, ValueError):
            return default

    def ensure_int(value, default=0, min_value=0):
        """Ensure value is an int and >= min_value"""
        try:
            result = int(value)
            return max(result, min_value)
        except (TypeError, ValueError):
            return default

    # For each tracked symbol, request real-time market data if not already requested
    for symbol, data in list(app.tracked_symbols.items()):
        # Skip non-string symbols
        if not isinstance(symbol, str):
            logging.error(f"CRITICAL ERROR: Skipping non-string symbol: {symbol} (type: {type(symbol)})")
            continue

        # Request real-time data if not already requested
        if symbol not in app.market_data_req_ids:
            # This would call a method to request market data
            # For now, we'll just log that we would request it
            logging.info(f"Would request real-time market data for {symbol}")

            # In a real implementation, this would call methods like:
            # contract = create_contract(symbol)
            # req_id = app.get_next_req_id()
            # app.market_data_req_ids[symbol] = req_id
            # app.reqMktData(req_id, contract, generic_ticks, False, False, [])

            # Store the request time for monitoring data freshness
            if not hasattr(app, 'market_data_request_times'):
                app.market_data_request_times = {}
            app.market_data_request_times[symbol] = time.time()

        # Get the current price from the last_price dictionary with type safety
        current_price = 0.0
        if hasattr(app, 'last_price') and symbol in app.last_price:
            current_price = ensure_float(app.last_price[symbol].get('float_value', 0.0))

        # If we don't have a valid price yet, use the last_price from tracked_symbols as fallback
        if current_price <= 0:
            current_price = ensure_float(data.get('last_price', 0.0))

            # If still no valid price, log a warning
            if current_price <= 0:
                logging.warning(f"No valid price data for {symbol} - using fallback value")

                # CRITICAL FIX: No fallback data allowed - must use real data only
                logging.error(f"CRITICAL ERROR: No valid price data for {symbol} - excluding from opening range tracking")

                # Remove this symbol from tracked_symbols to prevent further processing
                if symbol in app.tracked_symbols:
                    logging.error(f"Removing {symbol} from tracked symbols due to missing real-time price data")
                    del app.tracked_symbols[symbol]

                # Skip this symbol and continue with the next one
                continue

        # Log raw price data received
        log_range_data_collection(symbol, f"Received price data: {current_price}",
                                 previous_price=ensure_float(data.get('last_price', 0.0)))

        # Initialize opening range if not already done
        if not hasattr(app, 'opening_range_data'):
            app.opening_range_data = {}

        if symbol not in app.opening_range_data:
            log_range_calculation(symbol, "Initializing opening range data",
                                 price=current_price)

            app.opening_range_data[symbol] = {
                'high': ensure_float(current_price),
                'low': ensure_float(current_price),
                'open': ensure_float(current_price),
                'volume': 0,
                'source': 'real-time'
            }

            log_range_calculation(symbol, "Opening range initialized",
                                 high=current_price, low=current_price, open=current_price)
        else:
            # Log current opening range before update with type safety
            log_range_calculation(symbol, "Current opening range before update",
                                 high=ensure_float(app.opening_range_data[symbol].get('high', 0.0)),
                                 low=ensure_float(app.opening_range_data[symbol].get('low', 0.0)),
                                 open=ensure_float(app.opening_range_data[symbol].get('open', 0.0)),
                                 volume=ensure_int(app.opening_range_data[symbol].get('volume', 0)))

        # Update high/low with type safety
        if current_price > ensure_float(app.opening_range_data[symbol].get('high', 0.0)):
            old_high = ensure_float(app.opening_range_data[symbol].get('high', 0.0))
            app.opening_range_data[symbol]['high'] = ensure_float(current_price)
            logging.debug(f"Updated opening range high for {symbol}: {current_price}")
            log_range_calculation(symbol, "Updated opening range high",
                                 old_high=old_high, new_high=current_price)

        if current_price < ensure_float(app.opening_range_data[symbol].get('low', float('inf'))) or ensure_float(app.opening_range_data[symbol].get('low', 0.0)) == 0.0:
            old_low = ensure_float(app.opening_range_data[symbol].get('low', 0.0))
            app.opening_range_data[symbol]['low'] = ensure_float(current_price)
            logging.debug(f"Updated opening range low for {symbol}: {current_price}")
            log_range_calculation(symbol, "Updated opening range low",
                                 old_low=old_low, new_low=current_price)

        # Get volume from real-time data if available
        volume = 0
        if hasattr(app, 'last_price') and symbol in app.last_price and 'volume' in app.last_price[symbol]:
            volume = ensure_int(app.last_price[symbol].get('volume', 0))

            # CRITICAL FIX: Only update volume if we don't have historical data
            # Preserve historical opening range volume data - don't overwrite with real-time cumulative volume
            current_source = app.opening_range_data[symbol].get('source', 'unknown')
            current_volume = ensure_int(app.opening_range_data[symbol].get('volume', 0))

            if volume > 0:
                if current_source == 'historical' and current_volume > 0:
                    # Don't overwrite historical opening range volume with real-time cumulative volume
                    log_range_calculation(symbol, "Preserving historical opening range volume",
                                        historical_volume=current_volume,
                                        realtime_volume=volume,
                                        action="preserved_historical")
                else:
                    # No historical data, use real-time volume
                    old_volume = current_volume
                    app.opening_range_data[symbol]['volume'] = volume
                    log_range_calculation(symbol, "Updated volume from real-time data",
                                        old_volume=old_volume,
                                        new_volume=volume)

        # Update last price in tracked symbols with explicit type conversion
        app.tracked_symbols[symbol]['last_price'] = ensure_float(current_price)

        # Calculate and log range width for validation
        high = app.opening_range_data[symbol]['high']
        low = app.opening_range_data[symbol]['low']
        open_price = app.opening_range_data[symbol]['open']

        if low > 0:
            range_width_pct = ((high - low) / low) * 100
            log_range_validation(symbol, "Range width calculation",
                                high=high, low=low, open=open_price,
                                range_width_pct=range_width_pct)

            # Validate range width
            if range_width_pct < 0.1:
                log_range_warning(symbol, "Range width too narrow",
                                 range_width_pct=range_width_pct, min_threshold=0.1)
            elif range_width_pct > 10.0:
                log_range_warning(symbol, "Range width too wide",
                                 range_width_pct=range_width_pct, max_threshold=10.0)
            else:
                log_range_validation(symbol, "Range width valid",
                                    range_width_pct=range_width_pct)

def _update_opening_ranges_historical(app, market_open_time=None):
    """
    Update opening ranges using historical data for tracked symbols
    This is the original method that processes historical data for tracked symbols

    Args:
        app: The IBApp instance
        market_open_time (datetime, optional): Market open time. If None, uses 9:30 AM today.
    """
    # Log wrapper call for method extraction verification
    log_wrapper_call("_update_opening_ranges_historical", market_open_time)

    # DIAGNOSTIC LOGGING: Log tracked symbols before processing
    if hasattr(app, 'tracked_symbols'):
        # Log the first 5 symbols and their types to avoid excessive logging
        sample_symbols = list(app.tracked_symbols.keys())[:5]
        logging.debug(f"Tracked symbols before processing in _update_opening_ranges_historical: { {s: type(s).__name__ for s in sample_symbols} }...")
        logging.debug(f"Total tracked symbols: {len(app.tracked_symbols)}")

        # Check for non-string keys
        non_string_keys = [k for k in app.tracked_symbols.keys() if not isinstance(k, str)]
        if non_string_keys:
            logging.warning(f"Found {len(non_string_keys)} non-string keys in tracked_symbols")
            for k in non_string_keys[:3]:  # Log first 3 only
                logging.warning(f"Non-string key: {str(k)}, type: {type(k).__name__}")

    # CRITICAL FIX: Ensure we have tracked symbols before requesting historical data
    # This breaks the circular dependency between tracking symbols and updating opening ranges
    if not hasattr(app, 'tracked_symbols') or not app.tracked_symbols:
        logging.error("CRITICAL ERROR: No tracked symbols available for historical data processing")
        logging.error("This indicates a sequencing issue - symbols must be tracked before requesting historical data")
        logging.error("Check that scannerDataEnd is properly tracking symbols when scanner data is received")
        return

    # CRITICAL FIX: Check for self in tracked_symbols before processing
    if app in app.tracked_symbols:
        logging.critical("CRITICAL ERROR: Found 'self' (IBApp object) in tracked_symbols!")
        logging.critical("This is a programming error - the IBApp object cannot be used as a symbol")

        # Remove self from tracked_symbols
        del app.tracked_symbols[app]
        logging.critical("CRITICAL FIX: Removed 'self' from tracked_symbols")

    logging.info(f"Updating opening ranges for {len(app.tracked_symbols)} symbols using historical data")

    # Validate market_open_time to prevent future data requests
    if market_open_time is None:
        est_tz = pytz.timezone('US/Eastern')
        now = datetime.now(est_tz)

        # If in after-open mode, use the simulated market open time
        if hasattr(app, 'after_open') and app.after_open and hasattr(app, 'simulated_market_open_time'):
            market_open_time = app.simulated_market_open_time
            logging.info(f"AFTER-OPEN MODE: Using simulated market open time: {market_open_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        else:
            market_open_time = now.replace(hour=9, minute=30, second=0, microsecond=0)
            logging.info(f"Using default market open time: {market_open_time.strftime('%Y-%m-%d %H:%M:%S')}")

    # Calculate end time (market open + opening range minutes)
    end_time = market_open_time + timedelta(minutes=OPENING_RANGE_MINUTES)

    # Get current time in Eastern timezone for comparison
    est_tz = pytz.timezone('US/Eastern')
    current_time = datetime.now(est_tz)

    # Check if we're requesting future data (critical error)
    if end_time > current_time:
        time_diff = (end_time - current_time).total_seconds() / 60.0
        logging.error("=" * 80)
        logging.error(f"⚠️ CRITICAL TIME ERROR IN BATCH PROCESSING ⚠️")
        logging.error(f"⚠️ Requesting FUTURE data for {len(app.tracked_symbols)} symbols")
        logging.error(f"⚠️ Current time: {current_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        logging.error(f"⚠️ Requested end time: {end_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        logging.error(f"⚠️ Time difference: {time_diff:.1f} minutes in the FUTURE")
        logging.error(f"⚠️ ALL HISTORICAL DATA REQUESTS WILL FAIL - DATA FROM THE FUTURE DOESN'T EXIST")
        logging.error(f"⚠️ To fix: Use --market-open-date with a past date or wait until after market open")
        logging.error("=" * 80)

    # CRITICAL FIX: Validate and normalize tracked_symbols keys to ensure they're all strings
    valid_symbols = []
    invalid_symbols = []
    normalized_symbols = {}

    for symbol, data in list(app.tracked_symbols.items()):
        # Use ensure_symbol_string to normalize the symbol
        normalized_symbol = ensure_symbol_string(app, symbol)

        if normalized_symbol == "UNKNOWN":
            invalid_symbols.append((symbol, type(symbol)))
        else:
            valid_symbols.append(normalized_symbol)
            normalized_symbols[normalized_symbol] = data

            # If the symbol was normalized (changed), update the tracked_symbols dictionary
            if normalized_symbol != symbol:
                logging.info(f"Normalized symbol: {symbol} -> {normalized_symbol}")

    if invalid_symbols:
        logging.error("=" * 80)
        logging.error(f"⚠️ CRITICAL ERROR: Found {len(invalid_symbols)} non-string symbols in tracked_symbols")
        for symbol, symbol_type in invalid_symbols[:10]:  # Show first 10 only
            logging.error(f"⚠️ Invalid symbol type: {symbol} (type: {symbol_type})")

            # Log the call stack to help identify where this is happening
            stack_trace = ''.join(traceback.format_stack())
            logging.error(f"Call stack for invalid symbol in _update_opening_ranges_historical:\n{stack_trace}")

        logging.error(f"⚠️ These symbols will be skipped to prevent errors")
        logging.error("=" * 80)

        # If we found any invalid symbols, replace the entire tracked_symbols dictionary
        # with the normalized version to ensure all keys are valid strings
        if len(normalized_symbols) != len(app.tracked_symbols):
            logging.warning(f"Replacing tracked_symbols dictionary with normalized version")
            logging.warning(f"Original size: {len(app.tracked_symbols)}, New size: {len(normalized_symbols)}")
            app.tracked_symbols = normalized_symbols

    # CRITICAL FIX: Add additional check to ensure no self references in valid_symbols
    if app in valid_symbols:
        logging.critical("CRITICAL ERROR: Found 'self' (IBApp object) in valid_symbols list!")
        logging.critical("This is a programming error - the IBApp object cannot be used as a symbol")
        valid_symbols.remove(app)
        logging.critical("CRITICAL FIX: Removed 'self' from valid_symbols list")

    # CRITICAL FIX: Double-check all symbols are strings
    non_string_symbols = [s for s in valid_symbols if not isinstance(s, str)]
    if non_string_symbols:
        logging.critical(f"CRITICAL ERROR: Found {len(non_string_symbols)} non-string objects in valid_symbols after normalization")
        for s in non_string_symbols[:5]:  # Show first 5 only
            logging.critical(f"Bad symbol type: {type(s)}, Value: {s}")

        # Remove non-string symbols
        valid_symbols = [s for s in valid_symbols if isinstance(s, str)]
        logging.critical(f"CRITICAL FIX: Filtered out non-string objects from valid_symbols")

    # CRITICAL FIX: Deduplicate symbols before batching to avoid redundant requests
    # Use dict.fromkeys() to preserve order while removing duplicates
    deduplicated_symbols = list(dict.fromkeys(valid_symbols))
    logging.info(f"Deduplicated symbols list: {len(valid_symbols)} -> {len(deduplicated_symbols)} symbols")

    # Track processed symbols to avoid redundant processing
    processed_symbols = set()

    # Process symbols in batches to avoid overwhelming the API
    batch_size = BATCH_SIZE
    symbol_batches = [deduplicated_symbols[i:i+batch_size] for i in range(0, len(deduplicated_symbols), batch_size)]

    for batch_idx, symbol_batch in enumerate(symbol_batches):
        logging.info(f"Processing historical data batch {batch_idx+1}/{len(symbol_batches)} ({len(symbol_batch)} symbols)")

        # Process each symbol in the batch
        for symbol in symbol_batch:
            # CRITICAL FIX: Skip already processed symbols
            if symbol in processed_symbols:
                logging.debug(f"Symbol {symbol} already processed, skipping")
                continue

            # Mark symbol as processed
            processed_symbols.add(symbol)

            # CRITICAL FIX: Double-check symbol type before calling get_historical_opening_range
            if not isinstance(symbol, str):
                logging.error(f"CRITICAL ERROR: Skipping non-string symbol: {symbol} (type: {type(symbol)})")
                continue

            # Skip if we already have historical data for this symbol
            if symbol in app.opening_range_data and app.opening_range_data[symbol].get('source') == 'historical':
                logging.debug(f"Already have historical data for {symbol}, skipping")
                continue

            # Get historical opening range
            try:
                # CRITICAL FIX: Triple-check that symbol is not self before calling get_historical_opening_range
                if symbol is app:
                    logging.critical("CRITICAL ERROR: Attempting to pass 'self' (IBApp object) as symbol parameter")
                    logging.critical("This is a programming error - the IBApp object cannot be used as a symbol")
                    # Log the call stack to help identify where this is happening
                    stack_trace = ''.join(traceback.format_stack())
                    logging.critical(f"Call stack for 'self' as symbol parameter:\n{stack_trace}")
                    continue

                # CRITICAL FIX: Verify symbol is a string and not an object reference string
                if not isinstance(symbol, str):
                    logging.error(f"CRITICAL ERROR: Non-string symbol after validation: {symbol} (type: {type(symbol)})")
                    continue
                elif '<' in symbol and '>' in symbol and 'object at' in symbol:
                    logging.error(f"CRITICAL ERROR: Symbol appears to be an object reference string: {symbol}")
                    continue

                # Now safe to call get_historical_opening_range
                range_data = get_historical_opening_range(app, symbol, market_open_time)

                if range_data:
                    # Add data source tracking for better diagnostics
                    range_data['source'] = 'historical'
                    range_data['data_source_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                    app.opening_range_data[symbol] = range_data
                    logging.info(f"Updated opening range for {symbol} using historical data: High={range_data['high']:.2f}, Low={range_data['low']:.2f}")

                    # Add specific logging for after-open mode
                    if hasattr(app, 'after_open') and app.after_open:
                        logging.info(f"AFTER-OPEN MODE: Successfully populated opening range for {symbol} using historical data (original method)")
                else:
                    logging.warning(f"Failed to get historical opening range for {symbol}")
            except Exception as e:
                logging.error(f"Exception while getting historical data for {symbol}: {e}")
                logging.error(f"Symbol type: {type(symbol)}")
                logging.error(f"Traceback: {traceback.format_exc()}")

        # Small delay between batches to avoid overwhelming the API
        if batch_idx < len(symbol_batches) - 1:
            logging.debug(f"Waiting {BATCH_PROCESSING_DELAY} seconds before processing next batch...")
            time.sleep(BATCH_PROCESSING_DELAY)

def validate_historical_data_quality(range_data):
    """
    Strictly validate historical data quality - no fallbacks

    Args:
        range_data (dict): Opening range data to validate

    Returns:
        bool: True if data is valid, False otherwise
    """
    # Check for minimum required data
    if not range_data:
        return False

    # Ensure we have high and low values
    high = range_data.get('high', 0)
    low = range_data.get('low', 0)

    if high <= 0 or low <= 0 or high <= low:
        return False

    # Ensure we have sufficient volume
    volume = range_data.get('volume', 0)
    if volume <= 0:
        return False

    # Ensure we have enough bars
    bars_count = range_data.get('bars', 0)
    if bars_count < OPENING_RANGE_MINUTES:  # Must have at least one bar per minute
        return False

    # Validate range width
    range_pct = ((high - low) / low) * 100
    if range_pct < 0.1 or range_pct > 10.0:  # Range must be between 0.1% and 10.0%
        return False

    return True

def analyze_historical_data_errors(app):
    """
    Analyze historical data errors and provide recommendations.

    This method analyzes the patterns in historical data errors and provides
    recommendations for improving data retrieval success rates.

    Args:
        app: The IBApp instance

    Returns:
        dict: Dictionary with analysis results and recommendations
    """
    # Log wrapper call for method extraction verification
    log_wrapper_call("analyze_historical_data_errors")

    if not hasattr(app, 'historical_data_errors') or not app.historical_data_errors:
        logging.info("No historical data errors to analyze")
        return {
            'error_count': 0,
            'symbols_with_errors': 0,
            'recommendations': ['No errors to analyze']
        }

    # Count errors by type
    error_types = {}
    symbols_with_errors = set()
    exchange_errors = {
        'SMART': 0,
        'ARCA': 0,
        'NYSE': 0,
        'NASDAQ': 0
    }

    for symbol, errors in app.historical_data_errors.items():
        symbols_with_errors.add(symbol)

        for error in errors:
            error_type = error.get('error_type', 'Unknown')
            error_types[error_type] = error_types.get(error_type, 0) + 1

            # Count errors by exchange
            exchange = error.get('exchange')
            if exchange and exchange in exchange_errors:
                exchange_errors[exchange] += 1

    # Calculate total errors
    total_errors = sum(error_types.values())

    # Generate recommendations based on error patterns
    recommendations = []

    # Check for common error types
    if 'Historical Market Data Service error message:' in error_types:
        recommendations.append("Consider using a different historical data source or API endpoint")

    if 'HMDS query returned no data:' in error_types:
        recommendations.append("Some symbols may not have historical data available - consider filtering these out")

    if 'pacing violation' in str(error_types).lower():
        recommendations.append("Reduce request frequency or batch size to avoid pacing violations")

    # Check for exchange-specific issues
    worst_exchange = max(exchange_errors.items(), key=lambda x: x[1])[0] if exchange_errors else None
    if worst_exchange and exchange_errors[worst_exchange] > 0:
        recommendations.append(f"Consider using a different exchange than {worst_exchange} for historical data requests")

    # Add general recommendations
    if total_errors > 0:
        recommendations.append("Implement retry logic with exponential backoff for failed requests")
        recommendations.append("Consider caching historical data to reduce API calls")

    # If no specific recommendations, add a generic one
    if not recommendations:
        recommendations.append("No specific recommendations based on error patterns")

    # Return analysis results
    return {
        'error_count': total_errors,
        'symbols_with_errors': len(symbols_with_errors),
        'error_types': error_types,
        'exchange_errors': exchange_errors,
        'recommendations': recommendations
    }

def finalize_opening_ranges(app, max_duration=None):
    """
    Finalize opening ranges after the 5-minute window and fetch daily trend data

    Args:
        app: The IBApp instance
        max_duration (timedelta, optional): Maximum duration for trend data collection.
                                           If provided, will limit trend data collection time.
    """
    # Log wrapper call for method extraction verification
    log_wrapper_call("finalize_opening_ranges", max_duration)

    # CRITICAL FIX: Normalize opening_range_data keys before processing
    if hasattr(app, 'opening_range_data'):
        normalized_opening_range_data = {}
        non_string_symbols = []

        for symbol, data in list(app.opening_range_data.items()):
            if not isinstance(symbol, str):
                non_string_symbols.append((symbol, type(symbol)))
                norm_symbol = ensure_symbol_string(app, symbol)
                if norm_symbol != "UNKNOWN":
                    normalized_opening_range_data[norm_symbol] = data
                    logging.warning(f"CRITICAL FIX: Normalized symbol in opening_range_data: {symbol} -> {norm_symbol}")
            else:
                normalized_opening_range_data[symbol] = data

        if non_string_symbols:
            logging.error(f"CRITICAL ERROR: Found {len(non_string_symbols)} non-string symbols in opening_range_data")
            for symbol, symbol_type in non_string_symbols[:5]:  # Show first 5 only
                logging.error(f"Non-string symbol: {symbol} (type: {symbol_type})")

            if len(normalized_opening_range_data) != len(app.opening_range_data):
                logging.warning(f"Replacing opening_range_data with normalized version")
                logging.warning(f"Original size: {len(app.opening_range_data)}, New size: {len(normalized_opening_range_data)}")
                app.opening_range_data = normalized_opening_range_data

    symbol_count = len(app.opening_range_data)
    logging.info(f"Finalizing opening ranges for {symbol_count} symbols")

    # Record start time for duration tracking
    start_time = datetime.now(pytz.timezone('US/Eastern'))

    if symbol_count == 0:
        logging.warning("No opening range data available for any symbols")
        return

    # Calculate statistics for summary
    valid_ranges = 0
    total_width = 0
    max_width = 0
    max_width_symbol = ""
    min_width = float('inf')
    min_width_symbol = ""
    high_volume_symbols = []

    # Process all symbols to gather statistics
    for symbol, data in app.opening_range_data.items():
        # CRITICAL FIX: Ensure symbol is a string
        if not isinstance(symbol, str):
            logging.error(f"CRITICAL ERROR: Symbol is not a string after normalization: {type(symbol)}")
            logging.error(f"Symbol value: {symbol}")
            continue

        range_width = ((data['high'] - data['low']) / data['low']) * 100 if data['low'] > 0 else 0

        # Count valid ranges (non-zero width)
        if range_width > 0:
            valid_ranges += 1
            total_width += range_width

            # Track max width
            if range_width > max_width:
                max_width = range_width
                max_width_symbol = symbol

            # Track min width
            if range_width < min_width:
                min_width = range_width
                min_width_symbol = symbol

        # Track high volume symbols
        if data['volume'] > 1000:
            high_volume_symbols.append((symbol, data['volume']))

    # Calculate average width
    avg_width = total_width / valid_ranges if valid_ranges > 0 else 0

    # Sort high volume symbols and get top 5
    high_volume_symbols.sort(key=lambda x: x[1], reverse=True)
    top_volume_symbols = high_volume_symbols[:5]

    # Log summary
    logging.info(f"Opening range summary:")
    logging.info(f"- Total symbols with opening range data: {symbol_count}")
    logging.info(f"- Symbols with valid ranges (width > 0): {valid_ranges}")
    logging.info(f"- Average opening range width: {avg_width:.2f}%")

    if max_width_symbol:
        logging.info(f"- Widest opening range: {max_width_symbol} ({max_width:.2f}%)")

    if min_width_symbol and min_width < float('inf'):
        logging.info(f"- Narrowest opening range: {min_width_symbol} ({min_width:.2f}%)")

    if top_volume_symbols:
        volume_str = ", ".join([f"{s[0]} ({s[1]})" for s in top_volume_symbols])
        logging.info(f"- Top volume symbols: {volume_str}")

    # Log detailed data at debug level
    for symbol, data in app.opening_range_data.items():
        range_width = ((data['high'] - data['low']) / data['low']) * 100 if data['low'] > 0 else 0
        logging.debug(f"Final opening range for {symbol}: High={data['high']:.2f}, Low={data['low']:.2f}, Width={range_width:.2f}%, Volume={data['volume']}")

    # Fetch daily trend data for top candidates if trend alignment is enabled
    if hasattr(app, 'use_trend_alignment') and app.use_trend_alignment:
        logging.info("Fetching daily trend data for top candidates...")

        # Create a dictionary to store trend data
        app.daily_trend_data = {}

        # Process top candidates (both long and short)
        # We'll process the top 20 candidates from each list to avoid excessive API calls
        top_candidates = []

        # Get all candidates and sort by priority score
        all_candidates = []

        if hasattr(app, 'longCandidates') and app.longCandidates:
            for candidate in app.longCandidates:
                # CRITICAL FIX: Ensure symbol is a string
                if 'symbol' in candidate:
                    symbol_raw = candidate['symbol']
                    if not isinstance(symbol_raw, str):
                        norm_symbol = ensure_symbol_string(app, symbol_raw)
                        if norm_symbol != "UNKNOWN":
                            logging.warning(f"CRITICAL FIX: Normalized symbol in longCandidates: {symbol_raw} -> {norm_symbol}")
                            candidate['symbol'] = norm_symbol
                        else:
                            logging.error(f"CRITICAL ERROR: Invalid symbol in longCandidates: {symbol_raw} (type: {type(symbol_raw)})")
                            continue

                candidate['direction'] = 'LONG'
                all_candidates.append(candidate)

        if hasattr(app, 'shortCandidates') and app.shortCandidates:
            for candidate in app.shortCandidates:
                # CRITICAL FIX: Ensure symbol is a string
                if 'symbol' in candidate:
                    symbol_raw = candidate['symbol']
                    if not isinstance(symbol_raw, str):
                        norm_symbol = ensure_symbol_string(app, symbol_raw)
                        if norm_symbol != "UNKNOWN":
                            logging.warning(f"CRITICAL FIX: Normalized symbol in shortCandidates: {symbol_raw} -> {norm_symbol}")
                            candidate['symbol'] = norm_symbol
                        else:
                            logging.error(f"CRITICAL ERROR: Invalid symbol in shortCandidates: {symbol_raw} (type: {type(symbol_raw)})")
                            continue

                candidate['direction'] = 'SHORT'
                all_candidates.append(candidate)

        # Sort all candidates by priority score (highest first)
        all_candidates.sort(key=lambda x: x.get('priority_score', 0), reverse=True)

        # Take top 40 candidates (or fewer if there aren't that many)
        top_candidates = all_candidates[:40]

        logging.info(f"Selected top {len(top_candidates)} candidates for trend data collection based on priority score")

        # CRITICAL FIX: Preserve candidates immediately after selection
        # This ensures candidates are preserved even if the phase transition happens before the end of this method
        try:
            from orbscanner.utils.phase.manager import PhaseManager

            # Log to both critical log and FINALIZING_PHASE log
            logging.critical("CRITICAL FIX: Early candidate preservation after selection")
            log_to_phase(PhaseManager.FINALIZING, "CRITICAL FIX: Early candidate preservation after selection")

            # Log current state of app's candidate lists
            if hasattr(app, 'longCandidates'):
                long_count = len(app.longCandidates)
                logging.critical(f"EARLY PRESERVATION: Current longCandidates count: {long_count}")
                log_to_phase(PhaseManager.FINALIZING, f"EARLY PRESERVATION: Current longCandidates count: {long_count}")

                # Log sample of symbols
                if app.longCandidates:
                    long_sample = [c.get('symbol', 'unknown') if isinstance(c, dict) else c for c in app.longCandidates[:5]]
                    logging.critical(f"EARLY PRESERVATION: Long candidates sample: {long_sample}")
                    log_to_phase(PhaseManager.FINALIZING, f"EARLY PRESERVATION: Long candidates sample: {long_sample}")
            else:
                logging.critical("EARLY PRESERVATION: No longCandidates attribute found")
                log_to_phase(PhaseManager.FINALIZING, "EARLY PRESERVATION: No longCandidates attribute found")

            if hasattr(app, 'shortCandidates'):
                short_count = len(app.shortCandidates)
                logging.critical(f"EARLY PRESERVATION: Current shortCandidates count: {short_count}")
                log_to_phase(PhaseManager.FINALIZING, f"EARLY PRESERVATION: Current shortCandidates count: {short_count}")

                # Log sample of symbols
                if app.shortCandidates:
                    short_sample = [c.get('symbol', 'unknown') if isinstance(c, dict) else c for c in app.shortCandidates[:5]]
                    logging.critical(f"EARLY PRESERVATION: Short candidates sample: {short_sample}")
                    log_to_phase(PhaseManager.FINALIZING, f"EARLY PRESERVATION: Short candidates sample: {short_sample}")
            else:
                logging.critical("EARLY PRESERVATION: No shortCandidates attribute found")
                log_to_phase(PhaseManager.FINALIZING, "EARLY PRESERVATION: No shortCandidates attribute found")

            # Check if the preserve_candidates_for_monitoring method exists
            if hasattr(app, 'preserve_candidates_for_monitoring'):
                logging.critical("EARLY PRESERVATION: Calling preserve_candidates_for_monitoring method")
                log_to_phase(PhaseManager.FINALIZING, "EARLY PRESERVATION: Calling preserve_candidates_for_monitoring method")

                # Call the preservation method
                preserved_long_count, preserved_short_count = app.preserve_candidates_for_monitoring()

                logging.critical(f"CRITICAL FIX: Early preservation saved {preserved_long_count} long and {preserved_short_count} short candidates")
                log_to_phase(PhaseManager.FINALIZING, f"CRITICAL FIX: Early preservation saved {preserved_long_count} long and {preserved_short_count} short candidates")

                # Update phase manager with the correct count of valid symbols
                if hasattr(app, 'phase_manager') and app.phase_manager is not None:
                    total_preserved = preserved_long_count + preserved_short_count
                    if total_preserved > 0:
                        logging.critical(f"CRITICAL FIX: Early updating phase manager with preserved candidate count: {total_preserved}")
                        log_to_phase(PhaseManager.FINALIZING, f"CRITICAL FIX: Early updating phase manager with preserved candidate count: {total_preserved}")

                        try:
                            app.phase_manager.update_symbol_validation(PhaseManager.FINALIZING, total_preserved, total_preserved)
                            logging.critical(f"CRITICAL FIX: Phase manager updated with valid symbols for FINALIZING: {total_preserved}")
                            log_to_phase(PhaseManager.FINALIZING, f"CRITICAL FIX: Phase manager updated with valid symbols for FINALIZING: {total_preserved}")
                        except Exception as pm_error:
                            logging.critical(f"CRITICAL ERROR: Failed to update phase manager: {pm_error}")
                            log_to_phase(PhaseManager.FINALIZING, f"CRITICAL ERROR: Failed to update phase manager: {pm_error}", level=logging.ERROR)
                    else:
                        logging.critical("CRITICAL WARNING: No candidates were preserved, not updating phase manager")
                        log_to_phase(PhaseManager.FINALIZING, "CRITICAL WARNING: No candidates were preserved, not updating phase manager", level=logging.WARNING)
                else:
                    logging.critical("CRITICAL WARNING: Phase manager not available, cannot update symbol validation")
                    log_to_phase(PhaseManager.FINALIZING, "CRITICAL WARNING: Phase manager not available, cannot update symbol validation", level=logging.WARNING)
            else:
                logging.critical("CRITICAL ERROR: App instance does not have preserve_candidates_for_monitoring method")
                log_to_phase(PhaseManager.FINALIZING, "CRITICAL ERROR: App instance does not have preserve_candidates_for_monitoring method", level=logging.ERROR)
        except Exception as e:
            logging.critical(f"CRITICAL ERROR: Failed during early candidate preservation: {e}")
            log_to_phase(PhaseManager.FINALIZING, f"CRITICAL ERROR: Failed during early candidate preservation: {e}", level=logging.ERROR)

            import traceback
            stack_trace = traceback.format_exc()
            logging.critical(f"Traceback: {stack_trace}")
            log_to_phase(PhaseManager.FINALIZING, f"Traceback: {stack_trace}", level=logging.ERROR)

        # Check if we have a max_duration parameter
        if max_duration is not None:
            end_time = start_time + max_duration
            logging.info(f"Trend data collection will be limited to {max_duration.total_seconds()/60:.1f} minutes")
        else:
            # Default to 5 minutes
            end_time = start_time + timedelta(minutes=5)
            logging.info("Trend data collection will be limited to 5 minutes (default)")

        # Process candidates in batches to avoid overwhelming the API
        batch_size = 5
        candidate_batches = [top_candidates[i:i+batch_size] for i in range(0, len(top_candidates), batch_size)]

        for batch_idx, candidate_batch in enumerate(candidate_batches):
            logging.info(f"Processing trend data batch {batch_idx+1}/{len(candidate_batches)} ({len(candidate_batch)} candidates)")

            # Check if we've exceeded the time limit
            current_time = datetime.now(pytz.timezone('US/Eastern'))
            if current_time > end_time:
                logging.warning(f"Reached time limit for trend data collection. Processed {batch_idx*batch_size + len(candidate_batch)}/{len(top_candidates)} candidates")
                break

            # Process each candidate in the batch
            for candidate in candidate_batch:
                symbol = candidate.get('symbol')
                direction = candidate.get('direction')

                if not symbol or not isinstance(symbol, str):
                    logging.error(f"Invalid symbol in candidate: {symbol}")
                    continue

                try:
                    # Get daily trend data for this symbol
                    # This would call a method to fetch daily trend data
                    # For now, we'll just log that we would fetch it
                    logging.info(f"Would fetch daily trend data for {symbol} ({direction})")

                    # In a real implementation, this would call a method like:
                    # trend_data = app.get_daily_trend_data(symbol)
                    # app.daily_trend_data[symbol] = trend_data
                except Exception as e:
                    logging.error(f"Exception while getting trend data for {symbol}: {e}")
                    logging.error(traceback.format_exc())

            # Small delay between batches to avoid overwhelming the API
            if batch_idx < len(candidate_batches) - 1:
                time.sleep(2)

        # CRITICAL FIX: Ensure volume data is propagated to candidate objects
        logging.info("Copying volume data from opening_range_data to candidate objects")
        for symbol, data in app.opening_range_data.items():
            volume = data.get('volume', 0)
            if volume > 0:
                # Update in longCandidates
                for candidate in app.longCandidates:
                    if candidate.get('symbol') == symbol:
                        candidate['volume'] = volume
                        logging.info(f"Updated volume for LONG {symbol}: {volume}")
                # Update in shortCandidates
                for candidate in app.shortCandidates:
                    if candidate.get('symbol') == symbol:
                        candidate['volume'] = volume
                        logging.info(f"Updated volume for SHORT {symbol}: {volume}")

        # Log summary of trend data collection
        end_time = datetime.now(pytz.timezone('US/Eastern'))
        duration = (end_time - start_time).total_seconds() / 60.0
        logging.info(f"Trend data collection complete in {duration:.1f} minutes")

    # Call analyze_historical_data_errors
    try:
        error_analysis = analyze_historical_data_errors(app)
        if error_analysis and error_analysis.get('error_count', 0) > 0:
            logging.info(f"Historical data error analysis: {error_analysis.get('error_count')} errors across {error_analysis.get('symbols_with_errors')} symbols")
            for recommendation in error_analysis.get('recommendations', []):
                logging.info(f"Recommendation: {recommendation}")
    except Exception as e:
        logging.error(f"Error in historical data error analysis: {e}")
        logging.error(traceback.format_exc())
