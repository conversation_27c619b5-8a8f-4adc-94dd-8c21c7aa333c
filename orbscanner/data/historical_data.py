"""
Historical data processing utilities for the ORB Scanner.

This module provides functions for retrieving and processing historical data
from Interactive Brokers, including opening range data and daily trend data.
"""

import logging
import threading
import time
import traceback
import inspect
from datetime import datetime, timedelta
import pytz

from ibapi.contract import Contract
from ibapi.common import BarData

# Import tracing utilities if available
try:
    from orbscanner.utils.data.tracing_integration import trace_data_request
except ImportError:
    # Create dummy decorator if tracing is not available
    def trace_data_request(request_type):
        def decorator(func):
            return func
        return decorator

# Constants (imported from scanner.py)
HISTORICAL_DATA_TIMEOUT = 30  # seconds to wait for historical data
HISTORICAL_DATA_RETRIES = 1  # number of retries for historical data requests


def ensure_symbol_string(app, symbol):
    """
    Utility function to ensure a symbol is a valid string.
    This is a critical fix for the symbol type inconsistency issue where object references
    are incorrectly used instead of proper string symbols in historical opening range requests.

    Args:
        app: The IBApp instance
        symbol: The symbol to validate and convert if needed

    Returns:
        str: The validated symbol as a string, or "UNKNOWN" if conversion fails
    """
    # If already a string, check for object references in the string
    if isinstance(symbol, str):
        # Check if the string looks like an object reference
        if '<' in symbol and '>' in symbol and 'object at' in symbol:
            logging.critical(f"CRITICAL ERROR: String symbol appears to be an object reference: {symbol}")
            # Log the call stack to help identify where this is happening
            stack_trace = ''.join(traceback.format_stack())
            logging.critical(f"Call stack for object reference string:\n{stack_trace}")
            return "UNKNOWN"
        return symbol

    # If it's an IBApp object (app), this is definitely wrong
    if symbol is app:
        logging.critical(f"CRITICAL ERROR: 'app' was passed as a symbol parameter")
        # Log the call stack to help identify where this is happening
        stack_trace = ''.join(traceback.format_stack())
        logging.critical(f"Call stack:\n{stack_trace}")

        # CRITICAL FIX: Add more detailed error information to help diagnose the issue
        logging.critical("This is a critical bug where the IBApp object itself is being passed as a symbol")
        logging.critical("This typically happens when a method is called with 'self' as the first argument")
        logging.critical("Check for calls like: self.some_method(self, ...) instead of self.some_method(...)")

        # Log the caller information to help identify the source
        caller_frame = inspect.currentframe().f_back
        if caller_frame:
            caller_info = inspect.getframeinfo(caller_frame)
            logging.critical(f"CRITICAL DIAGNOSTIC: Called from {caller_info.filename}:{caller_info.lineno} in function {caller_info.function}")

            # Try to get the calling code
            if caller_info.code_context:
                logging.critical(f"CRITICAL DIAGNOSTIC: Calling code: {caller_info.code_context[0].strip()}")

        return "UNKNOWN"  # Return "UNKNOWN" instead of None for better error handling

    # Try to convert to string if possible
    try:
        # Try to convert to string if it's a simple type
        if hasattr(symbol, 'symbol'):
            # If it's an object with a symbol attribute, use that
            symbol_str = symbol.symbol
            if not isinstance(symbol_str, str):
                # Handle case where symbol attribute is not a string
                logging.error(f"CRITICAL ERROR: Object's symbol attribute is not a string: {type(symbol_str).__name__}")
                return "UNKNOWN"

            logging.warning(f"CRITICAL FIX: Converted object to string using symbol attribute: {symbol_str}")

            # CRITICAL FIX: Log the call stack to help identify where this is happening
            stack_trace = ''.join(traceback.format_stack())
            logging.warning(f"Call stack for object with symbol attribute:\n{stack_trace}")

            # Log the object type for better debugging
            logging.warning(f"Original object type: {type(symbol).__name__}")

            return symbol_str
        elif hasattr(symbol, 'contract') and hasattr(symbol.contract, 'symbol'):
            # Handle case where it's an object with a contract attribute that has a symbol
            symbol_str = symbol.contract.symbol
            if not isinstance(symbol_str, str):
                logging.error(f"CRITICAL ERROR: Object's contract.symbol attribute is not a string: {type(symbol_str).__name__}")
                return "UNKNOWN"

            logging.warning(f"CRITICAL FIX: Converted object to string using contract.symbol attribute: {symbol_str}")

            # Log the object type for better debugging
            logging.warning(f"Original object type: {type(symbol).__name__}")

            return symbol_str
        else:
            # Otherwise try direct string conversion
            symbol_str = str(symbol)
            # Check if the conversion resulted in something useful (not just object representation)
            if '<' in symbol_str and '>' in symbol_str and 'object at' in symbol_str:
                logging.error(f"CRITICAL ERROR: String conversion resulted in object representation: {symbol_str}")
                # Log the call stack to help identify where this is happening
                stack_trace = ''.join(traceback.format_stack())
                logging.error(f"Call stack for object representation:\n{stack_trace}")

                # Log the object type for better debugging
                logging.error(f"Original object type: {type(symbol).__name__}")

                return "UNKNOWN"  # Return "UNKNOWN" instead of None

            logging.warning(f"CRITICAL FIX: Converted {type(symbol).__name__} to string: {symbol_str}")
            return symbol_str
    except Exception as e:
        logging.error(f"CRITICAL ERROR: Failed to convert symbol to string: {e}")
        logging.error(f"Traceback: {traceback.format_exc()}")

        # Log the object type for better debugging
        try:
            logging.error(f"Original object type: {type(symbol).__name__}")
        except:
            logging.error("Could not determine original object type")

        return "UNKNOWN"  # Return "UNKNOWN" instead of None


@trace_data_request("historical_opening_range")
def get_historical_opening_range_with_exchange(app, symbol, market_open_time=None, exchange="SMART"):
    """
    Get historical opening range data for a symbol using a specific exchange.

    Args:
        app: The IBApp instance
        symbol (str): The symbol to get opening range for
        market_open_time (datetime, optional): Market open time. If None, uses 9:30 AM today.
        exchange (str): Exchange to use for routing (SMART, ARCA, NYSE, NASDAQ, etc.)

    Returns:
        dict: Dictionary with high, low, open, and volume data, or None if data retrieval failed
    """
    # CRITICAL FIX: Use the enhanced ensure_symbol_string utility function
    original_symbol = symbol
    symbol = ensure_symbol_string(app, symbol)

    if symbol == "UNKNOWN":
        logging.error(f"CRITICAL ERROR: Invalid symbol passed to get_historical_opening_range_with_exchange")
        logging.error(f"Original symbol type: {type(original_symbol).__name__}")
        logging.error(f"This is likely causing the 'IBApp object' error in historical data requests")

        # Log the call stack to help identify where this is happening
        stack_trace = ''.join(traceback.format_stack())
        logging.error(f"Call stack for invalid symbol in get_historical_opening_range_with_exchange:\n{stack_trace}")

        return None

    logging.info(f"Requesting historical opening range data for {symbol} using {exchange} exchange")

    # Create contract with specified exchange using standardized function
    contract = app.create_standard_contract(symbol)
    # Override the exchange to use the specified one
    contract.exchange = exchange

    # Use the same request ID generation and event handling as the original method
    req_id = app.get_next_req_id()
    app.reqId_to_symbol[req_id] = symbol

    # Create event to wait for data
    event = threading.Event()
    app.historical_data_events[req_id] = event
    app.historical_data_buffer[req_id] = []
    app.historical_data_finished[req_id] = False

    # Set market open time if not provided
    if market_open_time is None:
        current_time = datetime.now(pytz.timezone('America/New_York'))
        market_open_time = current_time.replace(hour=9, minute=30, second=0, microsecond=0)

    # Track request start time for performance monitoring
    start_time = time.time()

    # CRITICAL FIX: Use the working parameters from the sanity check
    # Use empty endDateTime, 1 D duration, and useRTH=0
    logging.info(f"Historical data request parameters for {symbol}:")
    logging.info(f"  Using API test parameters that work in sanity check")
    logging.info(f"  endDateTime: '' (empty string)")
    logging.info(f"  durationStr: '1 D' (1 day)")
    logging.info(f"  barSizeSetting: '1 min'")
    logging.info(f"  useRTH: 0 (include all data)")
    logging.info(f"  Exchange: {exchange}")
    logging.info(f"  Will post-process to extract first 7 minutes after market open")

    # Prepare request parameters with working parameters from sanity check
    request_params = {
        "reqId": req_id,
        "symbol": symbol,
        "secType": contract.secType,
        "exchange": contract.exchange,
        "endDateTime": "",  # Empty string - use most recent data
        "durationStr": "1 D",  # 1 day - will include market open period
        "barSizeSetting": "1 min",  # 1-minute bars
        "whatToShow": "TRADES",
        "useRTH": 0,  # Include all data, not just regular trading hours
        "formatDate": 1,
        "keepUpToDate": False,
        "start_time": start_time
    }

    # CRITICAL FIX: Add more detailed logging for debugging
    logging.info(f"Historical data request parameters for {symbol}:")
    logging.info(f"  Symbol: {symbol} (type: {type(symbol).__name__})")
    logging.info(f"  Contract: {contract.symbol} {contract.secType} {contract.exchange} {contract.currency}")
    logging.info(f"  End time: {request_params['endDateTime']}")
    logging.info(f"  Duration: {request_params['durationStr']}")
    logging.info(f"  Bar size: {request_params['barSizeSetting']}")
    logging.info(f"  What to show: {request_params['whatToShow']}")
    logging.info(f"  Use RTH: {request_params['useRTH']}")

    # Log the full parameters for reference
    logging.debug(f"Full request parameters: {request_params}")

    try:
        # CRITICAL FIX: Add pre-request validation
        if not contract.symbol or not isinstance(contract.symbol, str):
            logging.error(f"CRITICAL ERROR: Invalid contract symbol: {contract.symbol} (type: {type(contract.symbol).__name__})")
            logging.error(f"This would cause the request to fail. Aborting request.")
            return None

        # Make the request with simplified parameters
        app.reqHistoricalData(
            reqId=req_id,
            contract=contract,
            endDateTime=request_params['endDateTime'],
            durationStr=request_params['durationStr'],
            barSizeSetting=request_params['barSizeSetting'],
            whatToShow=request_params['whatToShow'],
            useRTH=request_params['useRTH'],
            formatDate=request_params['formatDate'],
            keepUpToDate=request_params['keepUpToDate'],
            chartOptions=[]
        )

        # Log successful request submission
        logging.info(f"Historical data request submitted for {symbol} with reqId={req_id}")

        # Wait for data with timeout
        received = event.wait(timeout=HISTORICAL_DATA_TIMEOUT)

        # Check if we received data
        if not received or not app.historical_data_finished.get(req_id, False):
            # Check if we have a specific error recorded
            error_details = None
            if symbol in app.historical_data_errors:
                latest_error = app.historical_data_errors[symbol][-1] if app.historical_data_errors[symbol] else None
                if latest_error and latest_error['request_id'] == req_id:
                    error_details = f"Error {latest_error['error_code']}: {latest_error['error_message']}"
                    logging.warning(f"Historical data request for {symbol} using {exchange} exchange failed: {error_details}")
                else:
                    logging.warning(f"Timeout waiting for historical data for {symbol} using {exchange} exchange")
            else:
                logging.warning(f"Timeout waiting for historical data for {symbol} using {exchange} exchange")

            # Update exchange success rate
            if exchange in app.exchange_success_rates:
                app.exchange_success_rates[exchange]['failure'] += 1

            # Store error details for the symbol
            if symbol not in app.historical_data_errors:
                app.historical_data_errors[symbol] = []

            app.historical_data_errors[symbol].append({
                'error_code': 'TIMEOUT',
                'error_message': error_details if error_details else 'Request timed out',
                'timestamp': datetime.now(),
                'request_id': req_id,
                'error_type': 'TIMEOUT',
                'exchange': exchange,
                'request_duration': time.time() - start_time
            })

            return None

        # Process the data
        all_bars = app.historical_data_buffer.get(req_id, [])

        # Check if we received any bars
        if not all_bars:
            logging.warning(f"No historical data bars received for {symbol} using {exchange} exchange")

            # Update exchange success rate
            if exchange in app.exchange_success_rates:
                app.exchange_success_rates[exchange]['failure'] += 1

            # Store error details for the symbol
            if symbol not in app.historical_data_errors:
                app.historical_data_errors[symbol] = []

            app.historical_data_errors[symbol].append({
                'error_code': 'NO_DATA',
                'error_message': 'No data bars received despite successful request',
                'timestamp': datetime.now(),
                'request_id': req_id,
                'error_type': 'EMPTY_RESPONSE',
                'exchange': exchange,
                'request_duration': time.time() - start_time
            })

            return None

        # Log the total number of bars received
        logging.info(f"Received {len(all_bars)} total bars for {symbol} using {exchange} exchange")

        # CRITICAL FIX: Post-process to extract only the first 7 minutes after market open
        # Find market open time (9:30 AM Eastern)
        market_open_bars = []
        market_open_time_str = None

        # Log a few bars to help with debugging
        if len(all_bars) > 0:
            logging.info(f"First few bars for {symbol}:")
            for i, bar in enumerate(all_bars[:5]):
                logging.info(f"  Bar {i+1}: Date={bar.date}, Open={bar.open}, High={bar.high}, Low={bar.low}, Close={bar.close}, Volume={bar.volume}")

        # Look for the 9:30 AM bar (market open)
        for bar in all_bars:
            # Parse the bar time
            bar_time = bar.date  # Format: YYYYMMDD HH:MM:SS

            # Check if this is a market open bar (9:30 AM)
            if " 09:30:00" in bar_time:
                market_open_time_str = bar_time
                logging.info(f"Found market open bar for {symbol}: {bar_time}")
                break

        if market_open_time_str:
            # Extract the first 7 minutes of bars starting from market open
            for bar in all_bars:
                bar_time = bar.date

                # Check if the bar is from the same day as market open
                if bar_time.split()[0] == market_open_time_str.split()[0]:
                    bar_hour_min = bar_time.split()[1][:5]  # Extract HH:MM

                    # Check if bar is within the first 7 minutes after market open (9:30 to 9:36)
                    if "09:30" <= bar_hour_min <= "09:36":
                        market_open_bars.append(bar)

            logging.info(f"Extracted {len(market_open_bars)} bars from market open period (9:30-9:37) for {symbol}")

            # Use the market open bars for the opening range calculation
            bars = market_open_bars
        else:
            logging.warning(f"Could not find market open bar (9:30 AM) for {symbol} - attempting alternative approach")

            # Try to find any bars in the 9:30-9:36 range regardless of date
            for bar in all_bars:
                bar_time = bar.date
                bar_hour_min = bar_time.split()[1][:5]  # Extract HH:MM

                # Check if bar is within the opening range time window (9:30 to 9:36)
                if "09:30" <= bar_hour_min <= "09:36":
                    market_open_bars.append(bar)

            if market_open_bars:
                logging.info(f"Found {len(market_open_bars)} bars in the 9:30-9:36 time window for {symbol}")
                bars = market_open_bars
            else:
                # If we still can't find any bars in the opening range window, try to use the earliest bars of the day
                logging.warning(f"Could not find any bars in the 9:30-9:36 time window for {symbol}")

                # Sort bars by time
                sorted_bars = sorted(all_bars, key=lambda x: x.date)

                # Take the first 7 bars if available
                if len(sorted_bars) >= 7:
                    bars = sorted_bars[:7]
                    logging.info(f"Using the first 7 bars of the day for {symbol} (earliest bars method)")
                    logging.info(f"First bar time: {bars[0].date}, Last bar time: {bars[6].date}")
                else:
                    bars = sorted_bars
                    logging.info(f"Using all {len(bars)} available bars for {symbol} (earliest bars method)")
                    if bars:
                        logging.info(f"First bar time: {bars[0].date}, Last bar time: {bars[-1].date}")

            # Track that we used an alternative filtering method
            app.opening_range_alt_filter_used[symbol] = True

        # Calculate opening range from the selected bars
        high = max(bar.high for bar in bars) if bars else 0
        low = min(bar.low for bar in bars) if bars else 0
        open_price = bars[0].open if bars else 0
        volume = sum(bar.volume for bar in bars) if bars else 0

        # Check for zero volume
        if volume <= 0:
            logging.warning(f"{symbol} has ZERO TOTAL VOLUME using {exchange} exchange - data quality issue")

            # Update exchange success rate
            if exchange in app.exchange_success_rates:
                app.exchange_success_rates[exchange]['failure'] += 1

            # Store error details for the symbol
            if symbol not in app.historical_data_errors:
                app.historical_data_errors[symbol] = []

            app.historical_data_errors[symbol].append({
                'error_code': 'ZERO_VOLUME',
                'error_message': 'Zero total volume in historical data',
                'timestamp': datetime.now(),
                'request_id': req_id,
                'error_type': 'DATA_QUALITY',
                'exchange': exchange,
                'request_duration': time.time() - start_time,
                'bar_count': len(bars)
            })

            return None

        # Calculate request duration for performance monitoring
        duration = time.time() - start_time

        # Log success with detailed information
        logging.info(f"Historical opening range for {symbol} using {exchange}: High={high:.2f}, Low={low:.2f}, Width={((high-low)/low*100):.2f}%, Bars={len(bars)}/{len(all_bars)} (opening range/total), Volume={volume}")
        logging.info(f"Request completed in {duration:.2f} seconds with {len(bars)} opening range bars extracted from {len(all_bars)} total bars")

        # Update exchange success rate
        if exchange in app.exchange_success_rates:
            app.exchange_success_rates[exchange]['success'] += 1

        # Calculate success rate for logging
        if exchange in app.exchange_success_rates:
            success_count = app.exchange_success_rates[exchange]['success']
            total_count = success_count + app.exchange_success_rates[exchange]['failure']
            success_rate = (success_count / total_count) * 100 if total_count > 0 else 0
            logging.info(f"Exchange {exchange} success rate: {success_rate:.1f}% ({success_count}/{total_count})")

        # Return opening range data with enhanced metadata
        return {
            'high': high,
            'low': low,
            'open': open_price,
            'volume': volume,
            'source': f'historical-{exchange.lower()}',
            'bars': len(bars),
            'request_duration': duration,
            'bar_count': len(bars),
            'total_bars': len(all_bars),
            'timestamp': datetime.now(),
            'filter_method': 'exact_market_open' if market_open_time_str else 'time_window' if market_open_bars else 'earliest_bars'
        }

    except Exception as e:
        # Calculate request duration for performance monitoring
        duration = time.time() - start_time

        logging.error(f"Error getting historical opening range for {symbol} using {exchange}: {e}")
        logging.error(f"Request failed after {duration:.2f} seconds")

        # Update exchange success rate
        if exchange in app.exchange_success_rates:
            app.exchange_success_rates[exchange]['failure'] += 1

        # Store error details for the symbol
        if symbol not in app.historical_data_errors:
            app.historical_data_errors[symbol] = []

        app.historical_data_errors[symbol].append({
            'error_code': 'EXCEPTION',
            'error_message': str(e),
            'timestamp': datetime.now(),
            'request_id': req_id,
            'error_type': 'EXCEPTION',
            'exchange': exchange,
            'request_duration': duration,
            'traceback': traceback.format_exc()
        })

        return None
    finally:
        # Clean up
        if req_id in app.historical_data_buffer:
            del app.historical_data_buffer[req_id]
        if req_id in app.historical_data_finished:
            del app.historical_data_finished[req_id]
        if req_id in app.historical_data_events:
            del app.historical_data_events[req_id]
        if req_id in app.reqId_to_symbol:
            del app.reqId_to_symbol[req_id]


@trace_data_request("historical_opening_range")
def get_historical_opening_range(app, symbol, market_open_time=None):
    """
    Get historical opening range data for a symbol.

    Args:
        app: The IBApp instance
        symbol (str): The symbol to get opening range for
        market_open_time (datetime, optional): Market open time. If None, uses 9:30 AM today.

    Returns:
        dict: Dictionary with high, low, open, and volume data, or None if data retrieval failed
    """
    # ENHANCED DIAGNOSTIC LOGGING: Log detailed symbol information at entry point
    logging.info(f"get_historical_opening_range called with symbol: {str(symbol)}, type: {type(symbol).__name__}")

    # Log id of the symbol object to help identify duplicates
    try:
        symbol_id = id(symbol)
        logging.debug(f"Symbol object ID: {symbol_id}")

        # Check if symbol is self
        if symbol is app:
            logging.critical(f"CRITICAL ERROR: Symbol is the IBApp object itself (app)")
            logging.critical(f"App object ID: {id(app)}")
    except Exception as e:
        logging.error(f"Error getting symbol ID: {e}")

    # Get caller information for diagnostic purposes
    caller_frame = inspect.currentframe().f_back
    if caller_frame:
        caller_info = inspect.getframeinfo(caller_frame)
        logging.info(f"Called from {caller_info.filename}:{caller_info.lineno} in function {caller_info.function}")
        if caller_info.code_context:
            logging.info(f"Calling code: {caller_info.code_context[0].strip()}")

        # Get the full call stack for better debugging
        stack_frames = inspect.getouterframes(caller_frame)
        if len(stack_frames) > 1:
            # Log the previous frame (the caller's caller)
            prev_frame = stack_frames[1]
            logging.debug(f"Caller's caller: {prev_frame.filename}:{prev_frame.lineno} in function {prev_frame.function}")
            if prev_frame.code_context:
                logging.debug(f"Caller's caller code: {prev_frame.code_context[0].strip()}")

    # CRITICAL FIX: Use the enhanced ensure_symbol_string utility function
    # This fixes the issue where object references are incorrectly used instead of proper string symbols
    original_symbol = symbol
    symbol = ensure_symbol_string(app, symbol)

    if symbol == "UNKNOWN":
        logging.error(f"CRITICAL ERROR: Invalid symbol passed to get_historical_opening_range")
        logging.error(f"Original symbol type: {type(original_symbol).__name__}")
        logging.error(f"This is likely causing the 'IBApp object' error in historical data requests")

        # CRITICAL FIX: Log additional diagnostic information
        if original_symbol is app:
            logging.error("CRITICAL DIAGNOSTIC: The IBApp object itself was passed as a symbol parameter")
            logging.error("This typically happens when a method is called with 'self' as the first argument")
            logging.error("Check for calls like: self.get_historical_opening_range(self, ...) instead of self.get_historical_opening_range(...)")

        # CRITICAL FIX: Log the caller information to help identify the source
        caller_frame = inspect.currentframe().f_back
        if caller_frame:
            caller_info = inspect.getframeinfo(caller_frame)
            logging.error(f"CRITICAL DIAGNOSTIC: Called from {caller_info.filename}:{caller_info.lineno} in function {caller_info.function}")

            # Try to get the calling code
            if caller_info.code_context:
                logging.error(f"CRITICAL DIAGNOSTIC: Calling code: {caller_info.code_context[0].strip()}")

        # Log the call stack to help identify where this is happening
        stack_trace = ''.join(traceback.format_stack())
        logging.error(f"Call stack for invalid symbol in get_historical_opening_range:\n{stack_trace}")

        return None

    # CRITICAL FIX: Ensure symbol is in tracked_symbols before requesting historical data
    # This breaks the circular dependency between tracking symbols and updating opening ranges
    if symbol not in app.tracked_symbols:
        logging.info(f"CRITICAL FIX: Symbol {symbol} not in tracked_symbols, adding it before requesting historical data")
        app.tracked_symbols[symbol] = {
            'first_seen': datetime.now().strftime('%H:%M:%S'),
            'last_price': 0.0,
            'scanner_type': 'HISTORICAL_REQUEST'
        }

    # Log the start of the historical data retrieval process
    logging.info(f"Starting historical opening range retrieval for {symbol}")

    # Track exchanges tried and their results
    exchanges_tried = []

    # Determine the order of exchanges to try based on success rates
    exchange_order = ["SMART", "ARCA", "NYSE", "NASDAQ"]

    # If we have success rate data, prioritize exchanges with higher success rates
    if hasattr(app, 'exchange_success_rates') and app.exchange_success_rates:
        # Calculate success rates for each exchange
        success_rates = {}
        for exch, stats in app.exchange_success_rates.items():
            total = stats['success'] + stats['failure']
            if total > 0:
                success_rates[exch] = (stats['success'] / total) * 100
            else:
                success_rates[exch] = 0

        # Sort exchanges by success rate (highest first)
        exchange_order = sorted(exchange_order, key=lambda x: success_rates.get(x, 0), reverse=True)
        logging.info(f"Exchange order based on success rates: {exchange_order}")

        # Log success rates for reference
        for exch in exchange_order:
            rate = success_rates.get(exch, 0)
            success = app.exchange_success_rates[exch]['success']
            total = success + app.exchange_success_rates[exch]['failure']
            logging.info(f"  {exch}: {rate:.1f}% success ({success}/{total})")

    # Try each exchange in order
    for exchange in exchange_order:
        exchanges_tried.append(exchange)
        logging.info(f"Trying {exchange} exchange for {symbol} (attempt {len(exchanges_tried)} of {len(exchange_order)})")

        # CRITICAL FIX: Pass the validated string symbol to the exchange-specific method
        result = get_historical_opening_range_with_exchange(app, symbol, market_open_time, exchange)

        if result is not None:
            logging.info(f"✅ Successfully retrieved historical data for {symbol} using {exchange} exchange")
            logging.info(f"Exchanges tried: {', '.join(exchanges_tried)}")

            # Add exchange information to the result
            result['exchanges_tried'] = exchanges_tried
            result['successful_exchange'] = exchange

            return result
        else:
            # Check if we have specific error information
            error_info = "Unknown error"
            if symbol in app.historical_data_errors:
                # Find the most recent error for this symbol and exchange
                for error in reversed(app.historical_data_errors[symbol]):
                    if error.get('exchange') == exchange:
                        error_info = f"{error.get('error_type', 'Unknown')}: {error.get('error_message', 'No details')}"
                        break

            logging.warning(f"❌ {exchange} exchange failed for {symbol}: {error_info}")

    # If we get here, all exchanges failed
    logging.error(f"❌ All exchanges failed for {symbol}. Exchanges tried: {', '.join(exchanges_tried)}")

    # Analyze errors to provide more detailed information
    if symbol in app.historical_data_errors:
        error_types = {}
        for error in app.historical_data_errors[symbol]:
            error_type = error.get('error_type', 'Unknown')
            error_types[error_type] = error_types.get(error_type, 0) + 1

        logging.error(f"Error summary for {symbol}: {error_types}")

        # Provide recommendations based on error patterns
        if 'TIMEOUT' in error_types and error_types['TIMEOUT'] >= 2:
            logging.error(f"Multiple timeout errors for {symbol} - possible network or API connectivity issue")
        if 'HMDS_ERROR' in error_types:
            logging.error(f"HMDS errors for {symbol} - check market data permissions and subscription level")
        if 'DATA_QUALITY' in error_types:
            logging.error(f"Data quality issues for {symbol} - symbol may have low liquidity or trading volume")

    return None