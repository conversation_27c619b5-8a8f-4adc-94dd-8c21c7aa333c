"""
Timing utilities for the 7-minute scanner.

This module provides common timing utilities used by both PhaseManager
and AfterOpenTimer to ensure consistent behavior.
"""

import logging
from datetime import datetime

from orbscanner.utils.time.timezone import get_eastern_time, get_time_diff_seconds

def calculate_elapsed_time(start_time, log_errors=True):
    """
    Calculate elapsed time since a given start time.

    Args:
        start_time (datetime): The start time
        log_errors (bool): Whether to log errors

    Returns:
        float: Elapsed time in seconds, or 0 if error
    """
    if not start_time:
        if log_errors:
            logging.warning("Cannot calculate elapsed time: No start time provided")
        return 0

    try:
        current_time = get_eastern_time()
        return get_time_diff_seconds(current_time, start_time)
    except Exception as e:
        if log_errors:
            logging.error(f"Error calculating elapsed time: {e}")
        return 0

def calculate_remaining_time(end_time, log_errors=True):
    """
    Calculate remaining time until a given end time.

    Args:
        end_time (datetime): The end time
        log_errors (bool): Whether to log errors

    Returns:
        float: Remaining time in seconds, or 0 if error or negative
    """
    if not end_time:
        if log_errors:
            logging.warning("Cannot calculate remaining time: No end time provided")
        return 0

    try:
        current_time = get_eastern_time()
        remaining = get_time_diff_seconds(end_time, current_time)
        return max(0, remaining)  # Never return negative time
    except Exception as e:
        if log_errors:
            logging.error(f"Error calculating remaining time: {e}")
        return 0

def format_time_remaining(seconds):
    """
    Format time remaining in a human-readable format.

    Args:
        seconds (float): Time in seconds

    Returns:
        str: Formatted time string (e.g., "5m 30s")
    """
    if seconds < 0:
        seconds = 0

    minutes = int(seconds // 60)
    seconds = int(seconds % 60)

    if minutes > 0:
        return f"{minutes}m {seconds}s"
    else:
        return f"{seconds}s"

def validate_time_calculation(elapsed_time, max_time=3600):
    """
    Validate that a time calculation is reasonable.

    Args:
        elapsed_time (float): The calculated elapsed time
        max_time (float): Maximum reasonable time in seconds

    Returns:
        bool: True if time is valid, False otherwise
    """
    # Check for negative time (clock went backwards)
    if elapsed_time < 0:
        logging.error(f"TIMING ERROR: Negative elapsed time: {elapsed_time}s")
        return False

    # Check for unreasonably large time
    if elapsed_time > max_time:
        logging.error(f"TIMING ERROR: Unreasonably large elapsed time: {elapsed_time}s")
        return False

    return True

def calculate_duration(minutes):
    """
    Calculate a duration in seconds from minutes.

    Args:
        minutes (int): Duration in minutes

    Returns:
        int: Duration in seconds
    """
    return int(minutes * 60)
