"""
After-open mode utilities for the 7-minute scanner.

This module provides utilities for managing the after-open mode in the 7-minute scanner,
which simulates the scanner running immediately after market open.
"""

import logging
from datetime import datetime, timedelta

from orbscanner.utils.time.timezone import (
    get_eastern_time,
    convert_to_eastern,
    get_time_diff_seconds,
    format_timestamp
)

# Import shared timing utilities
from orbscanner.utils.time.timing import (
    calculate_elapsed_time,
    calculate_remaining_time,
    format_time_remaining,
    validate_time_calculation
)

class AfterOpenTimer:
    """
    Timer for after-open mode in the 7-minute scanner.

    This class manages timing in after-open mode, which simulates the scanner
    running immediately after market open.
    """

    def __init__(self, opening_range_minutes):
        """
        Initialize the after-open timer.

        Args:
            opening_range_minutes (int): Number of minutes for the opening range
        """
        # Always use Eastern timezone for consistency
        self.start_time = get_eastern_time()
        self.scanning_duration = timedelta(minutes=opening_range_minutes)

        # Calculate the simulated market open time (7 minutes before start)
        self.simulated_market_open_time = self.start_time - timedelta(minutes=7)

        # Log the initialization with timezone information
        logging.info(f"AFTER-OPEN MODE: Initialized at {format_timestamp(self.start_time)}")
        logging.info(f"AFTER-OPEN MODE: Using simulated market open time: {format_timestamp(self.simulated_market_open_time)}")
        logging.info(f"AFTER-OPEN MODE: Scanning phase will run for exactly {opening_range_minutes} minutes")

    def get_elapsed_time(self):
        """
        Get elapsed time in seconds since after-open mode started.

        Returns:
            float: Elapsed time in seconds
        """
        # Use shared timing utility
        return calculate_elapsed_time(self.start_time)

    def get_remaining_time(self):
        """
        Get remaining time in seconds for the scanning phase.

        Returns:
            float: Remaining time in seconds
        """
        elapsed = self.get_elapsed_time()
        total_seconds = self.scanning_duration.total_seconds()
        remaining = max(0, total_seconds - elapsed)

        # Validate the result
        if not validate_time_calculation(remaining):
            logging.warning(f"Invalid remaining time in after-open mode: {remaining}s")
            return 0

        return remaining

    def is_scanning_complete(self):
        """
        Check if the scanning phase is complete.

        Returns:
            bool: True if scanning phase is complete, False otherwise
        """
        return self.get_remaining_time() <= 0

    def get_simulated_current_time(self):
        """
        Get the simulated current time relative to market open.

        This method returns a time that represents the current time as if
        the market had just opened at the simulated market open time.

        Returns:
            datetime: Simulated current time
        """
        elapsed = self.get_elapsed_time()
        return self.simulated_market_open_time + timedelta(seconds=elapsed)

    def get_formatted_status(self):
        """
        Get a formatted status string for logging.

        Returns:
            str: Formatted status string
        """
        elapsed = self.get_elapsed_time()
        remaining = self.get_remaining_time()

        # Use shared formatting utility
        elapsed_str = format_time_remaining(elapsed)
        remaining_str = format_time_remaining(remaining)

        return (f"AFTER-OPEN MODE: {elapsed_str} elapsed, "
                f"{remaining_str} remaining until completion")

    def validate_timing(self):
        """
        Validate that timing calculations are producing reasonable results.

        Returns:
            bool: True if timing is valid, False otherwise
        """
        elapsed = self.get_elapsed_time()

        # Use shared validation utility for basic checks
        if not validate_time_calculation(elapsed, max_time=3600):
            return False

        # Additional check specific to after-open mode: simulated time
        try:
            simulated_time = self.get_simulated_current_time()
            now = get_eastern_time()

            # Simulated time should be within a day of current time
            diff = abs(get_time_diff_seconds(simulated_time, now))
            if diff > 86400:  # 24 hours in seconds
                logging.error(f"TIMING ERROR: Simulated time differs from current time by {diff/3600:.1f} hours")
                return False
        except Exception as e:
            logging.error(f"Error validating simulated time: {e}")
            return False

        return True
