"""
Logging utilities for the ORB Scanner.

This module provides centralized logging setup and management for the ORB Scanner,
ensuring consistent logging across all components.
"""

import logging
import sys
import os
from datetime import datetime

from orbscanner.utils.phase.logging import (
    setup_phase_logger,
    PHASE_LOGGERS
)
from orbscanner.utils.phase.manager import PhaseManager

# --- Wrapper Logging Configuration ---
# These will be set by the main scanner.py file
WRAPPER_DEBUG = True  # whether to enable wrapper logging for method extraction verification
# Default path in logs/live directory
WRAPPER_LOG_FILE = os.path.join("logs", "live", "scanner_wrapper.log")  # file to log wrapper method calls to

def setup_logging(log_base_path, level=logging.INFO, enable_data_tracing=True):
    """
    Set up logging for the ORB Scanner with phase-specific and diagnostic loggers.

    Args:
        log_base_path (str): Base path for log files
        level (int): Logging level (default: logging.INFO)
        enable_data_tracing (bool): Whether to enable data request tracing (default: True)

    Returns:
        None
    """
    # Ensure log directory exists
    log_dir = os.path.dirname(log_base_path)
    if log_dir and not os.path.exists(log_dir):
        try:
            os.makedirs(log_dir, exist_ok=True)
            print(f"Created log directory: {log_dir}")
        except Exception as e:
            print(f"Error creating log directory {log_dir}: {e}")

    log_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    root_logger = logging.getLogger()
    root_logger.setLevel(level)

    # Remove any existing handlers to avoid duplicates
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # File handler for main log
    try:
        file_handler = logging.FileHandler(f"{log_base_path}.log", mode='a', encoding='utf-8', delay=False)
        file_handler.setFormatter(log_formatter)
        root_logger.addHandler(file_handler)
        print(f"Added file handler for main log: {log_base_path}.log")
    except Exception as e:
        print(f"Error setting up main log file handler: {e}")
        # Try to create a fallback log in /tmp
        try:
            fallback_log = f"/tmp/scanner_fallback_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
            file_handler = logging.FileHandler(fallback_log, mode='a', encoding='utf-8')
            file_handler.setFormatter(log_formatter)
            root_logger.addHandler(file_handler)
            print(f"Using fallback log file: {fallback_log}")
        except Exception as e2:
            print(f"Error setting up fallback log file handler: {e2}")

    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(log_formatter)
    root_logger.addHandler(console_handler)
    print("Added console handler")

    # Set up phase-specific loggers (only once - setup_phase_logger now handles duplicates)
    print("Setting up phase-specific loggers...")
    # Clear existing phase loggers first to ensure clean initialization
    if 'PHASE_LOGGERS' in globals():
        globals()['PHASE_LOGGERS'].clear()
        
    setup_phase_logger(PhaseManager.PRE_MARKET, f"{log_base_path}_pre_market.log")
    setup_phase_logger(PhaseManager.ORB, f"{log_base_path}_orb.log")
    setup_phase_logger(PhaseManager.FINALIZING, f"{log_base_path}_finalizing.log")
    setup_phase_logger(PhaseManager.MONITORING, f"{log_base_path}_monitoring.log")

    # Set up diagnostic loggers
    try:
        from orbscanner.utils.data.range_logger import setup_range_debug_logger
        from orbscanner.utils.data.volume_logger import setup_volume_debug_logger
        from orbscanner.utils.data.priority_logger import setup_priority_debug_logger

        setup_range_debug_logger(log_base_path)
        setup_volume_debug_logger(log_base_path)
        setup_priority_debug_logger(log_base_path)

        logging.info("Diagnostic loggers initialized successfully")
    except ImportError as e:
        logging.warning(f"Could not initialize diagnostic loggers: {e}")

    # Initialize data request tracing
    try:
        from orbscanner.utils.data.tracing_integration import initialize_tracing

        tracer = initialize_tracing(enabled=enable_data_tracing)
        if tracer:
            logging.info(f"Data request tracing initialized and {'ENABLED' if enable_data_tracing else 'DISABLED'}")
    except ImportError as e:
        logging.warning(f"Could not initialize data request tracing: {e}")

    # Log initialization information
    logging.info(f"Main log: {log_base_path}.log")
    logging.info(f"Pre-market phase log: {log_base_path}_pre_market.log")
    logging.info(f"Opening Range Building phase log: {log_base_path}_orb.log")
    logging.info(f"Finalizing phase log: {log_base_path}_finalizing.log")
    logging.info(f"Monitoring phase log: {log_base_path}_monitoring.log")
    logging.info(f"Range debug log: {log_base_path}_range_debug.log")
    logging.info(f"Volume debug log: {log_base_path}_volume_debug.log")
    logging.info(f"Priority debug log: {log_base_path}_priority_debug.log")
    if enable_data_tracing:
        logging.info(f"Data request trace log: logs/live/data_request_trace_*.log")

def log_wrapper_call(method_name, *args, **kwargs):
    """
    Log a method call to the wrapper log file for extraction verification.

    Args:
        method_name (str): Name of the method being called
        *args: Positional arguments passed to the method
        **kwargs: Keyword arguments passed to the method
    """
    if not WRAPPER_DEBUG:
        return

    try:
        # Format the arguments for logging
        args_str = ", ".join([str(arg) for arg in args])
        kwargs_str = ", ".join([f"{k}={v}" for k, v in kwargs.items()])
        params = f"{args_str}{', ' if args_str and kwargs_str else ''}{kwargs_str}"

        # Create the log message
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        log_message = f"{timestamp} - WRAPPER - {method_name}({params})"

        # Write to the wrapper log file
        with open(WRAPPER_LOG_FILE, 'a') as f:
            f.write(f"{log_message}\n")
            f.flush()  # Ensure it's written immediately
    except Exception as e:
        # Log any errors to the main log
        logging.error(f"Error logging wrapper call: {e}")
        # Don't let wrapper logging errors affect the main program

def set_wrapper_debug(enabled):
    """
    Enable or disable wrapper logging.

    Args:
        enabled (bool): Whether to enable wrapper logging
    """
    global WRAPPER_DEBUG
    WRAPPER_DEBUG = enabled
    logging.info(f"Wrapper logging {'enabled' if enabled else 'disabled'}")

def set_wrapper_log_file(log_file):
    """
    Set the wrapper log file path.

    Args:
        log_file (str): Path to the wrapper log file
    """
    global WRAPPER_LOG_FILE
    WRAPPER_LOG_FILE = log_file
    logging.info(f"Wrapper log file set to: {log_file}")
