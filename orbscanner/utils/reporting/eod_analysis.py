#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
End-of-Day Trade Analysis for 7-Minute Scanner

This module analyzes trades made during the monitoring phase,
determines optimal exit points, and generates a comprehensive report.
"""

import os
import logging
import pandas as pd
from datetime import datetime
import pytz

# Import email functionality
try:
    # Try SendGrid email first (preferred)
    from sendgrid_email import email_report as send_email
    logging.info("Using SendGrid for email delivery")
except ImportError:
    logging.warning("Could not import sendgrid_email module. Trying standard email module...")
    try:
        # Fall back to standard email if SendGrid is not available
        from email_report import email_report as send_email
        logging.info("Using standard SMTP for email delivery")
    except ImportError:
        logging.warning("Could not import any email module. Email functionality will be disabled.")
        send_email = None

# Configure logger
logger = logging.getLogger(__name__)

def load_trade_data(log_base):
    """
    Load trade data from CSV files

    Args:
        log_base (str): Base path for log files

    Returns:
        tuple: (trades_df, price_updates_df)
    """
    trades_csv = f"{log_base}_trades.csv"
    price_updates_csv = f"{log_base}_price_updates.csv"

    trades_df = None
    price_updates_df = None

    # Check if files exist with the given log_base
    if os.path.exists(trades_csv) and os.path.exists(price_updates_csv):
        try:
            trades_df = pd.read_csv(trades_csv)
            price_updates_df = pd.read_csv(price_updates_csv)
            logger.info(f"Loaded {len(trades_df)} trades from {trades_csv}")
            logger.info(f"Loaded {len(price_updates_df)} price updates from {price_updates_csv}")
            return trades_df, price_updates_df
        except Exception as e:
            logger.error(f"Error loading CSV files with direct path: {e}")

    # If files not found with direct path, search for files with similar base name but different timestamp
    log_dir = os.path.dirname(log_base)
    base_name = os.path.basename(log_base)
    date_part = base_name.split('_')[2]  # Extract date part (YYYYMMDD)

    # Find all trade files in the log directory with the same date
    trade_files = [f for f in os.listdir(log_dir) if f.endswith('_trades.csv') and date_part in f]

    if trade_files:
        # Sort by timestamp (newest first)
        trade_files.sort(reverse=True)
        latest_trade_file = os.path.join(log_dir, trade_files[0])
        latest_base = latest_trade_file.replace('_trades.csv', '')
        latest_price_file = f"{latest_base}_price_updates.csv"

        logger.info(f"Found alternative trade files with base: {latest_base}")

        # Load the files
        if os.path.exists(latest_trade_file) and os.path.exists(latest_price_file):
            try:
                trades_df = pd.read_csv(latest_trade_file)
                price_updates_df = pd.read_csv(latest_price_file)
                logger.info(f"Loaded {len(trades_df)} trades from {latest_trade_file}")
                logger.info(f"Loaded {len(price_updates_df)} price updates from {latest_price_file}")
            except Exception as e:
                logger.error(f"Error loading alternative CSV files: {e}")
        else:
            logger.warning(f"Alternative trade files not found or incomplete: {latest_base}")
    else:
        logger.warning(f"No trade files found in {log_dir} with date {date_part}")

    return trades_df, price_updates_df

def find_optimal_exits(trades_df, price_updates_df):
    """
    Find optimal exit points for each trade

    Args:
        trades_df (DataFrame): Trades data
        price_updates_df (DataFrame): Price updates data

    Returns:
        dict: Optimal exit information for each trade
    """
    if trades_df is None or price_updates_df is None:
        return {}

    optimal_exits = {}

    for _, trade in trades_df.iterrows():
        trade_id = trade['trade_id']
        symbol = trade['symbol']
        direction = trade['direction']
        entry_price = trade['entry_price']

        # Get price updates for this trade
        trade_updates = price_updates_df[price_updates_df['trade_id'] == trade_id]

        if len(trade_updates) == 0:
            logger.warning(f"No price updates found for trade {trade_id}")
            continue

        # Calculate profit for each price update
        if direction.upper() == 'LONG':
            trade_updates['profit_pct'] = (trade_updates['price'] / entry_price - 1) * 100
        else:  # SHORT
            trade_updates['profit_pct'] = (entry_price / trade_updates['price'] - 1) * 100

        # Find the optimal exit (maximum profit)
        best_update_idx = trade_updates['profit_pct'].idxmax()
        best_update = trade_updates.loc[best_update_idx]

        optimal_exits[trade_id] = {
            'symbol': symbol,
            'direction': direction,
            'entry_price': entry_price,
            'optimal_exit_price': best_update['price'],
            'optimal_exit_time': best_update['timestamp'],
            'optimal_profit_pct': best_update['profit_pct'],
            'time_to_optimal': best_update['time_in_trade'],
            'actual_exit_price': trade.get('exit_price'),
            'actual_exit_time': trade.get('exit_time'),
            'actual_profit_pct': trade.get('pnl_percent')
        }

    logger.info(f"Found optimal exits for {len(optimal_exits)} trades")
    return optimal_exits

def calculate_performance_metrics(trades_df, price_updates_df, optimal_exits):
    """
    Calculate performance metrics

    Args:
        trades_df (DataFrame): Trades data
        price_updates_df (DataFrame): Price updates data (used for opportunity cost calculation)
        optimal_exits (dict): Optimal exit information

    Returns:
        dict: Performance metrics
    """
    # Note: price_updates_df is no longer used directly since we're assuming optimal exits for open trades
    # but we keep the parameter for API compatibility
    if trades_df is None or len(optimal_exits) == 0:
        return {}

    metrics = {
        'total_trades': len(trades_df),
        'long_trades': len(trades_df[trades_df['direction'].str.upper() == 'LONG']),
        'short_trades': len(trades_df[trades_df['direction'].str.upper() == 'SHORT']),
    }

    # Check if status column exists
    if 'status' in trades_df.columns:
        metrics['closed_trades'] = len(trades_df[trades_df['status'].str.upper() == 'CLOSED'])
        metrics['open_trades'] = len(trades_df[trades_df['status'].str.upper() == 'OPEN'])

        # Calculate metrics for closed trades
        closed_trades = trades_df[trades_df['status'].str.upper() == 'CLOSED']
        open_trades = trades_df[trades_df['status'].str.upper() == 'OPEN']

        # If we have closed trades, calculate metrics based on them
        if len(closed_trades) > 0 and 'pnl_percent' in closed_trades.columns:
            metrics['win_rate'] = len(closed_trades[closed_trades['pnl_percent'] > 0]) / len(closed_trades) * 100
            metrics['avg_profit_pct'] = closed_trades['pnl_percent'].mean()
            metrics['max_profit_pct'] = closed_trades['pnl_percent'].max()
            metrics['max_loss_pct'] = closed_trades['pnl_percent'].min()

            # Calculate profit factor
            winning_trades = closed_trades[closed_trades['pnl_percent'] > 0]
            losing_trades = closed_trades[closed_trades['pnl_percent'] <= 0]
        # If we only have open trades, calculate metrics based on optimal exits
        elif len(open_trades) > 0 and len(optimal_exits) > 0:
            # Calculate metrics based on optimal exits for open trades
            optimal_profits = []
            winning_count = 0

            for _, trade in open_trades.iterrows():
                trade_id = trade['trade_id']
                if trade_id in optimal_exits:
                    optimal_profit = optimal_exits[trade_id]['optimal_profit_pct']
                    optimal_profits.append(optimal_profit)
                    if optimal_profit > 0:
                        winning_count += 1

            if optimal_profits:
                metrics['win_rate'] = (winning_count / len(optimal_profits)) * 100
                metrics['avg_profit_pct'] = sum(optimal_profits) / len(optimal_profits)
                metrics['max_profit_pct'] = max(optimal_profits) if optimal_profits else 0
                metrics['max_loss_pct'] = min(optimal_profits) if optimal_profits else 0

                # Note that these are based on optimal exits
                metrics['based_on_optimal_exits'] = True

            if len(winning_trades) > 0 and len(losing_trades) > 0:
                total_gains = winning_trades['pnl_percent'].sum()
                total_losses = abs(losing_trades['pnl_percent'].sum())

                if total_losses > 0:
                    metrics['profit_factor'] = total_gains / total_losses
                else:
                    metrics['profit_factor'] = float('inf')  # No losses
            else:
                metrics['profit_factor'] = 0 if len(winning_trades) == 0 else float('inf')

    # Calculate optimal metrics
    if len(optimal_exits) > 0:
        optimal_profits = [exit_info['optimal_profit_pct'] for exit_info in optimal_exits.values()]
        metrics['avg_optimal_profit_pct'] = sum(optimal_profits) / len(optimal_profits)
        metrics['max_optimal_profit_pct'] = max(optimal_profits)

        # Calculate opportunity cost (difference between optimal and actual/current)
        opportunity_costs = []
        for trade_id, exit_info in optimal_exits.items():
            if exit_info['actual_profit_pct'] is not None:
                # For closed trades, use actual profit
                opportunity_cost = exit_info['optimal_profit_pct'] - exit_info['actual_profit_pct']
                opportunity_costs.append(opportunity_cost)
            else:
                # For open trades, assume optimal exit was taken (opportunity cost is 0)
                # This is per the user's requirement to assume trades exited at optimal points
                opportunity_cost = 0
                opportunity_costs.append(opportunity_cost)

                # Store the optimal profit as the current profit for reference
                exit_info['current_profit_pct'] = exit_info['optimal_profit_pct']

        if opportunity_costs:
            metrics['avg_opportunity_cost'] = sum(opportunity_costs) / len(opportunity_costs)
            metrics['max_opportunity_cost'] = max(opportunity_costs)

    logger.info(f"Calculated performance metrics: {metrics}")
    return metrics

def generate_eod_report(log_base, trades_df, price_updates_df, optimal_exits, metrics):
    """
    Generate end-of-day trade analysis report

    Args:
        log_base (str): Base path for log files
        trades_df (DataFrame): Trades data
        price_updates_df (DataFrame): Price updates data (kept for API compatibility)
        optimal_exits (dict): Optimal exit information
        metrics (dict): Performance metrics

    Returns:
        str: Path to the generated report
    """
    # Note: price_updates_df is no longer used directly but we keep the parameter for API compatibility
    logger.info("Generating end-of-day trade analysis report...")

    # Determine the correct log base to use for the report
    # If we have trade data, use the same base as the trade files
    if trades_df is not None and not trades_df.empty and 'trade_id' in trades_df.columns:
        # Extract the base from the first trade_id
        first_trade = trades_df.iloc[0]
        if 'trade_id' in first_trade:
            trade_id = first_trade['trade_id']
            if '_' in trade_id:
                symbol = trade_id.split('_')[0]
                # Find the trade file that contains this trade
                log_dir = os.path.dirname(log_base)
                trade_files = [f for f in os.listdir(log_dir) if f.endswith('_trades.csv')]
                for file in trade_files:
                    file_path = os.path.join(log_dir, file)
                    try:
                        with open(file_path, 'r') as f:
                            if trade_id in f.read():
                                # Found the file containing this trade
                                correct_base = file_path.replace('_trades.csv', '')
                                logger.info(f"Using log base from trade file: {correct_base}")
                                log_base = correct_base
                                break
                    except Exception:
                        continue

    # Create report path
    report_path = f"{log_base}_eod_report.md"

    # Get current time in Eastern timezone
    eastern_tz = pytz.timezone('US/Eastern')
    current_time = datetime.now(eastern_tz)

    # Generate the report
    with open(report_path, 'w') as f:
        # Report header
        f.write(f"# 7-Minute Scanner End-of-Day Trade Analysis\n\n")
        f.write(f"**Date:** {current_time.strftime('%Y-%m-%d')}\n")
        f.write(f"**Time:** {current_time.strftime('%H:%M:%S %Z')}\n")

        # Add prominent notice if no trades occurred
        if trades_df is None or trades_df.empty:
            f.write("\n**⚠️ NO TRADES EXECUTED TODAY ⚠️**\n\n")

        f.write("\n## Executive Summary\n\n")

        # Get key metrics for the summary
        total_trades = metrics.get('total_trades', 0)
        closed_trades = metrics.get('closed_trades', 0)
        open_trades = metrics.get('open_trades', 0)
        win_rate = metrics.get('win_rate', 0)
        avg_profit = metrics.get('avg_profit_pct', 0)
        profit_factor = metrics.get('profit_factor', 0)

        # Create a highlighted summary
        f.write(f"**Today's Trading Performance:**\n\n")

        if total_trades == 0:
            f.write("- **No trades were executed today**\n")
            f.write("- **Possible reasons:** No valid breakouts detected, market conditions unfavorable, or technical issues\n")
        else:
            # Highlight key metrics with emoji indicators
            win_emoji = "🟢" if win_rate >= 50 else "🔴"
            profit_emoji = "🟢" if avg_profit > 0 else "🔴"

            f.write(f"- **Total Trades:** {total_trades} ({closed_trades} closed, {open_trades} open)\n")
            f.write(f"- **Win Rate:** {win_emoji} {win_rate:.1f}%\n")
            f.write(f"- **Average Profit:** {profit_emoji} {avg_profit:.2f}%\n")

            # Add note about optimal exits for open trades if applicable
            if open_trades > 0 and 'based_on_optimal_exits' in metrics:
                f.write(f"- **Note:** For open trades, metrics assume optimal exits were taken\n")

        if profit_factor > 0:
            f.write(f"- **Profit Factor:** {profit_factor:.2f}\n")

        # Add optimal exit information if available
        if 'avg_optimal_profit_pct' in metrics:
            optimal_profit = metrics['avg_optimal_profit_pct']
            opportunity_cost = metrics.get('avg_opportunity_cost', 0)
            f.write(f"- **Average Optimal Profit:** {optimal_profit:.2f}% (Opportunity Cost: {opportunity_cost:.2f}%)\n")

        # Add market context if available
        try:
            # Get VIX value from trades data if available
            vix_value = None
            if trades_df is not None and 'vix' in trades_df.columns:
                vix_values = trades_df['vix'].dropna().unique()
                if len(vix_values) > 0:
                    vix_value = vix_values[0]

            if vix_value is not None:
                # Determine market condition based on VIX level
                if vix_value < 15:
                    market_condition = "Low volatility"
                elif vix_value < 25:
                    market_condition = "Normal volatility"
                elif vix_value < 35:
                    market_condition = "High volatility"
                else:
                    market_condition = "Extreme volatility"

                market_context = f"VIX: {vix_value:.2f} ({market_condition})"
            else:
                market_context = "Market context not available (VIX data missing)"

            f.write(f"\n**Market Context:** {market_context}\n")
        except Exception as e:
            logger.warning(f"Error getting market context: {e}")
            f.write(f"\n**Market Context:** Market context not available\n")

        f.write("\n")

        # Performance summary
        f.write("## Performance Summary\n\n")

        if metrics:
            # Create a more visually appealing summary with sections

            # Trade Statistics Section
            f.write("### Trade Statistics\n\n")
            f.write("| Metric | Value | | Metric | Value |\n")
            f.write("|--------|------:|-|--------|------:|\n")

            # First row: Trade counts
            total = metrics.get('total_trades', 0)
            long = metrics.get('long_trades', 0)
            short = metrics.get('short_trades', 0)
            closed = metrics.get('closed_trades', 0)
            open_trades = metrics.get('open_trades', 0)

            f.write(f"| **Total Trades** | **{total}** | | Long Trades | {long} |\n")
            f.write(f"| Closed Trades | {closed} | | Short Trades | {short} |\n")
            f.write(f"| Open Trades | {open_trades} | | | |\n\n")

            # Profitability Section - Highlighted
            f.write("### Profitability Metrics\n\n")
            f.write("| Metric | Value | | Metric | Value |\n")
            f.write("|--------|------:|-|--------|------:|\n")

            win_rate = metrics.get('win_rate', 0)
            avg_profit = metrics.get('avg_profit_pct', 0)
            max_profit = metrics.get('max_profit_pct', 0)
            max_loss = metrics.get('max_loss_pct', 0)
            profit_factor = metrics.get('profit_factor', 0)
            avg_win = metrics.get('avg_win_pct', 0)
            avg_loss = metrics.get('avg_loss_pct', 0)

            # Add emoji indicators for key metrics
            win_indicator = "🟢" if win_rate >= 50 else "🔴"
            profit_indicator = "🟢" if avg_profit > 0 else "🔴"

            # Handle infinite profit factor
            if profit_factor == float('inf'):
                pf_indicator = "🟢"
                pf_display = "∞ (No Losses)"
            else:
                pf_indicator = "🟢" if profit_factor > 1 else "🔴"
                pf_display = f"{profit_factor:.2f}"

            f.write(f"| **Win Rate** | **{win_indicator} {win_rate:.2f}%** | | **Profit Factor** | **{pf_indicator} {pf_display}** |\n")
            f.write(f"| **Average Profit** | **{profit_indicator} {avg_profit:.2f}%** | | Average Win | {avg_win:.2f}% |\n")
            f.write(f"| Maximum Profit | {max_profit:.2f}% | | Average Loss | {avg_loss:.2f}% |\n")
            f.write(f"| Maximum Loss | {max_loss:.2f}% | | | |\n\n")

            # Optimal Exit Analysis Section
            if 'avg_optimal_profit_pct' in metrics:
                f.write("### Optimal Exit Analysis\n\n")
                f.write("| Metric | Value | | Metric | Value |\n")
                f.write("|--------|------:|-|--------|------:|\n")

                avg_optimal = metrics.get('avg_optimal_profit_pct', 0)
                max_optimal = metrics.get('max_optimal_profit_pct', 0)
                avg_opp_cost = metrics.get('avg_opportunity_cost', 0)
                max_opp_cost = metrics.get('max_opportunity_cost', 0)

                # Highlight opportunity cost with emoji
                opp_indicator = "🟢" if avg_opp_cost < 1 else ("🟡" if avg_opp_cost < 2 else "🔴")

                f.write(f"| **Average Optimal Profit** | **{avg_optimal:.2f}%** | | **Average Opportunity Cost** | **{opp_indicator} {avg_opp_cost:.2f}%** |\n")
                f.write(f"| Maximum Optimal Profit | {max_optimal:.2f}% | | Maximum Opportunity Cost | {max_opp_cost:.2f}% |\n\n")

                # Add interpretation
                if avg_opp_cost > 2:
                    f.write("> **Note:** High opportunity cost indicates exit timing could be improved significantly.\n\n")
                elif avg_opp_cost > 1:
                    f.write("> **Note:** Moderate opportunity cost suggests some room for exit timing improvement.\n\n")
                else:
                    f.write("> **Note:** Low opportunity cost indicates good exit timing overall.\n\n")
        else:
            f.write("*No performance metrics available*\n\n")

        # Optimal exit analysis
        f.write("\n## Optimal Exit Analysis\n\n")

        # Add note about optimal exits for open trades
        if metrics.get('open_trades', 0) > 0:
            f.write("> **Note:** For open trades, this analysis assumes they were exited at the optimal point for maximum profit or minimal loss.\n\n")

        if optimal_exits:
            f.write("| Symbol | Direction | Entry Price | Optimal Exit | Optimal Profit | Time to Optimal | Actual Exit | Actual Profit | Opportunity Cost |\n")
            f.write("|--------|-----------|------------:|-------------:|--------------:|----------------:|-----------:|-------------:|----------------:|\n")

            # Sort by optimal profit (descending)
            sorted_exits = sorted(optimal_exits.values(), key=lambda x: x['optimal_profit_pct'], reverse=True)

            for exit_info in sorted_exits:
                symbol = exit_info['symbol']
                direction = exit_info['direction']
                entry_price = exit_info['entry_price']
                optimal_exit_price = exit_info['optimal_exit_price']
                optimal_profit_pct = exit_info['optimal_profit_pct']
                time_to_optimal = exit_info['time_to_optimal']
                actual_exit_price = exit_info.get('actual_exit_price', 'Open')
                actual_profit_pct = exit_info.get('actual_profit_pct', 'N/A')

                # Calculate opportunity cost
                if actual_profit_pct != 'N/A':
                    # For closed trades, use actual profit
                    opportunity_cost = optimal_profit_pct - actual_profit_pct
                    opportunity_cost_str = f"{opportunity_cost:.2f}%"
                else:
                    # For open trades, assume optimal exit was taken (opportunity cost is 0)
                    opportunity_cost_str = "0.00%"

                # For open trades, show what the profit would have been if exited at optimal point
                if actual_profit_pct == 'N/A':
                    actual_profit_display = f"(Optimal: {optimal_profit_pct:.2f}%)"
                else:
                    actual_profit_display = f"{actual_profit_pct:.2f}%"

                f.write(f"| {symbol} | {direction} | ${entry_price:.2f} | ${optimal_exit_price:.2f} | {optimal_profit_pct:.2f}% | {time_to_optimal} | {actual_exit_price if actual_exit_price != 'Open' else 'Open'} | {actual_profit_display} | {opportunity_cost_str} |\n")
        else:
            f.write("*No optimal exit data available*\n\n")

        # Breakout Detection Formula Section
        f.write("\n## Breakout Detection Formula\n\n")
        f.write("The following formula was used to detect breakouts and trigger trades:\n\n")

        # Get VIX level from trades if available
        vix_level = None
        if trades_df is not None and not trades_df.empty and 'vix' in trades_df.columns:
            # Use the first trade's VIX value
            vix_level = trades_df['vix'].iloc[0] if not trades_df['vix'].isna().all() else None

        # VIX-based breakout parameters
        volume_threshold = 1.5  # Default
        breakout_buffer = 0.1   # Default
        min_range_width = 0.5   # Default
        max_range_width = 3.0   # Default

        if vix_level is not None:
            if vix_level > 35:
                volume_threshold = 4.0
                breakout_buffer = 0.2
                min_range_width = 0.8
                max_range_width = 4.0
                f.write("**High Volatility Parameters (VIX > 35):**\n")
            elif vix_level > 25:
                volume_threshold = 2.5
                breakout_buffer = 0.15
                min_range_width = 0.6
                max_range_width = 3.5
                f.write("**Elevated Volatility Parameters (VIX > 25):**\n")
            else:
                f.write("**Standard Parameters (VIX ≤ 25):**\n")
        else:
            f.write("**Standard Parameters (VIX level unknown):**\n")

        f.write(f"- Volume Threshold: {volume_threshold}x (current volume must exceed baseline by this factor)\n")
        f.write(f"- Breakout Buffer: {breakout_buffer}% (price must exceed range by this percentage)\n")
        f.write(f"- Range Width Requirements: {min_range_width}% to {max_range_width}%\n\n")

        f.write("### LONG Breakout Formula\n\n")
        f.write("For a LONG breakout to trigger, ALL of the following conditions must be met:\n\n")
        f.write("* **Symbol Type:** The symbol must be classified as a LONG candidate\n")
        f.write(f"* **Price Breakout:** Current price must exceed the opening range high by at least {breakout_buffer}%\n")
        f.write(f"  * Example: If opening high is $100.00, price must exceed ${100 * (1 + breakout_buffer/100):.2f}\n")
        f.write(f"* **Volume Confirmation:** Current volume must be at least {volume_threshold}x the baseline volume\n")
        f.write(f"  * Baseline is the greater of opening range volume or average daily volume\n")
        f.write(f"* **Range Width Validation:** Opening range width must be between {min_range_width}% and {max_range_width}%\n")
        f.write(f"  * Example: For a $100 stock, range width should be ${min_range_width:.2f} to ${max_range_width:.2f}\n\n")

        f.write("### SHORT Breakout Formula\n\n")
        f.write("For a SHORT breakout to trigger, ALL of the following conditions must be met:\n\n")
        f.write("* **Symbol Type:** The symbol must be classified as a SHORT candidate\n")
        f.write(f"* **Price Breakout:** Current price must fall below the opening range low by at least {breakout_buffer}%\n")
        f.write(f"  * Example: If opening low is $100.00, price must fall below ${100 * (1 - breakout_buffer/100):.2f}\n")
        f.write(f"* **Volume Confirmation:** Current volume must be at least {volume_threshold}x the baseline volume\n")
        f.write(f"  * Baseline is the greater of opening range volume or average daily volume\n")
        f.write(f"* **Range Width Validation:** Opening range width must be between {min_range_width}% and {max_range_width}%\n")
        f.write(f"  * Example: For a $100 stock, range width should be ${min_range_width:.2f} to ${max_range_width:.2f}\n\n")

        # Trade details
        f.write("\n## Trade Details\n\n")

        if trades_df is not None and not trades_df.empty:
            # Enhanced trade details with breakout information
            f.write("| Symbol | Direction | Entry Time | Entry Price | Opening Range | Range Width | Volume | Status | Exit Time | Exit Price | P&L |\n")
            f.write("|--------|-----------|------------|------------:|--------------|------------:|-------:|--------|-----------|------------|----:|\n")

            # Sort by entry time
            if 'entry_time' in trades_df.columns:
                sorted_trades = trades_df.sort_values('entry_time')
            else:
                sorted_trades = trades_df

            for _, trade in sorted_trades.iterrows():
                symbol = trade['symbol']
                direction = trade['direction']
                entry_time = trade.get('entry_time', 'N/A')
                entry_price = trade['entry_price']
                status = trade.get('status', 'N/A')
                exit_time = trade.get('exit_time', 'N/A')
                exit_price = trade.get('exit_price', 'N/A')
                pnl = trade.get('pnl_percent', 'N/A')

                # Get opening range data if available
                opening_high = trade.get('opening_high', 'N/A')
                opening_low = trade.get('opening_low', 'N/A')
                # Format as low-high to be consistent with monitoring log format
                opening_range = f"${opening_low:.2f}-${opening_high:.2f}" if opening_high != 'N/A' and opening_low != 'N/A' else "N/A"

                # Calculate range width if possible
                if opening_high != 'N/A' and opening_low != 'N/A' and opening_low > 0:
                    range_width = ((opening_high - opening_low) / opening_low) * 100
                    range_width_str = f"{range_width:.2f}%"
                else:
                    range_width_str = "N/A"

                # Get volume if available
                volume = trade.get('volume', 'N/A')
                volume_str = f"{volume:,}" if volume != 'N/A' else "N/A"

                f.write(f"| {symbol} | {direction} | {entry_time} | ${entry_price:.2f} | {opening_range} | {range_width_str} | {volume_str} | {status} | {exit_time} | {exit_price if exit_price != 'N/A' else 'N/A'} | {pnl if pnl != 'N/A' else 'N/A'} |\n")
        else:
            f.write("*No trade data available*\n\n")

    logger.info(f"End-of-day report generated: {report_path}")
    return report_path

def analyze_eod_trades(log_base, email_report=True):
    """
    Main function to analyze trades and generate end-of-day report

    Args:
        log_base (str): Base path for log files
        email_report (bool): Whether to email the report

    Returns:
        str: Path to the generated report
    """
    logger.info("Starting end-of-day trade analysis...")

    # Load trade data
    trades_df, price_updates_df = load_trade_data(log_base)

    if trades_df is None or trades_df.empty:
        logger.warning("No trade data available for analysis")
        return None

    if price_updates_df is None or price_updates_df.empty:
        logger.warning("No price update data available for analysis")
        return None

    # Find optimal exits for each trade
    optimal_exits = find_optimal_exits(trades_df, price_updates_df)

    # Calculate performance metrics
    metrics = calculate_performance_metrics(trades_df, price_updates_df, optimal_exits)

    # Generate the report
    report_path = generate_eod_report(log_base, trades_df, price_updates_df, optimal_exits, metrics)

    # Email the report if requested
    if email_report and send_email is not None and report_path is not None:
        try:
            # Create a subject with date information
            date_str = datetime.now().strftime('%Y-%m-%d')
            subject = f"7-Minute Scanner End-of-Day Analysis - {date_str}"

            # Create additional message with summary information
            message = f"Summary: Analyzed {metrics.get('total_trades', 0)} trades.\n\n"

            # Add emoji indicators for key metrics
            win_rate = metrics.get('win_rate', 0)
            avg_profit = metrics.get('avg_profit_pct', 0)
            profit_factor = metrics.get('profit_factor', 0)

            win_indicator = "✅" if win_rate >= 50 else "❌"
            profit_indicator = "✅" if avg_profit > 0 else "❌"

            # Handle infinite profit factor
            if profit_factor == float('inf'):
                pf_indicator = "✅"
                pf_display = "∞ (No Losses)"
            else:
                pf_indicator = "✅" if profit_factor > 1 else "❌"
                pf_display = f"{profit_factor:.2f}"

            message += f"Win Rate: {win_indicator} {win_rate:.2f}%\n"
            message += f"Average Profit: {profit_indicator} {avg_profit:.2f}%\n"
            message += f"Profit Factor: {pf_indicator} {pf_display}\n"

            # Add note about optimal exits for open trades if applicable
            if metrics.get('open_trades', 0) > 0 and 'based_on_optimal_exits' in metrics:
                message += f"Note: For open trades, metrics assume optimal exits were taken\n"

            message += "\n"

            # Add optimal exit information if available
            if 'avg_optimal_profit_pct' in metrics and 'avg_opportunity_cost' in metrics:
                avg_optimal = metrics['avg_optimal_profit_pct']
                avg_opp_cost = metrics['avg_opportunity_cost']

                message += f"Average Optimal Profit: {avg_optimal:.2f}%\n"
                message += f"Average Opportunity Cost: {avg_opp_cost:.2f}%\n\n"

                # Add interpretation
                if avg_opp_cost > 2:
                    message += "Note: High opportunity cost indicates exit timing could be improved significantly.\n"
                elif avg_opp_cost > 1:
                    message += "Note: Moderate opportunity cost suggests some room for exit timing improvement.\n"
                else:
                    message += "Note: Low opportunity cost indicates good exit timing overall.\n"

            # Send the email
            email_result = send_email(report_path, subject, message)
            if email_result:
                logger.info(f"End-of-day report emailed successfully")
            else:
                logger.warning(f"Failed to email end-of-day report")
        except Exception as e:
            logger.error(f"Error emailing end-of-day report: {e}")

    logger.info("End-of-day analysis completed")
    return report_path

if __name__ == "__main__":
    # This can be used for testing the module independently
    import argparse

    # Setup logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    parser = argparse.ArgumentParser(description="Generate 7-Minute Scanner End-of-Day Trade Analysis")
    parser.add_argument("--log-base", required=True, help="Base path for log files")
    parser.add_argument("--no-email", action="store_false", dest="email_report",
                        help="Disable emailing the report")
    args = parser.parse_args()

    report_path = analyze_eod_trades(args.log_base, args.email_report)

    if report_path:
        print(f"End-of-day report generated: {report_path}")
    else:
        print("Failed to generate end-of-day report. Check logs for details.")
