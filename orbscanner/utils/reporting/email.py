#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Email Report Module for 7-Minute Scanner

This module provides functionality to email the finalizing report to the user.
"""

import os
import logging
import smtplib
import ssl
import time
import socket
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
import markdown

# Configure logger
logger = logging.getLogger(__name__)

# Email settings
SMTP_SERVER = "smtp.gmail.com"
SMTP_PORT = 465  # Changed from 587 to 465 for SSL
SMTP_USER = "<EMAIL>"
SMTP_PASS = "opfs hbjx hjtk ocvr"
SENDER = "<EMAIL>"
TO = "<EMAIL>"

def email_report(report_path, subject=None, additional_text=None, max_retries=3, retry_delay=5):
    """
    Email the finalizing report to the user.

    Args:
        report_path (str): Path to the Markdown report file
        subject (str, optional): Email subject. Defaults to a generic subject.
        additional_text (str, optional): Additional text to include in the email body.
        max_retries (int, optional): Maximum number of retry attempts. Defaults to 3.
        retry_delay (int, optional): Delay between retries in seconds. Defaults to 5.

    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    if not os.path.exists(report_path):
        logger.error(f"Report file not found: {report_path}")
        return False

    # Prepare email content outside the retry loop
    try:
        # Read the report file
        with open(report_path, 'r') as f:
            md_content = f.read()

        # Convert Markdown to HTML for email body
        html_content = markdown.markdown(md_content, extensions=['tables'])

        # Create email message
        msg = MIMEMultipart('alternative')

        # Set email subject
        if subject is None:
            # Extract date from filename if possible
            filename = os.path.basename(report_path)
            if 'scan_7min_' in filename:
                date_part = filename.split('scan_7min_')[1].split('_')[0]
                subject = f"7-Minute Scanner Report - {date_part}"
            else:
                subject = "7-Minute Scanner Finalizing Report"

        msg['Subject'] = subject
        msg['From'] = SENDER
        msg['To'] = TO

        # Add text part
        text_content = "Please see the attached 7-Minute Scanner report.\n\n"
        if additional_text:
            text_content += additional_text + "\n\n"
        text_content += "Note: This email contains an HTML version of the report for better readability, "
        text_content += "as well as the original Markdown file as an attachment."

        msg.attach(MIMEText(text_content, 'plain'))

        # Add HTML part
        msg.attach(MIMEText(html_content, 'html'))

        # Attach the original Markdown file
        with open(report_path, 'rb') as f:
            attachment = MIMEApplication(f.read(), Name=os.path.basename(report_path))

        attachment['Content-Disposition'] = f'attachment; filename="{os.path.basename(report_path)}"'
        msg.attach(attachment)

    except Exception as e:
        logger.error(f"Error preparing email content: {e}")
        return False

    # Retry loop for sending the email
    for attempt in range(max_retries):
        try:
            logger.info(f"Sending email attempt {attempt + 1}/{max_retries}...")

            # Connect to SMTP server using SSL and send email
            context = ssl.create_default_context()
            with smtplib.SMTP_SSL(SMTP_SERVER, SMTP_PORT, timeout=30, context=context) as server:
                server.login(SMTP_USER, SMTP_PASS)
                server.sendmail(SENDER, TO, msg.as_string())

            logger.info(f"Report email sent successfully to {TO}")
            return True

        except socket.gaierror as e:
            logger.warning(f"Network error (attempt {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                logger.info(f"Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
                # Increase delay for next attempt
                retry_delay *= 2
            else:
                logger.error(f"Failed to send email after {max_retries} attempts: Network unreachable")
                return False

        except smtplib.SMTPException as e:
            logger.warning(f"SMTP error (attempt {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                logger.info(f"Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
                # Increase delay for next attempt
                retry_delay *= 2
            else:
                logger.error(f"Failed to send email after {max_retries} attempts: SMTP error")
                return False

        except Exception as e:
            logger.error(f"Unexpected error sending email: {e}")
            return False

if __name__ == "__main__":
    # This can be used for testing the module independently
    import argparse

    # Setup logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    parser = argparse.ArgumentParser(description="Email 7-Minute Scanner Report")
    parser.add_argument("report_path", help="Path to the report file")
    parser.add_argument("--subject", help="Email subject")
    parser.add_argument("--message", help="Additional message to include in the email")
    parser.add_argument("--retries", type=int, default=3, help="Maximum number of retry attempts")
    parser.add_argument("--retry-delay", type=int, default=5, help="Delay between retries in seconds")

    args = parser.parse_args()

    result = email_report(args.report_path, args.subject, args.message, args.retries, args.retry_delay)
    if result:
        print(f"Email sent successfully to {TO}")
    else:
        print("Failed to send email. Check logs for details.")
