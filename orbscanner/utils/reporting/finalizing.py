#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Finalizing Report Generator for 7-Minute Scanner

This module generates a comprehensive Markdown report at the end of the finalizing phase,
providing detailed information about scanner results, filtering process, and final candidates.
"""

import logging
from datetime import datetime
import pytz

logger = logging.getLogger(__name__)

def generate_finalizing_report(app, log_base, vix_level=None, email_report=True):
    """
    Generate a comprehensive report at the end of the finalizing phase.

    Args:
        app: The scanner application instance
        log_base: Base path for log files
        vix_level: Current VIX level
        email_report: Whether to email the report after generation (default: True)

    Returns:
        str: Path to the generated report
    """
    logger.info("Generating finalizing phase report...")

    # Create report path
    report_path = f"{log_base}_finalizing_report.md"

    # Get current time in Eastern timezone
    eastern_tz = pytz.timezone('US/Eastern')
    current_time = datetime.now(eastern_tz)

    # Extract data from app
    long_candidates = app.longCandidates
    short_candidates = app.shortCandidates
    combined_results = app.combinedResults

    # Get scanner results
    scanner_results = {}
    for scanner_type, results in app.scannerResults.items():
        scanner_results[scanner_type] = len(results)

    # Get filtering statistics
    filtering_stats = getattr(app, 'filtering_stats', {})

    # Get rejected candidates
    rejected_candidates = getattr(app, 'rejected_candidates', {})

    # Get daily trend data
    daily_trend_data = getattr(app, 'daily_trend_data', {})

    # Generate the report
    with open(report_path, 'w') as f:
        # Report header
        f.write(f"# 7-Minute Scanner Finalizing Report\n\n")
        f.write(f"**Date:** {current_time.strftime('%Y-%m-%d')}\n")
        f.write(f"**Time:** {current_time.strftime('%H:%M:%S %Z')}\n")
        # Format VIX level with proper handling for None values
        vix_display = f"{vix_level:.2f}" if vix_level is not None else "N/A"
        f.write(f"**VIX Level:** {vix_display}\n")

        # Add prominent notice if no candidates were found
        long_count = len(long_candidates)
        short_count = len(short_candidates)
        if long_count == 0 and short_count == 0:
            f.write("\n**⚠️ NO TRADING CANDIDATES FOUND ⚠️**\n")

        f.write("\n## Executive Summary\n\n")
        f.write(f"**Selected Candidates:** {long_count} long, {short_count} short\n")

        # Add explanation if no candidates were found
        if long_count == 0 and short_count == 0:
            f.write("\n**No valid trading candidates were identified.** Possible reasons:\n")
            f.write("- No symbols met the minimum criteria (volume, price, range width)\n")
            f.write("- Trend alignment requirements filtered out potential candidates\n")
            f.write("- Market conditions unfavorable for the strategy\n")
            f.write("- Technical issues with data retrieval\n\n")
        f.write("\n")

        # VIX-based strategy note
        if vix_level is not None:
            if vix_level > 30:
                f.write("**Strategy:** High volatility environment (VIX > 30). Using strict trend alignment with strong correlation requirements.\n\n")
            elif vix_level > 25:
                f.write("**Strategy:** Elevated volatility environment (VIX > 25). Using moderate trend alignment.\n\n")
            elif vix_level > 20:
                f.write("**Strategy:** Normal volatility environment (VIX > 20). Using light trend alignment.\n\n")
            else:
                f.write("**Strategy:** Low volatility environment (VIX ≤ 20). No trend alignment filtering applied.\n\n")

        # Top candidates preview
        if long_candidates or short_candidates:
            f.write("### Top Candidates Preview\n\n")
            f.write("| Symbol | Direction | Priority | Range Width | Trend | Correlation |\n")
            f.write("|--------|-----------|----------:|------------:|-------|------------:|\n")

            # Combine and sort all candidates
            all_candidates = []
            for candidate in long_candidates:
                candidate_copy = candidate.copy()
                candidate_copy['direction'] = 'LONG'
                all_candidates.append(candidate_copy)

            for candidate in short_candidates:
                candidate_copy = candidate.copy()
                candidate_copy['direction'] = 'SHORT'
                all_candidates.append(candidate_copy)

            # Sort by priority score and take top 5
            sorted_candidates = sorted(all_candidates, key=lambda x: x.get('priority_score', 0), reverse=True)
            top_candidates = sorted_candidates[:5]

            for candidate in top_candidates:
                symbol = candidate.get('symbol', 'N/A')
                direction = candidate.get('direction', 'N/A')
                priority = candidate.get('priority_score', candidate.get('priority', 0))

                # Get opening range data
                opening_range = app.opening_range_data.get(symbol, {})
                range_high = opening_range.get('high', 0)
                range_low = opening_range.get('low', 0)
                
                # Calculate range width with fallback to pre-stored value in candidate
                if 'range_width' in candidate and candidate['range_width'] > 0:
                    range_width = candidate['range_width']
                    logger.info(f"Using range_width stored in candidate for {symbol}: {range_width:.2f}%")
                elif range_low > 0:
                    range_width = ((range_high - range_low) / range_low * 100)
                    # Store the calculated range width in the candidate for later use
                    candidate['range_width'] = range_width
                    logger.info(f"Calculated range_width for {symbol}: {range_width:.2f}%")
                else:
                    range_width = 0
                    candidate['range_width'] = 0
                    logger.warning(f"Unable to calculate range_width for {symbol}: invalid low price")
                
                # Get volume data with robust fallbacks
                volume = candidate.get('volume', 0)
                if volume <= 0 and 'volume' in opening_range:
                    volume = opening_range.get('volume', 0)
                    candidate['volume'] = volume
                    logger.info(f"Used opening_range volume for {symbol}: {volume}")
                
                # Get scanner appearances with fallbacks
                scanner_count = 0
                if 'scanner_count' in candidate:
                    scanner_count = candidate['scanner_count']
                else:
                    scanners = candidate.get('scanners', [])
                    if isinstance(scanners, list):
                        scanner_count = len(scanners)
                    else:
                        # Last resort fallback for scanner count
                        scanner_count = candidate.get('appearances', 1)
                    
                    # Store for future use
                    candidate['scanner_count'] = scanner_count
                
                # Get trend data with optimized fallback logic
                trend = 'N/A'
                correlation = 0.0
                
                # First try to get trend data from candidate (it was stored there during processing)
                if 'trend' in candidate and candidate['trend'] not in (None, 'N/A', ''):
                    trend = candidate.get('trend', 'N/A')
                    logger.info(f"Using trend data stored directly in candidate for {symbol}: {trend}")
                
                # If not in candidate, try from daily_trend_data
                if (trend == 'N/A' or not trend) and symbol in daily_trend_data:
                    trend_data = daily_trend_data.get(symbol, {})
                    trend = trend_data.get('trend', 'N/A')
                    
                    # Copy it to candidate for future reference
                    candidate['trend'] = trend
                    candidate['trend_vwap'] = trend_data.get('vwap', 0)
                    candidate['trend_sma200'] = trend_data.get('sma200', 0)
                    candidate['vwap_correlation'] = trend_data.get('vwap_correlation', 0.0)
                    
                    logger.info(f"Using trend data from daily_trend_data for {symbol}: {trend}")
                
                # Get correlation with fallbacks
                if 'vwap_correlation' in candidate:
                    correlation = candidate.get('vwap_correlation', 0.0)
                elif symbol in daily_trend_data:
                    correlation = daily_trend_data.get(symbol, {}).get('vwap_correlation', 0.0)
                    # Store for future use
                    candidate['vwap_correlation'] = correlation
                
                f.write(f"| {symbol} | {direction} | {priority:.2f} | {range_width:.2f}% | {trend} | {correlation:.2f} |\n")

            f.write("\n")

        # Scanner results summary
        f.write("## Scanner Results Summary\n\n")
        f.write("| Scanner Type | Results |\n")
        f.write("|-------------|--------:|\n")
        for scanner_type, count in scanner_results.items():
            f.write(f"| {scanner_type} | {count} |\n")
        f.write(f"\n**Total Unique Symbols:** {len(combined_results)}\n\n")

        # Final candidates section - Moved up for visibility
        f.write("## Final Candidates\n\n")

        # Long candidates
        f.write("### Long Candidates\n\n")
        if long_candidates:
            f.write("| Symbol | Priority | Range Width | Volume | Scanners | Trend | Correlation |\n")
            f.write("|--------|----------:|------------:|-------:|----------|-------|------------:|\n")

            # Sort by priority score
            sorted_longs = sorted(long_candidates, key=lambda x: x.get('priority_score', 0), reverse=True)

            for candidate in sorted_longs:
                symbol = candidate.get('symbol', 'N/A')
                # Try priority_score first, then fall back to priority for backward compatibility
                priority = candidate.get('priority_score', candidate.get('priority', 0))

                # Get opening range data
                opening_range = app.opening_range_data.get(symbol, {})
                range_high = opening_range.get('high', 0)
                range_low = opening_range.get('low', 0)
                
                # Calculate range width with fallback to pre-stored value in candidate
                if 'range_width' in candidate and candidate['range_width'] > 0:
                    range_width = candidate['range_width']
                    logger.info(f"Using range_width stored in candidate for {symbol}: {range_width:.2f}%")
                elif range_low > 0:
                    range_width = ((range_high - range_low) / range_low * 100)
                    # Store the calculated range width in the candidate for later use
                    candidate['range_width'] = range_width
                    logger.info(f"Calculated range_width for {symbol}: {range_width:.2f}%")
                else:
                    range_width = 0
                    candidate['range_width'] = 0
                    logger.warning(f"Unable to calculate range_width for {symbol}: invalid low price")
                
                # Get volume data with robust fallbacks
                volume = candidate.get('volume', 0)
                if volume <= 0 and 'volume' in opening_range:
                    volume = opening_range.get('volume', 0)
                    candidate['volume'] = volume
                    logger.info(f"Used opening_range volume for {symbol}: {volume}")
                
                # Get scanner appearances with fallbacks
                scanner_count = 0
                if 'scanner_count' in candidate:
                    scanner_count = candidate['scanner_count']
                else:
                    scanners = candidate.get('scanners', [])
                    if isinstance(scanners, list):
                        scanner_count = len(scanners)
                    else:
                        # Last resort fallback for scanner count
                        scanner_count = candidate.get('appearances', 1)
                    
                    # Store for future use
                    candidate['scanner_count'] = scanner_count
                
                # Get trend data with optimized fallback logic
                trend = 'N/A'
                correlation = 0.0
                
                # First try to get trend data from candidate (it was stored there during processing)
                if 'trend' in candidate and candidate['trend'] not in (None, 'N/A', ''):
                    trend = candidate.get('trend', 'N/A')
                    logger.info(f"Using trend data stored directly in candidate for {symbol}: {trend}")
                
                # If not in candidate, try from daily_trend_data
                if (trend == 'N/A' or not trend) and symbol in daily_trend_data:
                    trend_data = daily_trend_data.get(symbol, {})
                    trend = trend_data.get('trend', 'N/A')
                    
                    # Copy it to candidate for future reference
                    candidate['trend'] = trend
                    candidate['trend_vwap'] = trend_data.get('vwap', 0)
                    candidate['trend_sma200'] = trend_data.get('sma200', 0)
                    candidate['vwap_correlation'] = trend_data.get('vwap_correlation', 0.0)
                    
                    logger.info(f"Using trend data from daily_trend_data for {symbol}: {trend}")
                
                # Get correlation with fallbacks
                if 'vwap_correlation' in candidate:
                    correlation = candidate.get('vwap_correlation', 0.0)
                elif symbol in daily_trend_data:
                    correlation = daily_trend_data.get(symbol, {}).get('vwap_correlation', 0.0)
                    # Store for future use
                    candidate['vwap_correlation'] = correlation
                
                f.write(f"| {symbol} | {priority:.2f} | {range_width:.2f}% | {volume:,} | {scanner_count} | {trend} | {correlation:.2f} |\n")
        else:
            f.write("*No long candidates found*\n\n")

        # Short candidates
        f.write("\n### Short Candidates\n\n")
        if short_candidates:
            f.write("| Symbol | Priority | Range Width | Volume | Scanners | Trend | Correlation |\n")
            f.write("|--------|----------:|------------:|-------:|----------|-------|------------:|\n")

            # Sort by priority score
            sorted_shorts = sorted(short_candidates, key=lambda x: x.get('priority_score', 0), reverse=True)

            for candidate in sorted_shorts:
                symbol = candidate.get('symbol', 'N/A')
                # Try priority_score first, then fall back to priority for backward compatibility
                priority = candidate.get('priority_score', candidate.get('priority', 0))

                # Get opening range data
                opening_range = app.opening_range_data.get(symbol, {})
                range_high = opening_range.get('high', 0)
                range_low = opening_range.get('low', 0)
                
                # Calculate range width with fallback to pre-stored value in candidate
                if 'range_width' in candidate and candidate['range_width'] > 0:
                    range_width = candidate['range_width']
                    logger.info(f"Using range_width stored in candidate for {symbol}: {range_width:.2f}%")
                elif range_low > 0:
                    range_width = ((range_high - range_low) / range_low * 100)
                    # Store the calculated range width in the candidate for later use
                    candidate['range_width'] = range_width
                    logger.info(f"Calculated range_width for {symbol}: {range_width:.2f}%")
                else:
                    range_width = 0
                    candidate['range_width'] = 0
                    logger.warning(f"Unable to calculate range_width for {symbol}: invalid low price")
                
                # Get volume data with robust fallbacks
                volume = candidate.get('volume', 0)
                if volume <= 0 and 'volume' in opening_range:
                    volume = opening_range.get('volume', 0)
                    candidate['volume'] = volume
                    logger.info(f"Used opening_range volume for {symbol}: {volume}")
                
                # Get scanner appearances with fallbacks
                scanner_count = 0
                if 'scanner_count' in candidate:
                    scanner_count = candidate['scanner_count']
                else:
                    scanners = candidate.get('scanners', [])
                    if isinstance(scanners, list):
                        scanner_count = len(scanners)
                    else:
                        # Last resort fallback for scanner count
                        scanner_count = candidate.get('appearances', 1)
                    
                    # Store for future use
                    candidate['scanner_count'] = scanner_count
                
                # Get trend data with optimized fallback logic
                trend = 'N/A'
                correlation = 0.0
                
                # First try to get trend data from candidate (it was stored there during processing)
                if 'trend' in candidate and candidate['trend'] not in (None, 'N/A', ''):
                    trend = candidate.get('trend', 'N/A')
                    logger.info(f"Using trend data stored directly in candidate for {symbol}: {trend}")
                
                # If not in candidate, try from daily_trend_data
                if (trend == 'N/A' or not trend) and symbol in daily_trend_data:
                    trend_data = daily_trend_data.get(symbol, {})
                    trend = trend_data.get('trend', 'N/A')
                    
                    # Copy it to candidate for future reference
                    candidate['trend'] = trend
                    candidate['trend_vwap'] = trend_data.get('vwap', 0)
                    candidate['trend_sma200'] = trend_data.get('sma200', 0)
                    candidate['vwap_correlation'] = trend_data.get('vwap_correlation', 0.0)
                    
                    logger.info(f"Using trend data from daily_trend_data for {symbol}: {trend}")
                
                # Get correlation with fallbacks
                if 'vwap_correlation' in candidate:
                    correlation = candidate.get('vwap_correlation', 0.0)
                elif symbol in daily_trend_data:
                    correlation = daily_trend_data.get(symbol, {}).get('vwap_correlation', 0.0)
                    # Store for future use
                    candidate['vwap_correlation'] = correlation
                
                f.write(f"| {symbol} | {priority:.2f} | {range_width:.2f}% | {volume:,} | {scanner_count} | {trend} | {correlation:.2f} |\n")
        else:
            f.write("*No short candidates found*\n\n")

        # Filtering process
        f.write("## Filtering Process\n\n")
        if filtering_stats:
            f.write("| Filter Stage | Before | After | Rejected | % Rejected |\n")
            f.write("|--------------|-------:|------:|---------:|-----------:|\n")

            for stage, stats in filtering_stats.items():
                before = stats.get('before', 0)
                after = stats.get('after', 0)

                # Handle the case where symbols are added rather than rejected
                if after > before:
                    # This is the case for initial_scan where we combine multiple scanner results
                    symbols_added = after - before
                    f.write(f"| {stage} | {before} | {after} | +{symbols_added} | N/A |\n")
                    logger.info(f"Combined {before} scanner results → {after} unique symbols (+{symbols_added} from multi-scanner)")
                else:
                    # Normal case where filtering reduces the number of symbols
                    rejected = before - after
                    pct_rejected = (rejected / before * 100) if before > 0 else 0
                    f.write(f"| {stage} | {before} | {after} | {rejected} | {pct_rejected:.1f}% |\n")
        else:
            f.write("*No filtering statistics available*\n\n")

        # Breakout Detection Formula Section
        f.write("## Breakout Detection Formula\n\n")
        f.write("During the monitoring phase, the following formula is used to detect breakouts and trigger trades:\n\n")

        # VIX-based breakout parameters
        volume_threshold = 1.5  # Default
        breakout_buffer = 0.1   # Default
        min_range_width = 0.5   # Default
        max_range_width = 3.0   # Default

        if vix_level is not None:
            if vix_level > 35:
                volume_threshold = 4.0
                breakout_buffer = 0.2
                min_range_width = 0.8
                max_range_width = 4.0
                f.write("**High Volatility Parameters (VIX > 35):**\n")
            elif vix_level > 25:
                volume_threshold = 2.5
                breakout_buffer = 0.15
                min_range_width = 0.6
                max_range_width = 3.5
                f.write("**Elevated Volatility Parameters (VIX > 25):**\n")
            else:
                f.write("**Standard Parameters (VIX ≤ 25):**\n")
        else:
            f.write("**Standard Parameters (VIX level unknown):**\n")

        f.write(f"- Volume Threshold: {volume_threshold}x (current volume must exceed baseline by this factor)\n")
        f.write(f"- Breakout Buffer: {breakout_buffer}% (price must exceed range by this percentage)\n")
        f.write(f"- Range Width Requirements: {min_range_width}% to {max_range_width}%\n\n")

        f.write("### LONG Breakout Formula\n\n")
        f.write("For a LONG breakout to trigger, ALL of the following conditions must be met:\n\n")
        f.write("* **Symbol Type:** The symbol must be classified as a LONG candidate\n")
        f.write(f"* **Price Breakout:** Current price must exceed the opening range high by at least {breakout_buffer}%\n")
        f.write(f"  * Example: If opening high is $100.00, price must exceed ${100 * (1 + breakout_buffer/100):.2f}\n")
        f.write(f"* **Volume Confirmation:** Current volume must be at least {volume_threshold}x the baseline volume\n")
        f.write(f"  * Baseline is the greater of opening range volume or average daily volume\n")
        f.write(f"* **Range Width Validation:** Opening range width must be between {min_range_width}% and {max_range_width}%\n")
        f.write(f"  * Example: For a $100 stock, range width should be ${min_range_width:.2f} to ${max_range_width:.2f}\n\n")

        f.write("### SHORT Breakout Formula\n\n")
        f.write("For a SHORT breakout to trigger, ALL of the following conditions must be met:\n\n")
        f.write("* **Symbol Type:** The symbol must be classified as a SHORT candidate\n")
        f.write(f"* **Price Breakout:** Current price must fall below the opening range low by at least {breakout_buffer}%\n")
        f.write(f"  * Example: If opening low is $100.00, price must fall below ${100 * (1 - breakout_buffer/100):.2f}\n")
        f.write(f"* **Volume Confirmation:** Current volume must be at least {volume_threshold}x the baseline volume\n")
        f.write(f"  * Baseline is the greater of opening range volume or average daily volume\n")
        f.write(f"* **Range Width Validation:** Opening range width must be between {min_range_width}% and {max_range_width}%\n")
        f.write(f"  * Example: For a $100 stock, range width should be ${min_range_width:.2f} to ${max_range_width:.2f}\n\n")

        # Trend alignment section
        f.write("## Trend Alignment\n\n")
        if hasattr(app, 'use_trend_alignment') and app.use_trend_alignment:
            f.write(f"Trend alignment is **enabled**\n\n")

            # VIX-based settings
            if vix_level is not None:
                if vix_level > 30:
                    f.write("**VIX > 30:** Strict trend alignment applied (correlation threshold: 0.8)\n\n")
                elif vix_level > 25:
                    f.write("**VIX > 25:** Moderate trend alignment applied (correlation threshold: 0.6)\n\n")
                elif vix_level > 20:
                    # Calculate the interpolated threshold
                    interpolation_factor = (vix_level - 20) / 5
                    threshold = 0.4 + interpolation_factor * 0.2
                    f.write(f"**VIX {vix_level:.1f}:** Dynamic trend alignment applied (correlation threshold: {threshold:.2f}, interpolated)\n\n")
                else:
                    f.write("**VIX ≤ 20:** No trend alignment filtering applied\n\n")

            # Trend alignment statistics
            if hasattr(app, 'trend_alignment_stats'):
                f.write("### Trend Alignment Statistics\n\n")
                f.write("| Direction | Aligned | Rejected | Missing Data | Total |\n")
                f.write("|-----------|--------:|---------:|-------------:|------:|\n")

                for direction, stats in app.trend_alignment_stats.items():
                    aligned = stats.get('aligned', 0)
                    rejected = stats.get('rejected', 0)
                    missing = stats.get('missing', 0)
                    total = aligned + rejected + missing
                    f.write(f"| {direction} | {aligned} | {rejected} | {missing} | {total} |\n")

            # Rejected due to trend
            f.write("\n### Symbols Rejected Due to Trend Misalignment\n\n")
            f.write("| Symbol | Direction | Trend | Correlation | Threshold | Reason |\n")
            f.write("|--------|-----------|-------|------------:|----------:|--------|\n")

            if 'trend_rejected' in rejected_candidates and rejected_candidates['trend_rejected']:
                # Group by rejection reason
                missing_data = []
                misaligned_trend = []

                for symbol, data in rejected_candidates['trend_rejected'].items():
                    trend = data.get('trend', 'N/A')
                    if trend == 'NO_DATA':
                        missing_data.append((symbol, data))
                    else:
                        misaligned_trend.append((symbol, data))

                # First show misaligned trend symbols
                for symbol, data in sorted(misaligned_trend, key=lambda x: x[0]):
                    direction = data.get('direction', 'N/A')
                    trend = data.get('trend', 'N/A')
                    correlation = data.get('correlation', 0.0)
                    threshold = data.get('threshold', 0.0)
                    reason = "Insufficient correlation" if correlation != 0.0 else "Unknown"

                    f.write(f"| {symbol} | {direction} | {trend} | {correlation:.2f} | {threshold:.2f} | {reason} |\n")

                # Then show missing data symbols
                for symbol, data in sorted(missing_data, key=lambda x: x[0]):
                    direction = data.get('direction', 'N/A')
                    f.write(f"| {symbol} | {direction} | NO_DATA | 0.00 | 0.00 | Missing trend data |\n")
            else:
                f.write("| *No symbols rejected due to trend misalignment* | | | | | |")
        else:
            f.write("Trend alignment is **disabled**\n\n")

        # Rejected candidates section (other than trend)
        f.write("## Other Rejected Candidates\n\n")

        # Range width rejections
        if 'range_width_rejected' in rejected_candidates and rejected_candidates['range_width_rejected']:
            f.write("### Rejected Due to Range Width\n\n")
            f.write("| Symbol | Direction | Range Width | Min | Max | Reason |\n")
            f.write("|--------|-----------|------------:|----:|----:|--------|\n")

            for symbol, data in sorted(rejected_candidates['range_width_rejected'].items()):
                width = data.get('width', 0.0)
                min_width = data.get('min', 0.0)
                max_width = data.get('max', 0.0)

                # Determine reason for rejection
                if width < min_width:
                    reason = "Range too narrow"
                elif width > max_width:
                    reason = "Range too wide"
                else:
                    reason = "Invalid range"

                f.write(f"| {symbol} | {data.get('direction', 'N/A')} | {width:.2f}% | {min_width:.2f}% | {max_width:.2f}% | {reason} |\n")
        else:
            f.write("### Rejected Due to Range Width\n\n")
            f.write("| Symbol | Direction | Range Width | Min | Max | Reason |\n")
            f.write("|--------|-----------|------------:|----:|----:|--------|\n")
            f.write("| *No symbols rejected due to range width* | | | | | |\n")

        # Volume rejections
        if 'volume_rejected' in rejected_candidates and rejected_candidates['volume_rejected']:
            f.write("\n### Rejected Due to Insufficient Volume\n\n")
            f.write("| Symbol | Direction | Volume | Threshold | Reason |\n")
            f.write("|--------|-----------|-------:|----------:|--------|\n")

            for symbol, data in sorted(rejected_candidates['volume_rejected'].items()):
                volume = data.get('volume', 0)
                threshold = data.get('threshold', 0)
                reason = f"Volume {volume:,} below threshold {threshold:,}"

                f.write(f"| {symbol} | {data.get('direction', 'N/A')} | {volume:,} | {threshold:,} | {reason} |\n")
        else:
            f.write("\n### Rejected Due to Insufficient Volume\n\n")
            f.write("| Symbol | Direction | Volume | Threshold | Reason |\n")
            f.write("|--------|-----------|-------:|----------:|--------|\n")
            f.write("| *No symbols rejected due to insufficient volume* | | | | |\n")

        # Liquidity rejections
        if 'liquidity_rejected' in rejected_candidates and rejected_candidates['liquidity_rejected']:
            f.write("\n### Rejected Due to Liquidity Issues\n\n")
            f.write("| Symbol | Direction | Issue | Details |\n")
            f.write("|--------|-----------|-------|--------|\n")

            for symbol, data in sorted(rejected_candidates['liquidity_rejected'].items()):
                issue = data.get('reason', 'Unknown')
                details = data.get('details', '')

                f.write(f"| {symbol} | {data.get('direction', 'N/A')} | {issue} | {details} |\n")
        else:
            f.write("\n### Rejected Due to Liquidity Issues\n\n")
            f.write("| Symbol | Direction | Issue | Details |\n")
            f.write("|--------|-----------|-------|--------|\n")
            f.write("| *No symbols rejected due to liquidity issues* | | | |\n")

    logger.info(f"Finalizing report generated: {report_path}")

    # Email the report if requested
    if email_report:
        try:
            # Try SendGrid email first (preferred)
            try:
                from sendgrid_email import email_report as send_email
                logger.info("Using SendGrid for email delivery")
            except ImportError:
                # Fall back to standard email if SendGrid is not available
                logger.warning("Could not import sendgrid_email module. Trying standard email module...")
                from email_report import email_report as send_email
                logger.info("Using standard SMTP for email delivery")

            # Create a subject with date information
            date_str = current_time.strftime('%Y-%m-%d')
            subject = f"7-Minute Scanner Report - {date_str}"

            # Create additional message with summary information
            long_count = len(long_candidates)
            short_count = len(short_candidates)
            message = f"Summary: Found {long_count} long and {short_count} short candidates.\n"

            if vix_level is not None:
                message += f"VIX Level: {vix_level:.2f}\n"

            # Send the email
            email_result = send_email(report_path, subject, message)
            if email_result:
                logger.info(f"Report emailed successfully")
            else:
                logger.warning(f"Failed to email report")
        except ImportError:
            logger.warning("Could not import any email module. Email not sent.")
        except Exception as e:
            logger.error(f"Error emailing report: {e}")

    return report_path

if __name__ == "__main__":
    # This can be used for testing the module independently
    import argparse

    parser = argparse.ArgumentParser(description="Generate 7-Minute Scanner Finalizing Report")
    parser.add_argument("--log-base", required=True, help="Base path for log files")
    args = parser.parse_args()

    # Setup logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    print(f"This module is designed to be called from the main scanner. For testing, use:")
    print(f"python -c \"from scripts.7minScan.finalizing_report import generate_finalizing_report; generate_finalizing_report(None, '{args.log_base}')\"")
