#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SendGrid Email Module for 7-Minute Scanner

This module provides functionality to email reports using SendGrid Web API.
"""

import os
import logging
import time
import base64
import markdown

# Configure logger
logger = logging.getLogger(__name__)

# SendGrid API settings
SENDGRID_API_KEY = "*********************************************************************"
FROM_EMAIL = "<EMAIL>"  # Your verified sender email
TO_EMAIL = "<EMAIL>"  # Recipient email

def email_report(report_path, subject=None, additional_text=None, max_retries=3, retry_delay=5):
    """
    Email the report using SendGrid Web API.
    
    Args:
        report_path (str): Path to the Markdown report file
        subject (str, optional): Email subject. Defaults to a generic subject.
        additional_text (str, optional): Additional text to include in the email body.
        max_retries (int, optional): Maximum number of retry attempts. Defaults to 3.
        retry_delay (int, optional): Delay between retries in seconds. Defaults to 5.
        
    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    try:
        # Import SendGrid modules
        try:
            from sendgrid import SendGridAPIClient
            from sendgrid.helpers.mail import (
                Mail, Attachment, FileContent, FileName, 
                FileType, Disposition, ContentId
            )
        except ImportError:
            logger.error("SendGrid library not installed. Run 'pip install sendgrid'")
            return False
        
        if not os.path.exists(report_path):
            logger.error(f"Report file not found: {report_path}")
            return False
        
        # Read the report file
        with open(report_path, 'r') as f:
            md_content = f.read()
        
        # Convert Markdown to HTML for email body
        html_content = markdown.markdown(md_content, extensions=['tables'])
        
        # Set email subject
        if subject is None:
            # Extract date from filename if possible
            filename = os.path.basename(report_path)
            if 'scan_7min_' in filename:
                date_part = filename.split('scan_7min_')[1].split('_')[0]
                subject = f"7-Minute Scanner Report - {date_part}"
            else:
                subject = "7-Minute Scanner Report"
        
        # Create text content
        text_content = "Please see the attached 7-Minute Scanner report.\n\n"
        if additional_text:
            text_content += additional_text + "\n\n"
        text_content += "Note: This email contains an HTML version of the report for better readability, "
        text_content += "as well as the original Markdown file as an attachment."
        
        # Create message
        message = Mail(
            from_email=FROM_EMAIL,
            to_emails=TO_EMAIL,
            subject=subject,
            plain_text_content=text_content,
            html_content=html_content
        )
        
        # Attach the original Markdown file
        with open(report_path, 'rb') as f:
            file_content = base64.b64encode(f.read()).decode()
            
        attachment = Attachment()
        attachment.file_content = FileContent(file_content)
        attachment.file_name = FileName(os.path.basename(report_path))
        attachment.file_type = FileType('text/markdown')
        attachment.disposition = Disposition('attachment')
        attachment.content_id = ContentId('Report')
        
        message.attachment = attachment
        
        # Send the email with retry logic
        for attempt in range(max_retries):
            try:
                logger.info(f"Sending email attempt {attempt + 1}/{max_retries}...")
                sg = SendGridAPIClient(SENDGRID_API_KEY)
                response = sg.send(message)
                logger.info(f"Email sent successfully with status code: {response.status_code}")
                return True
            except Exception as e:
                logger.warning(f"Error sending email (attempt {attempt + 1}/{max_retries}): {e}")
                
                # Get more detailed error information
                if hasattr(e, 'body'):
                    try:
                        import json
                        error_body = json.loads(e.body.decode())
                        logger.error(f"Error details: {json.dumps(error_body, indent=2)}")
                    except Exception as json_err:
                        logger.error(f"Could not parse error body: {json_err}")
                        logger.error(f"Raw error body: {e.body if hasattr(e, 'body') else 'No body'}")
                
                if attempt < max_retries - 1:
                    logger.info(f"Retrying in {retry_delay} seconds...")
                    time.sleep(retry_delay)
                    # Increase delay for next attempt
                    retry_delay *= 2
                else:
                    logger.error(f"Failed to send email after {max_retries} attempts")
                    return False
                    
    except Exception as e:
        logger.error(f"Error preparing email: {e}")
        return False

if __name__ == "__main__":
    # This can be used for testing the module independently
    import argparse
    
    # Setup logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    parser = argparse.ArgumentParser(description="Email 7-Minute Scanner Report using SendGrid")
    parser.add_argument("report_path", help="Path to the report file")
    parser.add_argument("--subject", help="Email subject")
    parser.add_argument("--message", help="Additional message to include in the email")
    parser.add_argument("--retries", type=int, default=3, help="Maximum number of retry attempts")
    parser.add_argument("--retry-delay", type=int, default=5, help="Delay between retries in seconds")
    
    args = parser.parse_args()
    
    result = email_report(args.report_path, args.subject, args.message, args.retries, args.retry_delay)
    if result:
        print(f"Email sent successfully to {TO_EMAIL}")
    else:
        print("Failed to send email. Check logs for details.")
