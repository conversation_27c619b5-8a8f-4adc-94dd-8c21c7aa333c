"""
Selection Logger for 7-Minute Scanner

This module provides a consolidated logging system for the symbol selection process,
replacing the multiple selection log files with a single file containing sections
for each scan iteration.
"""

import os
import logging
from datetime import datetime

class SelectionLogger:
    """
    Handles logging of symbol selection process to a single consolidated file.
    """
    
    def __init__(self, log_base_path=None):
        """
        Initialize the selection logger.
        
        Args:
            log_base_path (str): Base path for log files. If None, uses default.
        """
        # Create log file path
        if log_base_path is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            log_dir = "logs/live"
            os.makedirs(log_dir, exist_ok=True)
            self.log_file = f"{log_dir}/scan_7min_{timestamp}_selection.log"
        else:
            self.log_file = f"{log_base_path}_selection.log"
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(self.log_file), exist_ok=True)
        
        # Initialize the log file
        with open(self.log_file, 'w') as f:
            f.write("# 7-Minute Scanner Selection Log\n")
            f.write(f"# Created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        self.iteration = 0
        self.logger = logging.getLogger('selection_logger')
        
        # Set up file handler for standard logging
        file_handler = logging.FileHandler(self.log_file, mode='a', encoding='utf-8')
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
        self.logger.setLevel(logging.INFO)
        self.logger.propagate = False
    
    def start_iteration(self, vix_level, is_continuous, apply_trend_filtering, phase=None):
        """
        Start a new scan iteration section in the log.
        
        Args:
            vix_level (float): Current VIX level
            is_continuous (bool): Whether this is a continuous scan
            apply_trend_filtering (bool): Whether trend filtering is applied
            phase (str): Current scanner phase
        """
        self.iteration += 1
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        with open(self.log_file, 'a') as f:
            f.write("\n" + "=" * 80 + "\n")
            f.write(f"SCAN ITERATION #{self.iteration} - {timestamp}")
            if phase:
                f.write(f" - PHASE: {phase}")
            f.write("\n" + "=" * 80 + "\n")
            f.write(f"VIX Level: {vix_level}\n")
            f.write(f"Continuous Mode: {is_continuous}\n")
            f.write(f"Apply Trend Filtering: {apply_trend_filtering}\n")
            f.write("=" * 80 + "\n\n")
    
    def log_scanner_results(self, scanner_results):
        """
        Log scanner results for the current iteration.
        
        Args:
            scanner_results (dict): Dictionary of scanner results by scanner type
        """
        with open(self.log_file, 'a') as f:
            f.write("SCANNER RESULTS:\n")
            f.write("-" * 80 + "\n")
            
            total_results = 0
            
            for scanner_type, results in scanner_results.items():
                result_count = len(results) if results else 0
                total_results += result_count
                f.write(f"Scanner {scanner_type}: {result_count} results\n")
                
                if results and result_count > 0:
                    f.write("Top symbols from {}:\n".format(scanner_type))
                    for i, result in enumerate(results[:10]):  # Show top 10
                        symbol = result.get('symbol', 'unknown')
                        rank = result.get('rank', i)
                        f.write(f"  {i+1}. {symbol} (Rank: {rank})\n")
                    f.write("\n")
            
            f.write(f"Total results across all scanners: {total_results}\n")
            f.write("-" * 80 + "\n\n")
    
    def log_symbol_appearances(self, symbol_appearances):
        """
        Log symbol appearance counts.
        
        Args:
            symbol_appearances (dict): Dictionary of symbol appearance counts
        """
        with open(self.log_file, 'a') as f:
            f.write("SYMBOL PROCESSING:\n")
            f.write("-" * 80 + "\n")
            f.write(f"Total unique symbols: {len(symbol_appearances)}\n\n")
            f.write("Symbol appearance counts:\n")
            
            # Log the top 50 symbols by appearance count
            sorted_appearances = sorted(symbol_appearances.items(), key=lambda x: x[1], reverse=True)
            for i, (symbol, count) in enumerate(sorted_appearances[:50]):
                f.write(f"{i+1}. {symbol}: {count} scanner(s)\n")
            
            if len(sorted_appearances) > 50:
                f.write(f"\n(Showing top 50 of {len(sorted_appearances)} symbols)\n")
            
            f.write("\n" + "-" * 80 + "\n\n")
    
    def log_final_candidates(self, long_candidates, short_candidates, filtering_stats=None):
        """
        Log final candidate selection.
        
        Args:
            long_candidates (list): List of long candidates
            short_candidates (list): List of short candidates
            filtering_stats (dict): Statistics about filtering process
        """
        with open(self.log_file, 'a') as f:
            f.write("FINAL CANDIDATE SELECTION:\n")
            f.write("-" * 80 + "\n")
            f.write(f"Long candidates: {len(long_candidates)}\n")
            f.write(f"Short candidates: {len(short_candidates)}\n\n")
            
            # Log top long candidates
            f.write("Top long candidates:\n")
            for i, candidate in enumerate(long_candidates[:20]):  # Show top 20
                symbol = candidate.get('symbol', 'unknown')
                score = candidate.get('long_score', 0)
                scanners = candidate.get('scanner_count', 0)
                trend = candidate.get('trend', 'NO_DATA')
                f.write(f"{i+1}. {symbol}: Score={score:.2f}, Scanners={scanners}, Trend={trend}\n")
            
            # Log top short candidates
            f.write("\nTop short candidates:\n")
            for i, candidate in enumerate(short_candidates[:20]):  # Show top 20
                symbol = candidate.get('symbol', 'unknown')
                score = candidate.get('short_score', 0)
                scanners = candidate.get('scanner_count', 0)
                trend = candidate.get('trend', 'NO_DATA')
                f.write(f"{i+1}. {symbol}: Score={score:.2f}, Scanners={scanners}, Trend={trend}\n")
            
            # Log filtering statistics
            if filtering_stats:
                f.write("\nFiltering statistics:\n")
                for stage, stats in filtering_stats.items():
                    if isinstance(stats, tuple) and len(stats) == 3:
                        before, after, rejected = stats
                        pct_rejected = (rejected / before * 100) if before > 0 else 0
                        f.write(f"{stage}: {before} → {after} symbols ({rejected} removed, {pct_rejected:.1f}%)\n")
            
            f.write("\n" + "-" * 80 + "\n\n")
    
    def log_filtered_symbols(self, filtered_symbols):
        """
        Log the final filtered symbols list.
        
        Args:
            filtered_symbols (list): List of filtered symbols
        """
        with open(self.log_file, 'a') as f:
            f.write("FINAL FILTERED SYMBOLS LIST:\n")
            f.write("-" * 80 + "\n")
            f.write(f"Total symbols: {len(filtered_symbols)}\n\n")
            
            if filtered_symbols:
                for i, symbol in enumerate(filtered_symbols):
                    f.write(f"{i+1}. {symbol}\n")
            else:
                f.write("No symbols in filtered list!\n")
            
            f.write("\n" + "-" * 80 + "\n")
    
    def log_summary(self, stats):
        """
        Log a summary of the entire selection process.
        
        Args:
            stats (dict): Statistics about the selection process
        """
        with open(self.log_file, 'a') as f:
            f.write("\n" + "=" * 80 + "\n")
            f.write("SELECTION PROCESS SUMMARY\n")
            f.write("=" * 80 + "\n")
            f.write(f"Total scan iterations: {self.iteration}\n")
            
            if stats:
                for key, value in stats.items():
                    f.write(f"{key}: {value}\n")
            
            f.write("=" * 80 + "\n")
    
    def log_message(self, message, level=logging.INFO):
        """
        Log a message to the selection log.
        
        Args:
            message (str): Message to log
            level (int): Logging level
        """
        self.logger.log(level, message)
