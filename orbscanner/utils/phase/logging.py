"""
Enhanced phase logging for the 7-minute scanner.

This module provides enhanced logging functionality for the 7-minute scanner,
with a focus on phase transitions and status updates.
"""

import logging
import os
from datetime import datetime

from orbscanner.utils.time.timezone import format_timestamp, get_eastern_time

# Global dictionary to store phase loggers
PHASE_LOGGERS = {}

def setup_phase_logger(phase_name, log_file):
    """
    Set up a logger for a specific phase with improved file handling.

    Args:
        phase_name (str): The name of the phase
        log_file (str): Path to the log file

    Returns:
        logging.Logger: The configured logger
    """
    # Check if logger already exists to prevent duplicate initialization
    if phase_name in PHASE_LOGGERS:
        logging.debug(f"Phase logger {phase_name} already initialized. Skipping setup.")
        return PHASE_LOGGERS[phase_name]
        
    # Ensure log directory exists
    log_dir = os.path.dirname(log_file)
    if log_dir and not os.path.exists(log_dir):
        try:
            os.makedirs(log_dir, exist_ok=True)
            logging.info(f"Created log directory for phase {phase_name}: {log_dir}")
        except Exception as e:
            logging.error(f"Error creating log directory {log_dir} for phase {phase_name}: {e}")

    logger = logging.getLogger(phase_name)
    logger.setLevel(logging.INFO)

    # Remove any existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    try:
        # Add file handler with immediate flushing
        file_handler = logging.FileHandler(log_file, mode='a', encoding='utf-8', delay=False)
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))

        # Set level to ensure proper logging
        file_handler.setLevel(logging.INFO)
        logger.addHandler(file_handler)

        # Log a test message to ensure the file handler is working
        logger.info(f"Phase logger {phase_name} initialized with file handler to {log_file}")

        # Explicitly flush the handler to ensure the message is written
        file_handler.flush()

        logging.info(f"Successfully set up phase logger {phase_name} with file {log_file}")
    except Exception as e:
        # Log any errors that occur when setting up the file handler
        logging.error(f"Error setting up file handler for {phase_name}: {e}")
        # Try a fallback approach
        try:
            # Try with a different mode
            fallback_log = f"/tmp/phase_{phase_name.lower()}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
            logging.warning(f"Attempting fallback log file for {phase_name}: {fallback_log}")

            file_handler = logging.FileHandler(fallback_log, mode='w', encoding='utf-8')
            file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
            logger.addHandler(file_handler)
            logging.warning(f"Using fallback file handler for {phase_name}: {fallback_log}")
        except Exception as e2:
            logging.error(f"Fallback file handler also failed for {phase_name}: {e2}")

    # Don't propagate to root logger to avoid duplicate entries in main log
    logger.propagate = False

    # Store logger in global dictionary
    PHASE_LOGGERS[phase_name] = logger

    return logger

def log_to_phase(phase, message, level=logging.INFO, force_flush=False):
    """
    Log a message to both the main log and the phase-specific log with improved error handling.

    Args:
        phase (str): The phase to log to (e.g., PHASE_SCANNING)
        message (str): The message to log
        level (int): The logging level (default: INFO)
        force_flush (bool): Whether to force a flush of the file handler (default: False)
    """
    # Ensure level is an integer
    if not isinstance(level, int):
        level = logging.INFO  # Default to INFO if level is not an integer
        logging.warning(f"Non-integer logging level provided: {level}. Defaulting to INFO.")

    # Log to main log
    logging.log(level, message)

    # Always flush root logger handlers for important messages
    if level >= logging.WARNING or force_flush:
        for handler in logging.root.handlers:
            if hasattr(handler, 'flush'):
                handler.flush()

    # Log to phase-specific log if it exists
    if phase in PHASE_LOGGERS:
        try:
            # Get the phase logger
            phase_logger = PHASE_LOGGERS[phase]

            # Log the message
            phase_logger.log(level, message)

            # Explicitly flush all handlers to ensure the message is written to the file
            # Always flush for ERROR and CRITICAL messages, or if force_flush is True
            if level >= logging.WARNING or force_flush:
                for handler in phase_logger.handlers:
                    if hasattr(handler, 'flush'):
                        handler.flush()
        except Exception as e:
            # Log any errors that occur when logging to the phase-specific log
            logging.error(f"Error logging to {phase}: {e}")

            # Ensure this error message is flushed
            for handler in logging.root.handlers:
                if hasattr(handler, 'flush'):
                    handler.flush()

            # Try to recreate the logger if it's not working
            try:
                # Find the log file path from the existing handler
                log_file = None
                if 'phase_logger' in locals() and hasattr(phase_logger, 'handlers'):
                    for handler in phase_logger.handlers:
                        if isinstance(handler, logging.FileHandler) and hasattr(handler, 'baseFilename'):
                            log_file = handler.baseFilename
                            break

                # If we found a log file path, recreate the logger
                if log_file:
                    logging.warning(f"Attempting to recreate logger for {phase} with file {log_file}")
                    setup_phase_logger(phase, log_file)

                    # Try logging again with the new logger
                    if phase in PHASE_LOGGERS:
                        PHASE_LOGGERS[phase].log(level, message)

                        # Force flush after recreation
                        for handler in PHASE_LOGGERS[phase].handlers:
                            if hasattr(handler, 'flush'):
                                handler.flush()
            except Exception as e2:
                logging.error(f"Failed to recreate logger for {phase}: {e2}")

                # Ensure this error message is flushed
                for handler in logging.root.handlers:
                    if hasattr(handler, 'flush'):
                        handler.flush()

def log_phase_transition(phase_manager, from_phase, to_phase, reason):
    """
    Log a phase transition with detailed information.

    Args:
        phase_manager: The phase manager object
        from_phase (str): The phase transitioning from
        to_phase (str): The phase transitioning to
        reason (str): The reason for the transition
    """
    separator = "=" * 80

    # Add enhanced debugging for phase transitions
    current_time = get_eastern_time()
    market_open = current_time.replace(hour=9, minute=30, second=0, microsecond=0)

    # Log detailed phase transition debug information
    logging.info(f"PHASE_TRANSITION_DEBUG: From {from_phase} to {to_phase}")
    logging.info(f"PHASE_TRANSITION_DEBUG: Current time: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"PHASE_TRANSITION_DEBUG: Market open time: {market_open.strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"PHASE_TRANSITION_DEBUG: Time since market open: {(current_time - market_open).total_seconds() / 60:.2f} minutes")

    # Log phase duration information
    if hasattr(phase_manager, 'get_phase_duration'):
        from_duration = phase_manager.get_phase_duration(from_phase)
        to_duration = phase_manager.get_phase_duration(to_phase)
        logging.info(f"PHASE_TRANSITION_DEBUG: {from_phase} duration: {from_duration.total_seconds()/60:.2f} minutes")
        logging.info(f"PHASE_TRANSITION_DEBUG: {to_phase} duration: {to_duration.total_seconds()/60:.2f} minutes")

    # Calculate actual phase duration
    if from_phase in phase_manager.phase_start_times:
        # Get elapsed time directly from phase manager
        actual_duration = phase_manager.get_elapsed_time(from_phase) / 60
        configured_duration = phase_manager.get_phase_duration(from_phase)
        if configured_duration:
            configured_duration = configured_duration.total_seconds() / 60
        else:
            configured_duration = 0
    else:
        actual_duration = 0
        configured_duration = 0

    # Current time with timezone
    current_time = get_eastern_time()

    # Get data quality information if available
    has_data_quality = hasattr(phase_manager, 'valid_symbols') and hasattr(phase_manager, 'total_symbols')
    if has_data_quality:
        valid_symbols = phase_manager.valid_symbols.get(from_phase, 0)
        total_symbols = phase_manager.total_symbols.get(from_phase, 0)
        has_valid_data = phase_manager.has_valid_data(from_phase)
        data_quality_info = f"Data quality: {valid_symbols}/{total_symbols} symbols with valid data"
        if has_valid_data:
            data_quality_status = "✓ At least one symbol has valid data - transition is data-valid"
        else:
            data_quality_status = "⚠ NO symbols have valid data - transition may produce invalid results"
    else:
        data_quality_info = "Data quality tracking not available"
        data_quality_status = ""

    # Log to the from_phase log
    log_to_phase(from_phase, separator)
    log_to_phase(from_phase, f"PHASE TRANSITION: {from_phase} → {to_phase}")
    log_to_phase(from_phase, f"Reason: {reason}")
    log_to_phase(from_phase, f"Phase completed after {actual_duration:.2f} minutes (configured: {configured_duration:.2f} minutes)")
    log_to_phase(from_phase, f"Current time: {format_timestamp(current_time)}")

    # Add data quality information if available
    if has_data_quality:
        log_to_phase(from_phase, data_quality_info)
        if data_quality_status:
            log_to_phase(from_phase, data_quality_status)

    log_to_phase(from_phase, separator)

    # Log to the to_phase log
    log_to_phase(to_phase, separator)
    log_to_phase(to_phase, f"PHASE TRANSITION: {from_phase} → {to_phase}")
    log_to_phase(to_phase, f"Reason: {reason}")
    log_to_phase(to_phase, f"Phase started at: {format_timestamp(current_time)}")

    # Add data quality information if available
    if has_data_quality:
        log_to_phase(to_phase, data_quality_info)
        if data_quality_status:
            log_to_phase(to_phase, data_quality_status)

    # Add end time if available
    end_time = phase_manager.get_phase_end_time(to_phase)
    if end_time:
        log_to_phase(to_phase, f"Phase will end at: {format_timestamp(end_time)}")

    log_to_phase(to_phase, separator)

    # Also log to main logger with data quality info
    log_msg = f"PHASE TRANSITION: {from_phase} → {to_phase} ({reason})"
    if has_data_quality:
        log_msg += f" - {data_quality_info}"
    logging.info(log_msg)

def log_phase_status(phase_manager):
    """
    Log the current phase status with timing information.

    Args:
        phase_manager: The phase manager object
    """
    current_phase = phase_manager.current_phase
    if not current_phase:
        return

    elapsed = phase_manager.get_elapsed_time()
    remaining = phase_manager.get_remaining_time()

    # Format as minutes and seconds
    elapsed_min = int(elapsed / 60)
    elapsed_sec = int(elapsed % 60)
    remaining_min = int(remaining / 60)
    remaining_sec = int(remaining % 60)

    message = f"{current_phase} STATUS: {elapsed_min}m {elapsed_sec}s elapsed, {remaining_min}m {remaining_sec}s remaining"

    # Add after-open mode specific information if applicable
    if phase_manager.after_open_mode:
        message += f" (AFTER-OPEN MODE)"

    # CRITICAL FIX: Add special debug logging when time is up
    if remaining_min == 0 and remaining_sec == 0:
        from orbscanner.utils.phase.manager import PhaseManager
        if current_phase == PhaseManager.FINALIZING:
            current_time = get_eastern_time()
            end_time = phase_manager.phase_end_times.get(current_phase)

            # Direct timestamp comparison for debugging
            if end_time:
                current_timestamp = current_time.timestamp()
                end_timestamp = end_time.timestamp()
                time_diff = current_timestamp - end_timestamp

                logging.warning(f"CRITICAL TIME DEBUG: Time is up for {current_phase}, should transition to MONITORING phase")
                logging.warning(f"CRITICAL TIME DEBUG: current_time={current_time}, end_time={end_time}")
                logging.warning(f"CRITICAL TIME DEBUG: current_timestamp={current_timestamp}, end_timestamp={end_timestamp}, diff={time_diff:.1f}s")
                logging.warning(f"CRITICAL TIME DEBUG: after_open_mode={phase_manager.after_open_mode}")
                logging.warning(f"CRITICAL TIME DEBUG: should_transition={phase_manager.should_transition(PhaseManager.FINALIZING, PhaseManager.MONITORING)}")
                logging.warning(f"CRITICAL TIME DEBUG: has_valid_data={phase_manager.has_valid_data(current_phase)}")

                # CRITICAL FIX: Force transition if time is up and we're in after_open_mode
                if phase_manager.after_open_mode and time_diff >= 0:
                    logging.critical(f"CRITICAL FIX: Forcing transition from {current_phase} to MONITORING_PHASE because time is up")

                    # Critical fix: Get the main app instance to properly handle the transition
                    from scanner_main import get_app_instance
                    app = get_app_instance()

                    if app:
                        # First, ensure candidates are preserved before transition
                        if hasattr(app, 'preserve_candidates_for_monitoring'):
                            try:
                                preserved_long_count, preserved_short_count = app.preserve_candidates_for_monitoring()
                                logging.critical(f"CRITICAL FIX: Preserved {preserved_long_count} long and {preserved_short_count} short candidates before transition")

                                # Update phase manager with the correct count of valid symbols
                                total_preserved = preserved_long_count + preserved_short_count
                                if total_preserved > 0:
                                    phase_manager.update_symbol_validation(PhaseManager.FINALIZING, total_preserved, total_preserved)
                                    logging.critical(f"CRITICAL FIX: Updated phase manager with valid symbols for FINALIZING: {total_preserved}")
                            except Exception as e:
                                logging.critical(f"CRITICAL ERROR: Failed to preserve candidates in phase logging: {e}")
                                import traceback
                                logging.critical(f"Traceback: {traceback.format_exc()}")

                        # Call proper transition method instead of direct transition
                        logging.critical("TRANSITION TIME CHECK: Calling app.transition_to_monitoring_phase()")
                        try:
                            app.transition_to_monitoring_phase()
                        except Exception as e:
                            logging.critical(f"CRITICAL ERROR: Failed to call transition_to_monitoring_phase: {e}")
                            import traceback
                            logging.critical(f"Traceback: {traceback.format_exc()}")
                            # Fall back to force transition if the proper method fails
                            logging.critical("CRITICAL FIX: Falling back to force transition after transition_to_monitoring_phase failed")
                            phase_manager.force_transition(PhaseManager.FINALIZING)
                    else:
                        # Only if app instance can't be found, use the fallback method
                        logging.critical("TRANSITION TIME CHECK: Could not get app instance, using fallback transition method")
                        
                        # CRITICAL FIX: Set force transition flag but DON'T transition yet
                        # This allows the main scanner loop to handle the transition properly
                        if hasattr(phase_manager, 'set_force_transition_flag'):
                            phase_manager.set_force_transition_flag(PhaseManager.FINALIZING, True)
                            logging.critical(f"CRITICAL FIX: Set force transition flag for {PhaseManager.FINALIZING} phase")
                            
                            # Check if force transition flag was set
                            if hasattr(phase_manager, 'is_force_transition_set'):
                                force_flag = phase_manager.is_force_transition_set(PhaseManager.FINALIZING)
                                logging.critical(f"CRITICAL FIX: Force transition flag after setting: {force_flag}")
                        else:
                            # Fallback if set_force_transition_flag doesn't exist
                            logging.critical("CRITICAL ERROR: Could not set force transition flag - set_force_transition_flag method not found")
                            
                        # DO NOT directly transition here - let the main scanner loop handle it
                        # This was causing the candidate preservation to be skipped

    log_to_phase(current_phase, message, force_flush=True)

def check_and_repair_phase_loggers():
    """
    Check all phase loggers and repair any that are not working properly.

    Returns:
        dict: Status of each phase logger (True if working, False if not)
    """
    status = {}

    # First, log the current state of all loggers
    logging.info("=== PHASE LOGGER DIAGNOSTIC ===")

    # Get level name without using deprecated getLevelName
    level_names = {
        logging.DEBUG: 'DEBUG',
        logging.INFO: 'INFO',
        logging.WARNING: 'WARNING',
        logging.ERROR: 'ERROR',
        logging.CRITICAL: 'CRITICAL'
    }
    root_level_name = level_names.get(logging.root.level, str(logging.root.level))

    logging.info(f"Root logger level: {root_level_name}")
    logging.info(f"Root logger handlers: {len(logging.root.handlers)}")

    for i, handler in enumerate(logging.root.handlers):
        handler_type = type(handler).__name__
        if isinstance(handler, logging.FileHandler) and hasattr(handler, 'baseFilename'):
            logging.info(f"  Root handler {i}: {handler_type} -> {handler.baseFilename}")
        else:
            logging.info(f"  Root handler {i}: {handler_type}")

    # Check each phase logger
    logging.info(f"Phase loggers: {len(PHASE_LOGGERS)}")

    for phase_name, phase_logger in list(PHASE_LOGGERS.items()):  # Use list() to avoid modification during iteration
        logging.info(f"  Checking phase logger: {phase_name}")
        phase_level_name = level_names.get(phase_logger.level, str(phase_logger.level))
        logging.info(f"    Level: {phase_level_name}")
        logging.info(f"    Propagate: {phase_logger.propagate}")
        logging.info(f"    Handlers: {len(phase_logger.handlers)}")

        # Log details of each handler
        for i, handler in enumerate(phase_logger.handlers):
            handler_type = type(handler).__name__
            if isinstance(handler, logging.FileHandler) and hasattr(handler, 'baseFilename'):
                logging.info(f"      Handler {i}: {handler_type} -> {handler.baseFilename}")
            else:
                logging.info(f"      Handler {i}: {handler_type}")

        # Check if the logger has handlers
        if not phase_logger.handlers:
            logging.warning(f"Phase logger {phase_name} has no handlers. Attempting to recreate.")
            status[phase_name] = False

            # Try to find the log file path from the phase name
            log_file = None
            for handler in logging.root.handlers:
                if isinstance(handler, logging.FileHandler) and hasattr(handler, 'baseFilename'):
                    if f"_{phase_name.lower().replace('_phase', '')}.log" in handler.baseFilename:
                        log_file = handler.baseFilename
                        break

            # If we found a log file path, recreate the logger
            if log_file:
                logging.warning(f"Recreating logger for {phase_name} with file {log_file}")
                try:
                    setup_phase_logger(phase_name, log_file)
                    # Test the logger with a message
                    PHASE_LOGGERS[phase_name].info(f"Logger recreated and tested at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                    # Force flush
                    for handler in PHASE_LOGGERS[phase_name].handlers:
                        if hasattr(handler, 'flush'):
                            handler.flush()
                    status[phase_name] = True
                    logging.info(f"Successfully recreated logger for {phase_name}")
                except Exception as e:
                    logging.error(f"Failed to recreate logger for {phase_name}: {e}")
                    status[phase_name] = False
            else:
                logging.error(f"Could not find log file for phase {phase_name}")
                status[phase_name] = False
        else:
            # Test the logger with a message
            try:
                phase_logger.info(f"Logger health check at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                # Ensure all handlers are flushed
                for handler in phase_logger.handlers:
                    if hasattr(handler, 'flush'):
                        handler.flush()
                status[phase_name] = True
                logging.info(f"Logger for {phase_name} is working properly")
            except Exception as e:
                logging.error(f"Error testing logger for {phase_name}: {e}")
                status[phase_name] = False

                # Try to recreate the logger
                try:
                    # Find the log file path
                    log_file = None
                    for handler in phase_logger.handlers:
                        if isinstance(handler, logging.FileHandler) and hasattr(handler, 'baseFilename'):
                            log_file = handler.baseFilename
                            break

                    if log_file:
                        logging.warning(f"Recreating logger for {phase_name} with file {log_file}")
                        setup_phase_logger(phase_name, log_file)
                        status[phase_name] = True
                except Exception as e2:
                    logging.error(f"Failed to recreate logger for {phase_name}: {e2}")
                    status[phase_name] = False

    logging.info("=== END PHASE LOGGER DIAGNOSTIC ===")
    return status
