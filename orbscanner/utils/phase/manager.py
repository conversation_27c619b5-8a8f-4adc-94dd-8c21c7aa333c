"""
Phase Manager for the 7-minute scanner.

This module provides a robust state machine for managing phase transitions
in the 7-minute scanner, ensuring proper timing and preventing phases from
getting stuck.
"""

import logging
import threading
import time
from datetime import timedelta
import inspect

# Import timezone utilities
from orbscanner.utils.time.timezone import (
    get_eastern_time,
    convert_to_eastern,
    get_time_diff_seconds,
    format_timestamp
)

# Import phase logging utilities
from orbscanner.utils.phase.logging import log_to_phase

# Import shared timing utilities
from orbscanner.utils.time.timing import (
    calculate_elapsed_time,
    calculate_remaining_time,
    format_time_remaining,
    validate_time_calculation
)

class PhaseManager:
    """
    Manages scanner phase transitions with explicit state tracking.

    This class provides a robust state machine for managing phase transitions
    in the 7-minute scanner, ensuring proper timing and preventing phases from
    getting stuck.
    """

    # Phase constants
    PRE_MARKET = "PRE_MARKET_PHASE"
    ORB = "ORB_PHASE"  # Opening Range Building phase (renamed from SCANNING)
    FINALIZING = "FINALIZING_PHASE"
    MONITORING = "MONITORING_PHASE"

    # Legacy constant for backward compatibility
    SCANNING = ORB

    # All phases in order
    ALL_PHASES = [PRE_MARKET, ORB, FINALIZING, MONITORING]

    # Valid phase transitions
    VALID_TRANSITIONS = {
        None: [PRE_MARKET, ORB],  # Initial state can go to PRE_MARKET or ORB (after-open mode)
        PRE_MARKET: [ORB],
        ORB: [FINALIZING],
        FINALIZING: [MONITORING],
        MONITORING: []  # No valid transitions from MONITORING
    }

    def __init__(self, log_base_path, after_open_mode=False):
        """
        Initialize the PhaseManager.

        Args:
            log_base_path (str): Base path for log files
            after_open_mode (bool, optional): Whether the scanner is running in after-open mode
        """
        self.current_phase = None
        self.phase_start_times = {}
        self.phase_end_times = {}
        
        # Initialize phase durations
        from scanner_main import (
            PRE_MARKET_PHASE_DURATION,
            ORB_PHASE_DURATION,
            FINALIZING_PHASE_DURATION
        )
        
        self.phase_durations = {
            self.PRE_MARKET: PRE_MARKET_PHASE_DURATION,
            self.ORB: ORB_PHASE_DURATION,
            self.FINALIZING: FINALIZING_PHASE_DURATION
        }
        
        self.after_open_mode = after_open_mode
        self.force_transition_flags = {
            self.PRE_MARKET: False,
            self.ORB: False,
            self.FINALIZING: False,
            self.MONITORING: False
        }
        # Flag to indicate monitoring should end
        self.monitoring_should_end = False

        # Symbol validation tracking
        self.valid_symbols = {
            self.PRE_MARKET: 0,
            self.ORB: 0,
            self.FINALIZING: 0,
            self.MONITORING: 0
        }
        self.total_symbols = {
            self.PRE_MARKET: 0,
            self.ORB: 0,
            self.FINALIZING: 0,
            self.MONITORING: 0
        }
        self.data_quality_flags = {
            self.PRE_MARKET: False,
            self.ORB: False,
            self.FINALIZING: False,
            self.MONITORING: False
        }
        self.transition_callbacks = {}

        # For tracking phase history
        self.phase_history = []

        logging.info(f"PhaseManager initialized with durations: {self._format_durations()}")
        if after_open_mode:
            logging.info("PhaseManager running in after-open mode")

    def _format_durations(self):
        """Format phase durations for logging."""
        return {phase: f"{duration.total_seconds()/60:.1f}m"
                for phase, duration in self.phase_durations.items()}

    def _safe_get(self, dictionary, key, default=None, log_error=False):
        """
        Safely get a value from a dictionary with optional error logging.

        Args:
            dictionary (dict): The dictionary to get the value from
            key: The key to look up
            default: The default value to return if key not found
            log_error (bool): Whether to log an error if key not found

        Returns:
            The value from the dictionary, or default if key not found
        """
        if key not in dictionary:
            if log_error:
                logging.warning(f"Key '{key}' not found in dictionary")
            return default
        return dictionary[key]

    def is_valid_transition(self, from_phase, to_phase):
        """
        Check if transition from one phase to another is valid.

        Args:
            from_phase (str): The phase transitioning from
            to_phase (str): The phase transitioning to

        Returns:
            bool: True if transition is valid, False otherwise
        """
        if from_phase not in self.VALID_TRANSITIONS:
            return False
        return to_phase in self.VALID_TRANSITIONS[from_phase]

    def register_transition_callback(self, from_phase, to_phase, callback):
        """
        Register a callback to be called when transitioning from one phase to another.

        Args:
            from_phase (str): The phase transitioning from
            to_phase (str): The phase transitioning to
            callback (callable): Function to call during transition
        """
        key = (from_phase, to_phase)
        self.transition_callbacks[key] = callback
        logging.debug(f"Registered transition callback for {from_phase} → {to_phase}")

    def start_phase(self, phase):
        """
        Start a new phase and record its start time.

        Args:
            phase (str): The phase to start

        Returns:
            datetime: The start time of the phase

        Raises:
            ValueError: If phase is not a valid phase or transition is invalid
        """
        if phase not in self.ALL_PHASES:
            raise ValueError(f"Invalid phase: {phase}")

        # Validate transition
        if not self.is_valid_transition(self.current_phase, phase):
            raise ValueError(f"Invalid phase transition: {self.current_phase} → {phase}")

        # Record the previous phase for history
        prev_phase = self.current_phase
        if prev_phase:
            self.phase_history.append({
                'phase': prev_phase,
                'start': self.phase_start_times.get(prev_phase),
                'end': get_eastern_time(),
                'duration': self.get_elapsed_time(prev_phase)
            })

        # Set the new phase
        self.current_phase = phase
        now = get_eastern_time()
        self.phase_start_times[phase] = now

        # Calculate and store the end time if we have a duration
        if phase in self.phase_durations:
            self.phase_end_times[phase] = now + self.phase_durations[phase]

        # Call transition callback if registered
        if prev_phase and (prev_phase, phase) in self.transition_callbacks:
            try:
                self.transition_callbacks[(prev_phase, phase)]()
            except Exception as e:
                logging.error(f"Error in transition callback {prev_phase} → {phase}: {e}")

        logging.info(f"Started phase: {phase} at {format_timestamp(now)}")
        if phase in self.phase_end_times:
            logging.info(f"Phase {phase} scheduled to end at {format_timestamp(self.phase_end_times[phase])}")

        return self.phase_start_times[phase]

    def get_phase_end_time(self, phase):
        """
        Get the scheduled end time for a phase.

        Args:
            phase (str): The phase to get the end time for

        Returns:
            datetime: The scheduled end time, or None if not set
        """
        return self._safe_get(self.phase_end_times, phase)

    def get_elapsed_time(self, phase=None):
        """
        Get elapsed time in seconds since phase started.

        Args:
            phase (str, optional): The phase to get elapsed time for.
                                  Defaults to current phase.

        Returns:
            float: Elapsed time in seconds, or 0 if phase not started
        """
        if phase is None:
            phase = self.current_phase
            if phase is None:
                logging.warning("Cannot get elapsed time: No current phase")
                return 0

        start_time = self._safe_get(self.phase_start_times, phase, log_error=True)

        # Use shared timing utility
        elapsed = calculate_elapsed_time(start_time)

        # Validate the result
        if not validate_time_calculation(elapsed):
            logging.warning(f"Invalid elapsed time for phase '{phase}': {elapsed}s")
            return 0

        return elapsed

    def get_remaining_time(self, phase=None):
        """
        Get remaining time in seconds for a phase.

        Args:
            phase (str, optional): The phase to get remaining time for.
                                  Defaults to current phase.

        Returns:
            float: Remaining time in seconds, or 0 if phase not started or no end time
        """
        if phase is None:
            phase = self.current_phase
            if phase is None:
                logging.warning("Cannot get remaining time: No current phase")
                return 0

        end_time = self._safe_get(self.phase_end_times, phase, log_error=True)

        # Use shared timing utility
        return calculate_remaining_time(end_time)

    def should_transition(self, from_phase, to_phase):
        """
        Check if it's time to transition from one phase to another.

        Args:
            from_phase (str): The phase to transition from
            to_phase (str): The phase to transition to

        Returns:
            bool: True if transition should occur, False otherwise
        """
        # If current phase is not from_phase, don't transition
        if self.current_phase != from_phase:
            return False

        # Validate that this would be a valid transition
        if not self.is_valid_transition(from_phase, to_phase):
            logging.warning(f"Invalid phase transition requested: {from_phase} → {to_phase}")
            return False

        # If force transition flag is set, always transition
        force_flag = self._safe_get(self.force_transition_flags, from_phase, False)
        if force_flag:
            logging.critical(f"CRITICAL FIX: Forced transition from {from_phase} to {to_phase} due to timeout")
            # Reset the flag
            self.force_transition_flags[from_phase] = False
            return True

        # CRITICAL FIX: Double-check force transition flag
        if from_phase == self.FINALIZING and self.after_open_mode:
            logging.warning(f"CRITICAL FIX: Double-checking force transition flag for {from_phase} in after_open_mode")
            # Check if we've been in this phase for too long
            if self.get_elapsed_time(from_phase) > 240:  # 4 minutes
                logging.critical(f"CRITICAL FIX: {from_phase} has been active for too long. Forcing transition.")
                return True

        # If phase hasn't started, don't transition
        if self._safe_get(self.phase_start_times, from_phase) is None:
            return False

        # Check if we've reached the end time for this phase
        end_time = self._safe_get(self.phase_end_times, from_phase)
        if not end_time:
            return False

        try:
            current_time = get_eastern_time()

            # CRITICAL FIX: Ensure we're comparing datetime objects correctly
            # Convert to timestamps for direct numeric comparison
            current_timestamp = current_time.timestamp()
            end_timestamp = end_time.timestamp()
            time_to_transition = current_timestamp >= end_timestamp

            # Calculate remaining time for logging and decision making
            time_remaining = (end_time - current_time).total_seconds()
            minutes_remaining = int(time_remaining / 60)
            seconds_remaining = int(time_remaining % 60)

            # CRITICAL FIX: Add enhanced debugging for time comparison
            logging.debug(f"TRANSITION TIME DEBUG: current_time={current_time}, end_time={end_time}")
            logging.debug(f"TRANSITION TIME DEBUG: current_timestamp={current_timestamp}, end_timestamp={end_timestamp}")
            logging.debug(f"TRANSITION TIME DEBUG: time_to_transition={time_to_transition}, time_remaining={time_remaining:.1f}s")

            # CRITICAL FIX: Force transition in after_open_mode when time is up or almost up
            if self.after_open_mode and from_phase == self.FINALIZING and time_remaining <= 5:  # 5 seconds buffer
                logging.warning(f"AFTER_OPEN_MODE: Forcing transition from {from_phase} to {to_phase} because time is up (remaining: {time_remaining:.1f}s)")
                return True

            # CRITICAL FIX: Add enhanced logging to explain why transition is not happening
            if not time_to_transition:
                logging.info(f"Not transitioning from {from_phase} to {to_phase} yet: {minutes_remaining}m {seconds_remaining}s remaining")
                logging.info(f"Current time: {current_time.strftime('%H:%M:%S')}, End time: {end_time.strftime('%H:%M:%S')}")
                return False

            # Only transition if we have valid data (at least one symbol)
            # Skip this check for PRE_MARKET to ORB transition
            if from_phase != self.PRE_MARKET:
                # CRITICAL FIX: In after_open_mode, always consider data valid during FINALIZING to allow transition
                if self.after_open_mode and from_phase == self.FINALIZING:
                    logging.info(f"AFTER_OPEN_MODE: Overriding valid data check in should_transition for {from_phase}, considering it True.")
                    has_valid_data = True
                else:
                    has_valid_data = self.has_valid_data(from_phase)

                valid_count = self.valid_symbols.get(from_phase, 0)
                total_count = self.total_symbols.get(from_phase, 0)

                if not has_valid_data:
                    logging.warning(f"Cannot transition from {from_phase} to {to_phase}: No valid data available")
                    logging.warning(f"Valid symbols count: {valid_count}/{total_count}")
                    logging.warning("At least one symbol must have valid data to proceed")
                    # Don't transition yet, keep trying to collect data
                    return False
                else:
                    logging.info(f"Valid data check passed: {valid_count}/{total_count} valid symbols")

            return True
        except Exception as e:
            logging.error(f"Error checking transition time: {e}")
            return False

    def transition_to(self, to_phase, reason=""):
        """
        Transition directly to a new phase.

        Args:
            to_phase (str): The phase to transition to
            reason (str, optional): Reason for the transition

        Returns:
            bool: True if transition was successful, False otherwise
        """
        from_phase = self.current_phase

        # Validate the transition
        if not self.is_valid_transition(from_phase, to_phase):
            logging.error(f"Invalid phase transition: {from_phase} → {to_phase}")
            return False

        # Log the transition with reason
        if reason:
            logging.critical(f"CRITICAL FIX: Explicit transition from {from_phase} to {to_phase}: {reason}")
        else:
            logging.critical(f"CRITICAL FIX: Explicit transition from {from_phase} to {to_phase}")

        # CDG_TRACKING before transition
        from scanner_main import get_app_instance
        app = get_app_instance()
        logging.critical(f"CDG_TRACKING [PHASEMANAGER_TRANSITION_BEFORE_{__name__}_L{inspect.currentframe().f_lineno}]: Before, longCandidates={{len(app.longCandidates) if app and hasattr(app, 'longCandidates') else 'N/A'}}, shortCandidates={{len(app.shortCandidates) if app and hasattr(app, 'shortCandidates') else 'N/A'}}")

        # CRITICAL FIX: Special handling for FINALIZING to MONITORING transition
        if from_phase == self.FINALIZING and to_phase == self.MONITORING:
            # Try to get the app instance to preserve candidates
            try:
                if app is not None:
                    # Log the state of the app's candidate lists before preservation
                    if hasattr(app, 'longCandidates'):
                        long_count = len(getattr(app, 'longCandidates', []))
                        logging.critical(f"PHASE TRANSITION: Current longCandidates count: {long_count}")
                        log_to_phase(self.FINALIZING, f"PHASE TRANSITION: Current longCandidates count: {long_count}")

                        # Log sample of symbols
                        if app.longCandidates:
                            long_sample = [c.get('symbol', 'unknown') if isinstance(c, dict) else c for c in app.longCandidates[:5]]
                            logging.critical(f"PHASE TRANSITION: Long candidates sample: {long_sample}")
                            log_to_phase(self.FINALIZING, f"PHASE TRANSITION: Long candidates sample: {long_sample}")
                    else:
                        logging.critical("PHASE TRANSITION: No longCandidates attribute found")
                        log_to_phase(self.FINALIZING, "PHASE TRANSITION: No longCandidates attribute found")

                    if hasattr(app, 'shortCandidates'):
                        short_count = len(getattr(app, 'shortCandidates', []))
                        logging.critical(f"PHASE TRANSITION: Current shortCandidates count: {short_count}")
                        log_to_phase(self.FINALIZING, f"PHASE TRANSITION: Current shortCandidates count: {short_count}")

                        # Log sample of symbols
                        if app.shortCandidates:
                            short_sample = [c.get('symbol', 'unknown') if isinstance(c, dict) else c for c in app.shortCandidates[:5]]
                            logging.critical(f"PHASE TRANSITION: Short candidates sample: {short_sample}")
                            log_to_phase(self.FINALIZING, f"PHASE TRANSITION: Short candidates sample: {short_sample}")
                    else:
                        logging.critical("PHASE TRANSITION: No shortCandidates attribute found")
                        log_to_phase(self.FINALIZING, "PHASE TRANSITION: No shortCandidates attribute found")

                    # Check if we need to preserve candidates
                    if hasattr(app, '_preserved_long_candidates') and hasattr(app, '_preserved_short_candidates'):
                        long_count = len(getattr(app, '_preserved_long_candidates', []))
                        short_count = len(getattr(app, '_preserved_short_candidates', []))
                        total_count = long_count + short_count

                        if total_count == 0:
                            # No candidates preserved yet, try to preserve them now
                            logging.critical("CRITICAL FIX: No candidates preserved yet, attempting preservation before transition")
                            log_to_phase(self.FINALIZING, "CRITICAL FIX: No candidates preserved yet, attempting preservation before transition")

                            if hasattr(app, 'preserve_candidates_for_monitoring'):
                                logging.critical("PHASE TRANSITION: About to call preserve_candidates_for_monitoring method")
                                log_to_phase(self.FINALIZING, "PHASE TRANSITION: About to call preserve_candidates_for_monitoring method")

                                try:
                                    preserved_long_count, preserved_short_count = app.preserve_candidates_for_monitoring()
                                    logging.critical(f"CRITICAL FIX: Last-chance preservation saved {preserved_long_count} long and {preserved_short_count} short candidates")
                                    log_to_phase(self.FINALIZING, f"CRITICAL FIX: Last-chance preservation saved {preserved_long_count} long and {preserved_short_count} short candidates")

                                    # Update valid symbols count
                                    total_preserved = preserved_long_count + preserved_short_count
                                    if total_preserved > 0:
                                        self.update_symbol_validation(self.FINALIZING, total_preserved, total_preserved)
                                        logging.critical(f"CRITICAL FIX: Updated valid symbols for FINALIZING: {total_preserved}")
                                        log_to_phase(self.FINALIZING, f"CRITICAL FIX: Updated valid symbols for FINALIZING: {total_preserved}")
                                    else:
                                        logging.critical("CRITICAL WARNING: No candidates were preserved during last-chance preservation")
                                        log_to_phase(self.FINALIZING, "CRITICAL WARNING: No candidates were preserved during last-chance preservation", level=logging.WARNING)
                                except Exception as e:
                                    logging.critical(f"CRITICAL ERROR: Failed during last-chance candidate preservation: {e}")
                                    log_to_phase(self.FINALIZING, f"CRITICAL ERROR: Failed during last-chance candidate preservation: {e}", level=logging.ERROR)

                                    import traceback
                                    stack_trace = traceback.format_exc()
                                    logging.critical(f"Traceback: {stack_trace}")
                                    log_to_phase(self.FINALIZING, f"Traceback: {stack_trace}", level=logging.ERROR)
                            else:
                                logging.critical("CRITICAL ERROR: App instance does not have preserve_candidates_for_monitoring method")
                                log_to_phase(self.FINALIZING, "CRITICAL ERROR: App instance does not have preserve_candidates_for_monitoring method", level=logging.ERROR)
                        else:
                            logging.critical(f"CRITICAL FIX: Found {total_count} already preserved candidates ({long_count} long, {short_count} short)")
                            log_to_phase(self.FINALIZING, f"CRITICAL FIX: Found {total_count} already preserved candidates ({long_count} long, {short_count} short)")

                            # Log sample of preserved candidates
                            if long_count > 0:
                                preserved_long_sample = [c.get('symbol', 'unknown') if isinstance(c, dict) else c for c in app._preserved_long_candidates[:5]]
                                logging.critical(f"PHASE TRANSITION: Preserved long candidates sample: {preserved_long_sample}")
                                log_to_phase(self.FINALIZING, f"PHASE TRANSITION: Preserved long candidates sample: {preserved_long_sample}")

                            if short_count > 0:
                                preserved_short_sample = [c.get('symbol', 'unknown') if isinstance(c, dict) else c for c in app._preserved_short_candidates[:5]]
                                logging.critical(f"PHASE TRANSITION: Preserved short candidates sample: {preserved_short_sample}")
                                log_to_phase(self.FINALIZING, f"PHASE TRANSITION: Preserved short candidates sample: {preserved_short_sample}")
                    else:
                        logging.critical("CRITICAL ERROR: App instance does not have _preserved_long_candidates or _preserved_short_candidates attributes")
                        log_to_phase(self.FINALIZING, "CRITICAL ERROR: App instance does not have _preserved_long_candidates or _preserved_short_candidates attributes", level=logging.ERROR)
                else:
                    logging.critical("CRITICAL ERROR: Could not get app instance for candidate preservation check")
                    log_to_phase(self.FINALIZING, "CRITICAL ERROR: Could not get app instance for candidate preservation check", level=logging.ERROR)
            except Exception as e:
                logging.critical(f"CRITICAL ERROR: Exception during candidate preservation check: {e}")
                log_to_phase(self.FINALIZING, f"CRITICAL ERROR: Exception during candidate preservation check: {e}", level=logging.ERROR)

                import traceback
                stack_trace = traceback.format_exc()
                logging.critical(f"Traceback: {stack_trace}")
                log_to_phase(self.FINALIZING, f"Traceback: {stack_trace}", level=logging.ERROR)

        # Start the new phase
        try:
            self.start_phase(to_phase)
            # CDG_TRACKING after transition
            app = get_app_instance()
            logging.critical(f"CDG_TRACKING [PHASEMANAGER_TRANSITION_AFTER_{__name__}_L{inspect.currentframe().f_lineno}]: After, longCandidates={{len(app.longCandidates) if app and hasattr(app, 'longCandidates') else 'N/A'}}, shortCandidates={{len(app.shortCandidates) if app and hasattr(app, 'shortCandidates') else 'N/A'}}")
            return True
        except Exception as e:
            logging.error(f"Error during explicit transition to {to_phase}: {e}")
            return False

    def force_transition(self, phase):
        """
        Force transition from the specified phase.

        Args:
            phase (str): The phase to force transition from
        """
        if phase in self.force_transition_flags:
            self.force_transition_flags[phase] = True
            logging.warning(f"Force transition flag set for {phase}")

            # Special handling for MONITORING phase since it has no next phase
            if phase == self.MONITORING:
                logging.warning("Forcing exit from MONITORING phase")
                # Signal to the main loop that monitoring should end
                self.monitoring_should_end = True
        else:
            logging.error(f"Cannot force transition for unknown phase: {phase}")

    def is_force_transition_set(self, phase):
        """
        Check if force transition flag is set for the specified phase.

        Args:
            phase (str): The phase to check

        Returns:
            bool: True if force transition flag is set, False otherwise
        """
        if phase in self.force_transition_flags:
            flag_set = self.force_transition_flags[phase]
            logging.debug(f"Force transition flag for {phase}: {flag_set}")
            return flag_set
        else:
            logging.error(f"Cannot check force transition flag for unknown phase: {phase}")
            return False

    def is_monitoring_done(self):
        """
        Check if monitoring phase should end.

        Returns:
            bool: True if monitoring should end, False otherwise
        """
        return self.monitoring_should_end

    def update_symbol_validation(self, phase, valid_count, total_count):
        """
        Update symbol validation tracking for a phase.

        Args:
            phase (str): The phase to update
            valid_count (int): Number of symbols with valid data
            total_count (int): Total number of symbols processed

        Returns:
            bool: True if at least one symbol has valid data, False otherwise
        """
        if phase not in self.ALL_PHASES:
            logging.warning(f"Cannot update symbol validation for unknown phase: {phase}")
            return False

        self.valid_symbols[phase] = valid_count
        self.total_symbols[phase] = total_count

        # Update data quality flag - at least one symbol must have valid data
        has_valid_data = valid_count > 0
        self.data_quality_flags[phase] = has_valid_data

        # Log the update
        if has_valid_data:
            logging.info(f"Phase {phase} has {valid_count}/{total_count} symbols with valid data")
        else:
            logging.warning(f"Phase {phase} has NO symbols with valid data (0/{total_count})")

        return has_valid_data

    def set_valid_symbols_for_phase(self, phase, count):
        """
        Set the valid symbols count for a specific phase.
        This is useful when explicitly setting the count for a phase.

        Args:
            phase (str): The phase to update
            count (int): Number of valid symbols

        Returns:
            bool: True if update was successful, False otherwise
        """
        if phase not in self.ALL_PHASES:
            logging.warning(f"Cannot set valid symbols for unknown phase: {phase}")
            return False

        old_count = self.valid_symbols.get(phase, 0)
        self.valid_symbols[phase] = count

        # Update data quality flag - at least one symbol must have valid data
        has_valid_data = count > 0
        self.data_quality_flags[phase] = has_valid_data

        # Log the update
        logging.critical(f"CRITICAL FIX: Set valid symbols for {phase}: {count} (was {old_count})")
        logging.critical(f"CRITICAL FIX: Data quality flag for {phase}: {self.data_quality_flags[phase]}")

        return True

    def has_valid_data(self, phase=None):
        """
        Check if a phase has at least one symbol with valid data.

        Args:
            phase (str, optional): The phase to check. Defaults to current phase.

        Returns:
            bool: True if the phase has at least one symbol with valid data, False otherwise
        """
        if phase is None:
            phase = self.current_phase

        if phase not in self.ALL_PHASES:
            logging.warning(f"Cannot check data quality for unknown phase: {phase}")
            return False

        # CRITICAL FIX: In after_open_mode, always consider data valid during FINALIZING to allow transition
        if self.after_open_mode and phase == self.FINALIZING:
            logging.info(f"AFTER_OPEN_MODE: Overriding valid data check for {phase}, considering it True.")
            return True

        return self.data_quality_flags.get(phase, False)

    def get_phase_duration(self, phase):
        """
        Get the configured duration for a phase.

        Args:
            phase (str): The phase to get duration for

        Returns:
            timedelta: The configured duration, or None if not set
        """
        return self._safe_get(self.phase_durations, phase)

    def get_actual_duration(self, phase):
        """
        Get the actual duration of a completed phase.

        Args:
            phase (str): The phase to get actual duration for

        Returns:
            float: Actual duration in seconds, or None if phase not completed
        """
        for entry in reversed(self.phase_history):
            if entry['phase'] == phase:
                return entry['duration']
        return None

    def recover_from_error(self):
        """
        Attempt to recover from error states.

        Returns:
            bool: True if recovery was needed and performed, False otherwise
        """
        logging.warning("Attempting to recover from error state")

        # Check for inconsistent state
        if self.current_phase and self.current_phase not in self.phase_start_times:
            logging.error(f"Inconsistent state: Current phase '{self.current_phase}' has no start time")
            # Reset to a known good state
            self._reset_to_safe_state()
            return True

        # Check for invalid phase
        if self.current_phase and self.current_phase not in self.ALL_PHASES:
            logging.error(f"Invalid current phase: {self.current_phase}")
            self._reset_to_safe_state()
            return True

        return False

    def _reset_to_safe_state(self):
        """Reset to a known good state after error."""
        logging.warning("Resetting phase manager to safe state")
        # Store current phase for logging
        old_phase = self.current_phase

        # If in MONITORING, stay there as it's the terminal state
        if self.current_phase == self.MONITORING:
            # Just ensure it has proper timestamps
            if self.current_phase not in self.phase_start_times:
                self.phase_start_times[self.current_phase] = get_eastern_time()
        else:
            # Otherwise, transition to MONITORING as the safest option
            self.current_phase = self.MONITORING
            self.phase_start_times[self.MONITORING] = get_eastern_time()

        logging.info(f"Reset phase from '{old_phase}' to '{self.current_phase}'")

    def dump_state(self):
        """
        Dump the current state of the phase manager for debugging.
        """
        logging.info("PHASE MANAGER STATE DUMP:")
        logging.info(f"Current phase: {self.current_phase}")
        logging.info(f"Valid symbols by phase: {self.valid_symbols}")
        logging.info(f"Total symbols by phase: {self.total_symbols}")
        logging.info(f"Data quality flags: {self.data_quality_flags}")
        logging.info(f"Phase start times: {self.phase_start_times}")
        logging.info(f"Phase end times: {self.phase_end_times}")
        logging.info(f"Force transition flags: {self.force_transition_flags}")

        # Log phase history
        if self.phase_history:
            logging.info("Phase history:")
            for entry in self.phase_history:
                phase = entry.get('phase', 'UNKNOWN')
                start = entry.get('start')
                end = entry.get('end')
                duration = entry.get('duration', 0)
                logging.info(f"  - {phase}: {format_timestamp(start) if start else 'N/A'} to {format_timestamp(end) if end else 'N/A'} ({duration:.1f}s)")

        return {
            'current_phase': self.current_phase,
            'valid_symbols': self.valid_symbols,
            'total_symbols': self.total_symbols,
            'data_quality_flags': self.data_quality_flags
        }

    def dump_state(self):
        """
        Dump the current state of the phase manager for debugging purposes.
        """
        logging.debug("=== PHASE MANAGER STATE DUMP ===")
        logging.debug(f"Current phase: {self.current_phase}")

        # Format phase times
        phase_times = {}
        for phase in self.ALL_PHASES:
            start_time = self.phase_start_times.get(phase)
            end_time = self.phase_end_times.get(phase)
            start_str = format_timestamp(start_time) if start_time else "Not started"
            end_str = format_timestamp(end_time) if end_time else "No end time"
            phase_times[phase] = {"start": start_str, "end": end_str}

        logging.debug(f"Phase times: {phase_times}")
        logging.debug(f"Valid symbols: {self.valid_symbols}")
        logging.debug(f"Total symbols: {self.total_symbols}")
        logging.debug(f"Data quality flags: {self.data_quality_flags}")
        logging.debug(f"Force transition flags: {self.force_transition_flags}")
        logging.debug("=== END STATE DUMP ===")

    def dump_state(self):
        """
        Dump the current state of the phase manager for debugging.
        """
        logging.info("PHASE MANAGER STATE DEBUG:")
        logging.info(f"Current phase: {self.current_phase}")
        logging.info(f"After open mode: {self.after_open_mode}")
        logging.info(f"Phase manager valid_symbols: {self.valid_symbols}")
        logging.info(f"Phase manager data_quality_flags: {self.data_quality_flags}")
        logging.info(f"Phase manager total_symbols: {self.total_symbols}")

        # Log phase timing information
        if self.current_phase:
            start_time = self.phase_start_times.get(self.current_phase)
            end_time = self.phase_end_times.get(self.current_phase)
            if start_time and end_time:
                current_time = get_eastern_time()
                elapsed = (current_time - start_time).total_seconds()
                remaining = (end_time - current_time).total_seconds()
                logging.info(f"Phase timing: elapsed={elapsed:.1f}s, remaining={remaining:.1f}s")
                logging.info(f"Start time: {format_timestamp(start_time)}, End time: {format_timestamp(end_time)}")

    def health_check(self):
        """
        Validate phase manager state and fix if needed.

        Returns:
            bool: True if state is valid, False if issues were found and fixed
        """
        issues_found = False

        # Check for current phase validity
        if self.current_phase and self.current_phase not in self.ALL_PHASES:
            logging.error(f"Invalid current phase: {self.current_phase}")
            issues_found = True

        # Check for timestamp consistency
        if self.current_phase and self.current_phase not in self.phase_start_times:
            logging.error(f"Missing start time for current phase: {self.current_phase}")
            issues_found = True

        # Check for end time consistency for phases with durations
        if self.current_phase and self.current_phase in self.phase_durations:
            if self.current_phase not in self.phase_end_times:
                logging.error(f"Missing end time for current phase: {self.current_phase}")
                issues_found = True

        # If issues found, attempt recovery
        if issues_found:
            self.recover_from_error()
            return False

        return True
