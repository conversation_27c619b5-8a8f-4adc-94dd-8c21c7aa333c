"""
Phase Watchdog for the 7-minute scanner.

This module provides a watchdog timer that monitors phase durations
and forces transitions if phases exceed their maximum allowed time.
It also includes a fail-safe mechanism to exit the test when no valid data
is available in critical phases.
"""

import logging
import threading
import sys
import time
import os
import signal

from orbscanner.utils.phase.manager import PhaseManager
from orbscanner.utils.time.timezone import get_eastern_time, format_timestamp

class PhaseWatchdog:
    """
    Monitors phase durations and forces transitions if phases exceed maximum time.

    This class runs a background thread that monitors the duration of each phase
    and forces a transition if a phase exceeds its maximum allowed time. It also
    issues warnings when phases approach their maximum duration.
    """

    def __init__(self, phase_manager, max_durations=None, enable_fail_safe=True):
        """
        Initialize the PhaseWatchdog.

        Args:
            phase_manager (PhaseManager): The phase manager to monitor
            max_durations (dict, optional): Dictionary mapping phase names to maximum
                                           durations in seconds. If None, default
                                           values will be used.
            enable_fail_safe (bool, optional): Whether to enable the fail-safe mechanism
                                              that exits the test when no valid data is
                                              available in critical phases. Default is True.
        """
        self.phase_manager = phase_manager
        # CRITICAL FIX: Always enable fail-safe for ORB phase
        self.enable_fail_safe = enable_fail_safe

        # Reference to the main app for calling check_valid_opening_ranges
        self.app = None

        # Default maximum durations (in seconds)
        self.max_durations = {
            PhaseManager.PRE_MARKET: 15 * 60,  # 15 minutes
            # CRITICAL FIX: Reduce ORB phase timeout to 8 minutes (was 10.5)
            PhaseManager.ORB: 8 * 60,  # 8 minutes (Opening Range Building phase)
            PhaseManager.FINALIZING: 4 * 60  # 4 minutes (was 4.5)
        }

        # Override with provided durations if any
        if max_durations:
            self.max_durations.update(max_durations)

        # Shorter timeouts for after-open mode
        if phase_manager.after_open_mode:
            self.max_durations[PhaseManager.PRE_MARKET] = 5 * 60  # 5 minutes

        # Warning thresholds (percentage of max duration)
        self.warning_thresholds = {
            70: {},  # 70% warnings issued for each phase (was 80%)
            90: {}   # 90% warnings issued for each phase (was 95%)
        }

        # Initialize warning flags for each phase
        for threshold in self.warning_thresholds:
            for phase in self.max_durations:
                self.warning_thresholds[threshold][phase] = False

        # Critical phases that require valid data
        self.critical_phases = [PhaseManager.ORB, PhaseManager.FINALIZING]

        # No-data warning counter for each phase
        self.no_data_warnings = {phase: 0 for phase in self.max_durations}

        # CRITICAL FIX: Reduce max warnings before fail-safe exit
        # Maximum number of no-data warnings before triggering fail-safe exit
        self.max_no_data_warnings = 1  # Was 3

        # CRITICAL FIX: Add timeout multiplier for extended duration
        self.extended_timeout_multiplier = 1.2  # Was 1.5

        self.active = True
        self.thread = threading.Thread(
            target=self._monitor_phases,
            name="PhaseWatchdog",
            daemon=True
        )

        logging.info(f"PhaseWatchdog initialized with max durations: {self._format_durations()}")
        if self.enable_fail_safe:
            logging.info("Fail-safe mechanism ENABLED: Test will exit if no valid data in critical phases")
            logging.info(f"Fail-safe will trigger after {self.max_no_data_warnings} warning(s)")
        else:
            logging.info("Fail-safe mechanism DISABLED: Test will continue even with no valid data")

        # Start the watchdog thread
        self.thread.start()

    def _format_durations(self):
        """Format max durations for logging."""
        return {phase: f"{duration/60:.1f}m"
                for phase, duration in self.max_durations.items()}

    def _monitor_phases(self):
        """Background thread that monitors phase durations."""
        logging.info("PhaseWatchdog thread started")

        while self.active:
            try:
                current_phase = self.phase_manager.current_phase

                # Skip if no current phase or in monitoring phase
                if not current_phase or current_phase == PhaseManager.MONITORING:
                    time.sleep(5)
                    continue

                # Skip if phase doesn't have a max duration
                if current_phase not in self.max_durations:
                    time.sleep(5)
                    continue

                # Get elapsed time and max duration
                elapsed = self.phase_manager.get_elapsed_time()
                max_duration = self.max_durations.get(current_phase, 0)

                if max_duration > 0:
                    percentage = (elapsed / max_duration) * 100

                    # Issue warnings at threshold percentages
                    for threshold, warned_phases in self.warning_thresholds.items():
                        if percentage >= threshold and not warned_phases.get(current_phase, False):
                            logging.warning(
                                f"WATCHDOG WARNING: {current_phase} at {percentage:.1f}% of maximum "
                                f"duration ({elapsed:.1f}s / {max_duration:.1f}s)"
                            )
                            self.warning_thresholds[threshold][current_phase] = True

                            # CRITICAL FIX: For ORB phase, check valid ranges at warning thresholds
                            if current_phase == PhaseManager.ORB and self.app is not None:
                                try:
                                    valid_count, total_count = self.app.check_valid_opening_ranges()
                                    logging.warning(
                                        f"WATCHDOG CHECK: {current_phase} has {valid_count}/{total_count} "
                                        f"valid opening ranges at {percentage:.1f}% of max duration"
                                    )

                                    # Update phase manager with validation data
                                    self.phase_manager.update_symbol_validation(current_phase, valid_count, total_count)

                                    # If no valid ranges at 90% threshold, trigger fail-safe immediately
                                    if threshold >= 90 and valid_count == 0 and self.enable_fail_safe:
                                        logging.critical(
                                            f"WATCHDOG CRITICAL: No valid opening ranges at {percentage:.1f}% "
                                            f"of maximum duration. Triggering fail-safe exit."
                                        )
                                        self._trigger_fail_safe_exit(current_phase, elapsed,
                                                                    reason="No valid opening ranges at warning threshold")
                                        return  # Exit the monitor thread
                                except Exception as e:
                                    logging.error(f"Error checking valid opening ranges: {e}")

                    # Force transition if we exceed max duration
                    if elapsed >= max_duration:
                        # CRITICAL FIX: For ORB phase, always check valid ranges before forcing transition
                        if current_phase == PhaseManager.ORB and self.app is not None:
                            try:
                                valid_count, total_count = self.app.check_valid_opening_ranges()
                                logging.critical(
                                    f"WATCHDOG TIMEOUT CHECK: {current_phase} has {valid_count}/{total_count} "
                                    f"valid opening ranges at timeout"
                                )

                                # Update phase manager with validation data
                                self.phase_manager.update_symbol_validation(current_phase, valid_count, total_count)

                                # If no valid ranges at timeout, trigger fail-safe immediately
                                if valid_count == 0 and self.enable_fail_safe:
                                    logging.critical(
                                        f"WATCHDOG CRITICAL: No valid opening ranges at timeout. "
                                        f"Triggering fail-safe exit."
                                    )
                                    self._trigger_fail_safe_exit(current_phase, elapsed,
                                                                reason="No valid opening ranges at timeout")
                                    return  # Exit the monitor thread
                            except Exception as e:
                                logging.error(f"Error checking valid opening ranges at timeout: {e}")

                        # Check if we have valid data
                        has_valid_data = self.phase_manager.has_valid_data(current_phase)

                        if has_valid_data:
                            logging.critical(
                                f"WATCHDOG TIMEOUT: {current_phase} exceeded maximum duration of "
                                f"{max_duration:.1f}s ({max_duration/60:.1f}m). Setting force transition flag."
                            )
                            # CRITICAL FIX: Use set_force_transition_flag instead of directly transitioning
                            # This allows the main scanner loop to handle the transition properly
                            if hasattr(self.phase_manager, 'set_force_transition_flag'):
                                self.phase_manager.set_force_transition_flag(current_phase, True)
                                
                                # Check if force transition flag was set
                                if hasattr(self.phase_manager, 'is_force_transition_set'):
                                    force_flag = self.phase_manager.is_force_transition_set(current_phase)
                                    logging.critical(f"CRITICAL FIX: Force transition flag after setting in watchdog: {force_flag}")
                            else:
                                # Fallback to force_transition if set_force_transition_flag doesn't exist
                                logging.critical("CRITICAL WARNING: set_force_transition_flag not found, falling back to force_transition")
                                self.phase_manager.force_transition(current_phase)

                            # CRITICAL FIX: Directly start the next phase if we're in FINALIZING
                            if current_phase == self.phase_manager.FINALIZING:
                                try:
                                    # CRITICAL FIX: Try to get the app instance from global registry first
                                    from scanner_main import get_app_instance
                                    app_instance = get_app_instance()

                                    # If we have a global app instance, use it for proper transition
                                    if app_instance is not None:
                                        logging.critical("CRITICAL FIX: Using global app instance for proper transition")
                                        try:
                                            # Call the proper transition method
                                            app_instance.transition_to_monitoring_phase()
                                            logging.critical("CRITICAL FIX: Successfully called transition_to_monitoring_phase via global app instance")
                                            continue  # Skip the rest of the watchdog logic since we've handled the transition
                                        except Exception as e:
                                            logging.critical(f"CRITICAL ERROR: Failed to call transition_to_monitoring_phase via global app instance: {e}")
                                            import traceback
                                            logging.critical(f"Traceback: {traceback.format_exc()}")

                                    # Fall back to local app instance if global one failed
                                    if hasattr(self, 'app') and self.app is not None:
                                        # CRITICAL FIX: Preserve candidate data before forced transition
                                        if hasattr(self.app, 'preserve_candidates_for_monitoring'):
                                            try:
                                                preserved_long_count, preserved_short_count = self.app.preserve_candidates_for_monitoring()
                                                logging.critical(f"CRITICAL FIX: Watchdog preserved {preserved_long_count} long and {preserved_short_count} short candidates before forced transition")

                                                # If we have candidates, try to call the proper transition method
                                                if preserved_long_count + preserved_short_count > 0:
                                                    if hasattr(self.app, 'transition_to_monitoring_phase'):
                                                        try:
                                                            self.app.transition_to_monitoring_phase()
                                                            logging.critical("CRITICAL FIX: Successfully called transition_to_monitoring_phase after preserving candidates")
                                                            continue  # Skip the rest of the watchdog logic
                                                        except Exception as e:
                                                            logging.critical(f"CRITICAL ERROR: Failed to call transition_to_monitoring_phase: {e}")
                                            except Exception as e:
                                                logging.critical(f"CRITICAL ERROR: Failed to preserve candidates in watchdog: {e}")
                                                import traceback
                                                logging.critical(f"Traceback: {traceback.format_exc()}")
                                    else:
                                        logging.critical("CRITICAL ERROR: No app instance available for candidate preservation")

                                    # Only do this if we have the start_phase method
                                    if hasattr(self.phase_manager, 'start_phase'):
                                        logging.critical(f"CRITICAL FIX: Directly starting MONITORING_PHASE from watchdog")
                                        self.phase_manager.start_phase(self.phase_manager.MONITORING)
                                except Exception as e:
                                    logging.error(f"CRITICAL ERROR: Failed to directly start MONITORING phase from watchdog: {e}")
                        else:
                            # If we don't have valid data, check if we should exit or give extra time
                            if self.enable_fail_safe and current_phase in self.critical_phases:
                                # Increment the no-data warning counter for this phase
                                self.no_data_warnings[current_phase] += 1

                                # Check if we've reached the maximum number of warnings
                                if self.no_data_warnings[current_phase] >= self.max_no_data_warnings:
                                    # Trigger fail-safe exit
                                    self._trigger_fail_safe_exit(current_phase, elapsed,
                                                                reason="No valid data after maximum warnings")
                                    return  # Exit the monitor thread

                            # If fail-safe is disabled or not triggered yet, give extra time
                            # CRITICAL FIX: Use configurable multiplier for extended timeout
                            extended_max = max_duration * self.extended_timeout_multiplier
                            if elapsed >= extended_max:
                                if self.enable_fail_safe and current_phase in self.critical_phases:
                                    # Trigger fail-safe exit
                                    self._trigger_fail_safe_exit(current_phase, elapsed,
                                                                reason="No valid data after extended timeout")
                                    return  # Exit the monitor thread
                                else:
                                    # Force transition anyway if fail-safe is disabled
                                    logging.critical(
                                        f"WATCHDOG FINAL TIMEOUT: {current_phase} exceeded extended maximum duration of "
                                        f"{extended_max:.1f}s ({extended_max/60:.1f}m) with NO VALID DATA. "
                                        f"Setting force transition flag."
                                    )
                                    # CRITICAL FIX: Use set_force_transition_flag instead of directly transitioning
                                    if hasattr(self.phase_manager, 'set_force_transition_flag'):
                                        self.phase_manager.set_force_transition_flag(current_phase, True)
                                        logging.critical(f"CRITICAL FIX: Force transition flag set for {current_phase} due to extended timeout")
                                    else:
                                        # Fallback to force_transition if set_force_transition_flag doesn't exist
                                        logging.critical("CRITICAL WARNING: set_force_transition_flag not found, falling back to force_transition")
                                        self.phase_manager.force_transition(current_phase)
                            else:
                                # Still within extended time, just log a warning
                                extra_time = extended_max - max_duration
                                remaining = extended_max - elapsed
                                warning_count = self.no_data_warnings[current_phase]

                                if self.enable_fail_safe and current_phase in self.critical_phases:
                                    logging.warning(
                                        f"WATCHDOG EXTENSION: {current_phase} exceeded normal duration but has NO VALID DATA. "
                                        f"Allowing extra {extra_time:.1f}s. {remaining:.1f}s remaining before forced transition. "
                                        f"Warning {warning_count}/{self.max_no_data_warnings} before fail-safe exit."
                                    )
                                else:
                                    logging.warning(
                                        f"WATCHDOG EXTENSION: {current_phase} exceeded normal duration but has NO VALID DATA. "
                                        f"Allowing extra {extra_time:.1f}s. {remaining:.1f}s remaining before forced transition."
                                    )

                        # Reset warning flags for this phase
                        for threshold in self.warning_thresholds:
                            self.warning_thresholds[threshold][current_phase] = False

            except Exception as e:
                logging.error(f"Error in PhaseWatchdog thread: {e}")
                import traceback
                logging.error(f"Traceback: {traceback.format_exc()}")

            # Check every second
            time.sleep(1)

        logging.info("PhaseWatchdog thread stopped")

    def set_app(self, app):
        """
        Set a reference to the main application.

        This allows the watchdog to call app methods like check_valid_opening_ranges.

        Args:
            app: The main application instance
        """
        self.app = app
        logging.info("PhaseWatchdog connected to main application")

    def stop(self):
        """
        Stop the watchdog.

        This method stops the watchdog thread. It should be called when
        the scanner is shutting down to ensure clean termination.
        """
        logging.info("Stopping PhaseWatchdog")
        self.active = False

        if self.thread.is_alive():
            self.thread.join(timeout=2)
            if self.thread.is_alive():
                logging.warning("PhaseWatchdog thread did not terminate cleanly")
            else:
                logging.info("PhaseWatchdog thread terminated successfully")

    def reset_warnings(self, phase=None):
        """
        Reset warning flags for a phase or all phases.

        Args:
            phase (str, optional): The phase to reset warnings for.
                                  If None, reset warnings for all phases.
        """
        if phase:
            for threshold in self.warning_thresholds:
                if phase in self.warning_thresholds[threshold]:
                    self.warning_thresholds[threshold][phase] = False
            # Also reset no-data warnings
            if phase in self.no_data_warnings:
                self.no_data_warnings[phase] = 0
        else:
            for threshold in self.warning_thresholds:
                for phase in self.warning_thresholds[threshold]:
                    self.warning_thresholds[threshold][phase] = False
            # Also reset all no-data warnings
            for phase in self.no_data_warnings:
                self.no_data_warnings[phase] = 0

        logging.debug(f"Reset watchdog warnings for {'all phases' if phase is None else phase}")

    def _trigger_fail_safe_exit(self, phase, elapsed, reason="No valid data"):
        """
        Trigger the fail-safe exit mechanism.

        This method logs critical error information and exits the process
        when no valid data is available in a critical phase.

        Args:
            phase (str): The phase that triggered the fail-safe
            elapsed (float): The elapsed time in the phase
            reason (str, optional): The specific reason for the fail-safe exit
        """
        # Log detailed error information
        separator = "!" * 100
        logging.critical(separator)
        logging.critical(f"FAIL-SAFE EXIT TRIGGERED: {phase} has {reason} after {elapsed:.1f}s")
        logging.critical("The scanner cannot proceed without valid data in critical phases.")
        logging.critical(f"Specific reason: {reason}")
        logging.critical("Dumping debug information before exit:")

        # Dump phase manager state
        logging.critical(f"Current phase: {self.phase_manager.current_phase}")
        logging.critical(f"Phase history: {self.phase_manager.phase_history}")
        logging.critical(f"Valid symbols by phase: {self.phase_manager.valid_symbols}")
        logging.critical(f"Total symbols by phase: {self.phase_manager.total_symbols}")
        logging.critical(f"Data quality flags: {self.phase_manager.data_quality_flags}")

        # CRITICAL FIX: Add more detailed diagnostics for ORB phase
        if phase == PhaseManager.ORB and self.app is not None:
            try:
                valid_count, total_count = self.app.check_valid_opening_ranges()
                logging.critical(f"ORB phase diagnostics: {valid_count}/{total_count} valid opening ranges")

                # Try to get more detailed information about tracked symbols
                if hasattr(self.app, 'tracked_symbols'):
                    logging.critical(f"Tracked symbols count: {len(self.app.tracked_symbols)}")
                    for i, symbol in enumerate(self.app.tracked_symbols[:5]):  # Log first 5 symbols
                        logging.critical(f"Symbol {i+1}: {symbol}")

                # Try to get information about historical data requests
                if hasattr(self.app, 'historical_data_requests'):
                    logging.critical(f"Historical data requests: {len(self.app.historical_data_requests)}")
            except Exception as e:
                logging.critical(f"Error getting ORB diagnostics: {e}")

        # Log exit message
        logging.critical("EXITING TEST: No valid data available for processing.")
        logging.critical("Check logs for errors and ensure proper market data is available.")
        logging.critical(separator)

        # CRITICAL FIX: Ensure the entire process is terminated, not just the current thread
        # First try to use os._exit which is more forceful than sys.exit
        try:
            # Flush all log handlers to ensure logs are written before exit
            for handler in logging.root.handlers:
                handler.flush()

            # Cancel any pending operations if possible
            if self.app is not None and hasattr(self.app, 'cancel_all_operations'):
                try:
                    self.app.cancel_all_operations()
                except Exception as e:
                    logging.critical(f"Error cancelling operations: {e}")

            # Send SIGTERM to the current process
            logging.critical("Sending SIGTERM to process...")
            pid = os.getpid()
            os.kill(pid, signal.SIGTERM)

            # If we're still running after 1 second, use os._exit
            time.sleep(1)
            logging.critical("SIGTERM didn't terminate process, using os._exit()...")
            os._exit(1)
        except Exception as e:
            # If all else fails, use sys.exit
            logging.critical(f"Error during forceful exit: {e}, falling back to sys.exit()")
            sys.exit(1)
