"""
Data Tracing Integration for the 7-minute scanner.

This module integrates the DataRequestTracer with the existing data retrieval code
to track price data requests and help diagnose missing data issues.
"""

import logging
import functools
import inspect
import time
import traceback
from orbscanner.utils.data.request_tracer import DataRequestTracer

# Global tracer instance
_tracer = None

def get_tracer():
    """Get the global tracer instance, creating it if needed."""
    global _tracer
    if _tracer is None:
        _tracer = DataRequestTracer()
    return _tracer

def trace_data_request(request_type):
    """
    Decorator to trace data requests.

    Args:
        request_type (str): Type of request being traced

    Returns:
        function: Decorated function
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Get the tracer
            tracer = get_tracer()

            # Extract symbol from args or kwargs
            symbol = None

            # Try to find symbol in kwargs
            if 'symbol' in kwargs:
                symbol = kwargs['symbol']

            # If not in kwargs, try to find in args based on function signature
            if symbol is None:
                try:
                    sig = inspect.signature(func)
                    param_names = list(sig.parameters.keys())

                    # Skip 'self' if it's the first parameter
                    if param_names and param_names[0] == 'self':
                        param_names = param_names[1:]

                    # Look for 'symbol' in parameter names
                    if 'symbol' in param_names:
                        symbol_idx = param_names.index('symbol')
                        # Adjust for 'self'
                        if len(args) > symbol_idx:
                            symbol = args[symbol_idx]
                except Exception as e:
                    logging.debug(f"Error extracting symbol from args: {e}")

            # If still no symbol, try to extract from first arg if it's a string
            if symbol is None and args and isinstance(args[0], str):
                symbol = args[0]

            # CRITICAL FIX: For priority_score requests, extract symbol from symbol_data
            if symbol is None and request_type == "priority_score" and len(args) > 1:
                # For calculate_priority_score(self, symbol_data), extract symbol from symbol_data
                symbol_data = args[1]
                if isinstance(symbol_data, dict) and 'symbol' in symbol_data:
                    symbol = symbol_data['symbol']
                    logging.debug(f"Extracted symbol '{symbol}' from symbol_data for priority_score request")

            # CRITICAL FIX: For historical_opening_range requests, ensure symbol is a string
            if symbol is not None and not isinstance(symbol, str):
                try:
                    # Log detailed information about the non-string symbol
                    logging.warning(f"CRITICAL FIX: Non-string symbol detected in {request_type} request: type={type(symbol).__name__}")

                    # Check if it's an IBApp object (self)
                    if str(symbol).startswith("<__main__.IBApp object at"):
                        logging.critical(f"CRITICAL ERROR: IBApp object (self) detected as symbol in {request_type} request")
                        # Log the call stack to help identify where this is happening
                        stack_trace = ''.join(traceback.format_stack())
                        logging.critical(f"Call stack for IBApp object as symbol:\n{stack_trace}")

                    # Try to convert to string
                    symbol_str = str(symbol)

                    # Check if the conversion resulted in an object reference
                    if '<' in symbol_str and '>' in symbol_str and 'object at' in symbol_str:
                        logging.error(f"CRITICAL ERROR: String conversion resulted in object reference: {symbol_str}")
                        symbol = "UNKNOWN"
                    else:
                        logging.warning(f"CRITICAL FIX: Converted non-string symbol to string: {symbol_str}")
                        symbol = symbol_str
                except Exception as e:
                    logging.error(f"CRITICAL FIX: Failed to convert symbol to string: {e}")
                    symbol = "UNKNOWN"

            # If still no symbol, use a placeholder
            if symbol is None:
                symbol = "UNKNOWN"
                logging.warning(f"CRITICAL WARNING: Could not extract symbol for {request_type} request")

            # Extract request ID if available
            request_id = kwargs.get('reqId', None)

            # Prepare params dict
            params = {}
            for k, v in kwargs.items():
                if k != 'self':  # Skip self
                    params[k] = str(v)  # Convert to string for logging

            # Get current phase if available with enhanced error handling
            current_phase = None
            try:
                if args and hasattr(args[0], 'phase_manager') and hasattr(args[0].phase_manager, 'current_phase'):
                    current_phase = args[0].phase_manager.current_phase

                    # CRITICAL FIX: Add more detailed phase information
                    if hasattr(args[0].phase_manager, 'get_elapsed_time'):
                        elapsed_time = args[0].phase_manager.get_elapsed_time()
                        elapsed_min = int(elapsed_time / 60)
                        elapsed_sec = int(elapsed_time % 60)
                        params['phase_elapsed'] = f"{elapsed_min}m {elapsed_sec}s"

                    # Add phase transition information if available
                    if hasattr(args[0].phase_manager, 'phase_history') and args[0].phase_manager.phase_history:
                        last_transition = args[0].phase_manager.phase_history[-1]
                        params['last_transition'] = f"{last_transition['phase']} → {current_phase}"
            except Exception as e:
                logging.debug(f"Error extracting detailed phase information: {e}")

            # Start tracing with enhanced phase information
            trace_id = tracer.start_request(request_type, symbol, request_id, params, phase=current_phase)

            start_time = time.time()
            result = None
            error = None
            success = False

            try:
                # Call the original function
                result = func(*args, **kwargs)
                success = True
                return result
            except Exception as e:
                error = str(e)
                raise
            finally:
                # End tracing
                duration = time.time() - start_time

                # Prepare response data
                response = {
                    'result_type': type(result).__name__ if result is not None else 'None',
                    'duration': duration
                }

                # Add result details if it's a simple type
                if result is not None and isinstance(result, (int, float, str, bool)):
                    response['value'] = result

                tracer.end_request(trace_id, success, response, error)

        return wrapper
    return decorator

def trace_async_data_request(request_type):
    """
    Decorator to trace async data requests.

    Args:
        request_type (str): Type of request being traced

    Returns:
        function: Decorated async function
    """
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Get the tracer
            tracer = get_tracer()

            # Extract symbol from args or kwargs
            symbol = None

            # Try to find symbol in kwargs
            if 'symbol' in kwargs:
                symbol = kwargs['symbol']

            # If not in kwargs, try to find in args based on function signature
            if symbol is None:
                try:
                    sig = inspect.signature(func)
                    param_names = list(sig.parameters.keys())

                    # Skip 'self' if it's the first parameter
                    if param_names and param_names[0] == 'self':
                        param_names = param_names[1:]

                    # Look for 'symbol' in parameter names
                    if 'symbol' in param_names:
                        symbol_idx = param_names.index('symbol')
                        # Adjust for 'self'
                        if len(args) > symbol_idx:
                            symbol = args[symbol_idx]
                except Exception as e:
                    logging.debug(f"Error extracting symbol from args: {e}")

            # If still no symbol, try to extract from first arg if it's a string
            if symbol is None and args and isinstance(args[0], str):
                symbol = args[0]

            # CRITICAL FIX: For priority_score requests, extract symbol from symbol_data
            if symbol is None and request_type == "priority_score" and len(args) > 1:
                # For calculate_priority_score(self, symbol_data), extract symbol from symbol_data
                symbol_data = args[1]
                if isinstance(symbol_data, dict) and 'symbol' in symbol_data:
                    symbol = symbol_data['symbol']
                    logging.debug(f"Extracted symbol '{symbol}' from symbol_data for priority_score request")

            # CRITICAL FIX: For historical_opening_range requests, ensure symbol is a string
            if symbol is not None and not isinstance(symbol, str):
                try:
                    # Log detailed information about the non-string symbol
                    logging.warning(f"CRITICAL FIX: Non-string symbol detected in {request_type} request: type={type(symbol).__name__}")

                    # Check if it's an IBApp object (self)
                    if str(symbol).startswith("<__main__.IBApp object at"):
                        logging.critical(f"CRITICAL ERROR: IBApp object (self) detected as symbol in {request_type} request")
                        # Log the call stack to help identify where this is happening
                        stack_trace = ''.join(traceback.format_stack())
                        logging.critical(f"Call stack for IBApp object as symbol:\n{stack_trace}")

                    # Try to convert to string
                    symbol_str = str(symbol)

                    # Check if the conversion resulted in an object reference
                    if '<' in symbol_str and '>' in symbol_str and 'object at' in symbol_str:
                        logging.error(f"CRITICAL ERROR: String conversion resulted in object reference: {symbol_str}")
                        symbol = "UNKNOWN"
                    else:
                        logging.warning(f"CRITICAL FIX: Converted non-string symbol to string: {symbol_str}")
                        symbol = symbol_str
                except Exception as e:
                    logging.error(f"CRITICAL FIX: Failed to convert symbol to string: {e}")
                    symbol = "UNKNOWN"

            # If still no symbol, use a placeholder
            if symbol is None:
                symbol = "UNKNOWN"
                logging.warning(f"CRITICAL WARNING: Could not extract symbol for {request_type} request")

            # Extract request ID if available
            request_id = kwargs.get('reqId', None)

            # Prepare params dict
            params = {}
            for k, v in kwargs.items():
                if k != 'self':  # Skip self
                    params[k] = str(v)  # Convert to string for logging

            # Get current phase if available with enhanced error handling
            current_phase = None
            try:
                if args and hasattr(args[0], 'phase_manager') and hasattr(args[0].phase_manager, 'current_phase'):
                    current_phase = args[0].phase_manager.current_phase

                    # CRITICAL FIX: Add more detailed phase information
                    if hasattr(args[0].phase_manager, 'get_elapsed_time'):
                        elapsed_time = args[0].phase_manager.get_elapsed_time()
                        elapsed_min = int(elapsed_time / 60)
                        elapsed_sec = int(elapsed_time % 60)
                        params['phase_elapsed'] = f"{elapsed_min}m {elapsed_sec}s"

                    # Add phase transition information if available
                    if hasattr(args[0].phase_manager, 'phase_history') and args[0].phase_manager.phase_history:
                        last_transition = args[0].phase_manager.phase_history[-1]
                        params['last_transition'] = f"{last_transition['phase']} → {current_phase}"
            except Exception as e:
                logging.debug(f"Error extracting detailed phase information: {e}")

            # Start tracing with enhanced phase information
            trace_id = tracer.start_request(request_type, symbol, request_id, params, phase=current_phase)

            start_time = time.time()
            result = None
            error = None
            success = False

            try:
                # Call the original function
                result = await func(*args, **kwargs)
                success = True
                return result
            except Exception as e:
                error = str(e)
                raise
            finally:
                # End tracing
                duration = time.time() - start_time

                # Prepare response data
                response = {
                    'result_type': type(result).__name__ if result is not None else 'None',
                    'duration': duration
                }

                # Add result details if it's a simple type
                if result is not None and isinstance(result, (int, float, str, bool)):
                    response['value'] = result

                tracer.end_request(trace_id, success, response, error)

        return wrapper
    return decorator

def initialize_tracing(enabled=True):
    """
    Initialize the data request tracing system.

    Args:
        enabled (bool): Whether tracing should be enabled

    Returns:
        DataRequestTracer: The tracer instance
    """
    global _tracer
    _tracer = DataRequestTracer(enabled=enabled)
    return _tracer

def log_tracing_statistics():
    """Log current tracing statistics with enhanced symbol type analysis."""
    tracer = get_tracer()

    # Log basic statistics
    tracer.log_statistics()

    # Enhanced logging: Analyze active requests for symbol type issues
    if hasattr(tracer, 'active_requests') and tracer.active_requests:
        logging.info("SYMBOL TYPE ANALYSIS: Checking active requests for symbol type issues")

        # Count requests by symbol type
        symbol_type_counts = {}
        object_reference_count = 0

        for trace_id, request in tracer.active_requests.items():
            symbol = request.get('symbol', 'UNKNOWN')
            symbol_type = type(symbol).__name__

            # Count by type
            if symbol_type not in symbol_type_counts:
                symbol_type_counts[symbol_type] = 0
            symbol_type_counts[symbol_type] += 1

            # Check for object references
            if isinstance(symbol, str) and '<' in symbol and '>' in symbol and 'object at' in symbol:
                object_reference_count += 1
                logging.warning(f"SYMBOL TYPE ISSUE: Object reference as string: {symbol} in request {trace_id}")

        # Log the results
        logging.info(f"SYMBOL TYPE ANALYSIS: Found {len(symbol_type_counts)} different symbol types in active requests")
        for symbol_type, count in symbol_type_counts.items():
            logging.info(f"SYMBOL TYPE ANALYSIS: {symbol_type}: {count} requests")

        if object_reference_count > 0:
            logging.warning(f"SYMBOL TYPE ANALYSIS: Found {object_reference_count} object references as strings")

    # Log a reminder about the importance of proper symbol types
    logging.info("REMINDER: All symbols should be strings. Object references can cause data retrieval failures.")
