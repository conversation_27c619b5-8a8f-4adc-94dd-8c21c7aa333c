"""
Data Request Tracer for the 7-minute scanner.

This module provides tracing capabilities for data requests to help diagnose
missing price data issues by tracking request/response details and timing.
"""

import logging
import time
import json
import os
from collections import defaultdict
from datetime import datetime
import threading

class DataRequestTracer:
    """
    Traces data requests and responses to help diagnose missing price data issues.

    This class tracks:
    - Request parameters and timing
    - Response details and success/failure
    - Success rates by symbol and request type
    - Timing statistics
    """

    def __init__(self, enabled=True, log_dir="logs/live"):
        """
        Initialize the DataRequestTracer.

        Args:
            enabled (bool): Whether tracing is enabled
            log_dir (str): Directory to store trace logs
        """
        self.enabled = enabled
        self.log_dir = log_dir
        self.logger = logging.getLogger('DataRequestTracer')

        # Create a dedicated file handler for trace logs
        self._setup_logging()

        # Track request statistics
        self.request_counts = defaultdict(int)
        self.success_counts = defaultdict(int)
        self.failure_counts = defaultdict(int)
        self.timing_data = defaultdict(list)

        # Track active requests
        self.active_requests = {}

        # Thread safety
        self.lock = threading.RLock()

        self.logger.info("Data Request Tracer initialized")

    def _setup_logging(self):
        """Set up dedicated logging for request tracing."""
        # Ensure log directory exists
        os.makedirs(self.log_dir, exist_ok=True)

        # Create timestamp for log file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = os.path.join(self.log_dir, f"data_request_trace_{timestamp}.log")

        # Create file handler
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.DEBUG)

        # Create formatter
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)

        # Add handler to logger
        self.logger.addHandler(file_handler)

        # Set logger level
        self.logger.setLevel(logging.DEBUG)

    def start_request(self, request_type, symbol, request_id=None, params=None, phase=None):
        """
        Start tracking a data request.

        Args:
            request_type (str): Type of request (e.g., 'price', 'historical')
            symbol (str): Symbol being requested
            request_id (int, optional): Request ID if available
            params (dict, optional): Request parameters
            phase (str, optional): Current scanner phase (e.g., 'PRE_MARKET', 'ORB', 'FINALIZING')

        Returns:
            str: Trace ID for this request
        """
        if not self.enabled:
            return None

        with self.lock:
            # Generate a trace ID
            trace_id = f"{request_type}_{symbol}_{time.time()}"

            # Record request start
            self.active_requests[trace_id] = {
                'start_time': time.time(),
                'request_type': request_type,
                'symbol': symbol,
                'request_id': request_id,
                'params': params or {},
                'phase': phase,
                'status': 'pending'
            }

            # Increment request counter
            self.request_counts[request_type] += 1

            # Log request start with phase information
            phase_info = f"Phase: {phase} | " if phase else ""
            self.logger.info(f"REQUEST_START: {trace_id} | {phase_info}Type: {request_type} | Symbol: {symbol} | ReqID: {request_id}")
            if params:
                self.logger.debug(f"REQUEST_PARAMS: {trace_id} | {phase_info}{json.dumps(params, default=str)}")

            return trace_id

    def end_request(self, trace_id, success=True, response=None, error=None):
        """
        End tracking a data request.

        Args:
            trace_id (str): Trace ID from start_request
            success (bool): Whether the request was successful
            response (any, optional): Response data
            error (str, optional): Error message if failed
        """
        if not self.enabled or trace_id is None or trace_id not in self.active_requests:
            return

        with self.lock:
            # Get request info
            request = self.active_requests.get(trace_id)
            if not request:
                self.logger.warning(f"REQUEST_UNKNOWN: {trace_id} | No matching start request found")
                return

            # Calculate duration
            end_time = time.time()
            duration = end_time - request['start_time']

            # Update request status
            request['end_time'] = end_time
            request['duration'] = duration
            request['status'] = 'success' if success else 'failure'
            request['response'] = response
            request['error'] = error

            # Update counters
            request_type = request['request_type']
            symbol = request['symbol']

            if success:
                self.success_counts[request_type] += 1
                log_level = logging.INFO
                status_str = "SUCCESS"
            else:
                self.failure_counts[request_type] += 1
                log_level = logging.ERROR
                status_str = "FAILURE"

            # Store timing data
            self.timing_data[request_type].append(duration)

            # Get phase information
            phase = request.get('phase')
            phase_info = f"Phase: {phase} | " if phase else ""

            # Log request completion with phase information
            self.logger.log(log_level,
                f"REQUEST_END: {trace_id} | {phase_info}Type: {request_type} | Symbol: {symbol} | "
                f"Status: {status_str} | Duration: {duration:.3f}s")

            if response is not None:
                self.logger.debug(f"RESPONSE_DATA: {trace_id} | {phase_info}{json.dumps(response, default=str)}")

            if error is not None:
                self.logger.error(f"ERROR_DETAILS: {trace_id} | {phase_info}{error}")

            # Remove from active requests
            del self.active_requests[trace_id]

    def get_success_rate(self, request_type=None):
        """
        Get success rate for requests.

        Args:
            request_type (str, optional): Type of request to get rate for.
                                         If None, returns overall rate.

        Returns:
            float: Success rate (0.0-1.0)
        """
        with self.lock:
            if request_type:
                total = self.request_counts[request_type]
                if total == 0:
                    return 0.0
                return self.success_counts[request_type] / total
            else:
                total = sum(self.request_counts.values())
                if total == 0:
                    return 0.0
                return sum(self.success_counts.values()) / total

    def get_average_duration(self, request_type=None):
        """
        Get average duration for requests.

        Args:
            request_type (str, optional): Type of request to get average for.
                                         If None, returns overall average.

        Returns:
            float: Average duration in seconds
        """
        with self.lock:
            if request_type:
                durations = self.timing_data[request_type]
                if not durations:
                    return 0.0
                return sum(durations) / len(durations)
            else:
                all_durations = []
                for durations in self.timing_data.values():
                    all_durations.extend(durations)
                if not all_durations:
                    return 0.0
                return sum(all_durations) / len(all_durations)

    def log_statistics(self):
        """Log current request statistics with enhanced details."""
        if not self.enabled:
            return

        with self.lock:
            self.logger.info("===== DATA REQUEST STATISTICS =====")

            # Overall statistics
            total_requests = sum(self.request_counts.values())
            total_success = sum(self.success_counts.values())
            total_failure = sum(self.failure_counts.values())

            if total_requests > 0:
                overall_success_rate = (total_success / total_requests) * 100
            else:
                overall_success_rate = 0.0

            self.logger.info(f"Overall: {total_requests} requests, {total_success} success, {total_failure} failure")
            self.logger.info(f"Overall Success Rate: {overall_success_rate:.1f}%")

            # CRITICAL FIX: Add timestamp for when statistics were generated
            self.logger.info(f"Statistics generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            # CRITICAL FIX: Add warning if success rate is below threshold
            if overall_success_rate < 50.0 and total_requests > 10:
                self.logger.error(f"CRITICAL WARNING: Overall success rate is below 50% ({overall_success_rate:.1f}%)")
                self.logger.error(f"This indicates a systematic issue with data requests")

            # Per-type statistics with enhanced details
            self.logger.info("\n----- Request Type Statistics -----")
            for request_type in sorted(self.request_counts.keys()):
                requests = self.request_counts[request_type]
                success = self.success_counts[request_type]
                failure = self.failure_counts[request_type]

                if requests > 0:
                    success_rate = (success / requests) * 100
                else:
                    success_rate = 0.0

                avg_duration = self.get_average_duration(request_type)

                # Calculate min/max durations for this request type
                durations = self.timing_data.get(request_type, [])
                min_duration = min(durations) if durations else 0
                max_duration = max(durations) if durations else 0

                self.logger.info(
                    f"Type '{request_type}': {requests} requests, {success} success, {failure} failure, "
                    f"Success Rate: {success_rate:.1f}%, Avg Duration: {avg_duration:.3f}s, "
                    f"Min/Max: {min_duration:.3f}s/{max_duration:.3f}s"
                )

                # CRITICAL FIX: Add warning for request types with low success rates
                if success_rate < 50.0 and requests > 5:
                    self.logger.error(f"CRITICAL WARNING: '{request_type}' has a low success rate ({success_rate:.1f}%)")
                    self.logger.error(f"This may indicate an issue with {request_type} requests")

            # Collect phase-specific statistics
            phase_stats = {}
            for req_id, req_data in list(self.active_requests.items()) + [
                (tid, data) for tid, data in [(tid, self.active_requests.get(tid, {}))
                                             for tid in set(r.get('trace_id', '')
                                                          for r in self.active_requests.values())]
            ]:
                phase = req_data.get('phase')
                if phase:
                    if phase not in phase_stats:
                        phase_stats[phase] = {'requests': 0, 'success': 0, 'failure': 0}

                    phase_stats[phase]['requests'] += 1
                    if req_data.get('status') == 'success':
                        phase_stats[phase]['success'] += 1
                    elif req_data.get('status') == 'failure':
                        phase_stats[phase]['failure'] += 1

            # Log phase-specific statistics
            if phase_stats:
                self.logger.info("\n----- Phase-Specific Statistics -----")
                for phase, stats in sorted(phase_stats.items()):
                    requests = stats['requests']
                    success = stats['success']
                    failure = stats['failure']

                    if requests > 0:
                        success_rate = (success / requests) * 100
                    else:
                        success_rate = 0.0

                    self.logger.info(
                        f"Phase '{phase}': {requests} requests, {success} success, {failure} failure, "
                        f"Success Rate: {success_rate:.1f}%"
                    )

            # Log detailed historical data request parameters
            historical_requests = [req for req in self.active_requests.values()
                                  if 'historical' in req.get('request_type', '').lower()]

            if historical_requests:
                self.logger.info("\n----- Historical Data Request Parameters -----")
                for req in historical_requests:
                    phase = req.get('phase', 'UNKNOWN')
                    symbol = req.get('symbol', 'UNKNOWN')
                    params = req.get('params', {})

                    param_str = ", ".join([f"{k}={v}" for k, v in params.items()])
                    self.logger.info(f"Phase: {phase} | Symbol: {symbol} | Params: {param_str}")

            self.logger.info("===================================")
