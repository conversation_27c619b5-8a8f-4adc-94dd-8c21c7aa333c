"""
Priority Debug Logger for 7-Minute Scanner

This module provides enhanced logging for priority score calculation steps
to help diagnose issues with priority calculation.
"""

import os
import logging
from datetime import datetime

# Configure logger
PRIORITY_DEBUG_LOGGER = None

def setup_priority_debug_logger(log_base_path=None):
    """
    Set up a dedicated logger for priority calculation debugging.

    Args:
        log_base_path (str): Base path for log files. If None, uses default.

    Returns:
        logging.Logger: Configured logger object
    """
    global PRIORITY_DEBUG_LOGGER

    if PRIORITY_DEBUG_LOGGER is not None:
        return PRIORITY_DEBUG_LOGGER

    # Create logger
    logger = logging.getLogger('priority_debug')
    logger.setLevel(logging.DEBUG)

    # Remove any existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Create log file path
    if log_base_path is None:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_dir = "logs/live"
        os.makedirs(log_dir, exist_ok=True)
        log_file = f"{log_dir}/scan_7min_{timestamp}_priority_debug.log"
    else:
        log_file = f"{log_base_path}_priority_debug.log"

    # Create file handler
    try:
        file_handler = logging.FileHandler(log_file, mode='a', encoding='utf-8')
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - [%(symbol)s] [%(stage)s] %(message)s')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

        # Don't propagate to root logger to avoid duplicate entries
        logger.propagate = False

        # Log initialization
        logger.info("Priority debug logger initialized", extra={'symbol': 'SYSTEM', 'stage': 'INIT'})

        PRIORITY_DEBUG_LOGGER = logger
        return logger

    except Exception as e:
        logging.error(f"Failed to set up priority debug logger: {e}")
        return None

def log_priority_debug(symbol, stage, message, level=logging.INFO, **kwargs):
    """
    Log a priority calculation debug message with symbol and stage context.

    Args:
        symbol (str): The symbol being processed
        stage (str): The calculation stage (e.g., INPUT, CALCULATION, RESULT)
        message (str): The log message
        level (int): Logging level (default: INFO)
        **kwargs: Additional data to include in the log message
    """
    global PRIORITY_DEBUG_LOGGER

    if PRIORITY_DEBUG_LOGGER is None:
        setup_priority_debug_logger()

    if PRIORITY_DEBUG_LOGGER is None:
        # Fallback to standard logging if setup failed
        logging.log(level, f"[PRIORITY_DEBUG] [{symbol}] [{stage}] {message} {kwargs}")
        return

    # Format additional data if provided
    data_str = ""
    if kwargs:
        data_str = " " + " ".join([f"{k}={v}" for k, v in kwargs.items()])

    # Log with context
    PRIORITY_DEBUG_LOGGER.log(level, f"{message}{data_str}", extra={'symbol': symbol, 'stage': stage})

# Convenience functions for different stages
def log_priority_input(symbol, message, **kwargs):
    """Log input data for priority calculation"""
    log_priority_debug(symbol, "INPUT", message, **kwargs)

def log_priority_calculation(symbol, message, **kwargs):
    """Log a calculation step for priority calculation"""
    log_priority_debug(symbol, "CALCULATION", message, **kwargs)

def log_priority_component(symbol, component_name, value, weight=None, cap=None, **kwargs):
    """Log a priority score component calculation"""
    if weight is not None and cap is not None:
        message = f"Component {component_name}: {value:.4f} (weight={weight:.2f}, cap={cap:.2f})"
    elif weight is not None:
        message = f"Component {component_name}: {value:.4f} (weight={weight:.2f})"
    else:
        message = f"Component {component_name}: {value:.4f}"

    log_priority_debug(symbol, "COMPONENT", message, **kwargs)

def log_priority_result(symbol, score, **kwargs):
    """Log the final priority score result"""
    log_priority_debug(symbol, "RESULT", f"Final priority score: {score:.4f}", **kwargs)

def log_priority_error(symbol, message, **kwargs):
    """Log an error in priority calculation"""
    log_priority_debug(symbol, "ERROR", message, level=logging.ERROR, **kwargs)

def log_priority_warning(symbol, message, **kwargs):
    """Log a warning in priority calculation"""
    log_priority_debug(symbol, "WARNING", message, level=logging.WARNING, **kwargs)

def log_priority_fallback(symbol, message, **kwargs):
    """Log a fallback mechanism in priority calculation"""
    log_priority_debug(symbol, "FALLBACK", message, **kwargs)

def log_priority_validation(symbol, message, **kwargs):
    """Log validation of input data for priority calculation"""
    log_priority_debug(symbol, "VALIDATION", message, **kwargs)

def log_priority_summary(stage, **kwargs):
    """
    Log summary statistics for priority calculation.

    Args:
        stage (str): The summary stage identifier
        **kwargs: Statistics to log
    """
    log_priority_debug("SYSTEM", stage, "Priority calculation statistics:", **kwargs)
