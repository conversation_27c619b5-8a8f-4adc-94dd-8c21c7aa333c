"""
Volume Debug Logger for 7-Minute Scanner

This module provides enhanced logging for volume data collection and processing
to help diagnose issues with volume data.
"""

import os
import logging
from datetime import datetime

# Configure logger
VOLUME_DEBUG_LOGGER = None

def setup_volume_debug_logger(log_base_path=None):
    """
    Set up a dedicated logger for volume data debugging.
    
    Args:
        log_base_path (str): Base path for log files. If None, uses default.
    
    Returns:
        logging.Logger: Configured logger object
    """
    global VOLUME_DEBUG_LOGGER
    
    if VOLUME_DEBUG_LOGGER is not None:
        return VOLUME_DEBUG_LOGGER
    
    # Create logger
    logger = logging.getLogger('volume_debug')
    logger.setLevel(logging.DEBUG)
    
    # Remove any existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # Create log file path
    if log_base_path is None:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_dir = "logs/live"
        os.makedirs(log_dir, exist_ok=True)
        log_file = f"{log_dir}/scan_7min_{timestamp}_volume_debug.log"
    else:
        log_file = f"{log_base_path}_volume_debug.log"
    
    # Create file handler
    try:
        file_handler = logging.FileHandler(log_file, mode='a', encoding='utf-8')
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - [%(symbol)s] [%(request_id)s] [%(operation)s] %(message)s')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        # Don't propagate to root logger to avoid duplicate entries
        logger.propagate = False
        
        # Log initialization
        logger.info("Volume debug logger initialized", 
                   extra={'symbol': 'SYSTEM', 'request_id': 'INIT', 'operation': 'INIT'})
        
        VOLUME_DEBUG_LOGGER = logger
        return logger
    
    except Exception as e:
        logging.error(f"Failed to set up volume debug logger: {e}")
        return None

def log_volume_debug(symbol, request_id, operation, message, level=logging.INFO, **kwargs):
    """
    Log a volume data debug message with context.
    
    Args:
        symbol (str): The symbol being processed
        request_id (str): The request ID or identifier
        operation (str): The operation being performed (e.g., REQUEST, RESPONSE, PROCESSING)
        message (str): The log message
        level (int): Logging level (default: INFO)
        **kwargs: Additional data to include in the log message
    """
    global VOLUME_DEBUG_LOGGER
    
    if VOLUME_DEBUG_LOGGER is None:
        setup_volume_debug_logger()
    
    if VOLUME_DEBUG_LOGGER is None:
        # Fallback to standard logging if setup failed
        logging.log(level, f"[VOLUME_DEBUG] [{symbol}] [{request_id}] [{operation}] {message} {kwargs}")
        return
    
    # Format additional data if provided
    data_str = ""
    if kwargs:
        data_str = " " + " ".join([f"{k}={v}" for k, v in kwargs.items()])
    
    # Log with context
    VOLUME_DEBUG_LOGGER.log(level, f"{message}{data_str}", 
                           extra={'symbol': symbol, 'request_id': str(request_id), 'operation': operation})

# Convenience functions for different operations
def log_volume_request(symbol, request_id, message, **kwargs):
    """Log a message for volume data request"""
    log_volume_debug(symbol, request_id, "REQUEST", message, **kwargs)

def log_volume_response(symbol, request_id, message, **kwargs):
    """Log a message for volume data response"""
    log_volume_debug(symbol, request_id, "RESPONSE", message, **kwargs)

def log_volume_processing(symbol, request_id, message, **kwargs):
    """Log a message for volume data processing"""
    log_volume_debug(symbol, request_id, "PROCESSING", message, **kwargs)

def log_volume_caching(symbol, request_id, message, **kwargs):
    """Log a message for volume data caching"""
    log_volume_debug(symbol, request_id, "CACHING", message, **kwargs)

def log_volume_error(symbol, request_id, message, **kwargs):
    """Log an error message for volume data"""
    log_volume_debug(symbol, request_id, "ERROR", message, level=logging.ERROR, **kwargs)

def log_volume_warning(symbol, request_id, message, **kwargs):
    """Log a warning message for volume data"""
    log_volume_debug(symbol, request_id, "WARNING", message, level=logging.WARNING, **kwargs)

def log_volume_fallback(symbol, request_id, message, **kwargs):
    """Log a message for volume data fallback mechanism"""
    log_volume_debug(symbol, request_id, "FALLBACK", message, **kwargs)

def log_volume_summary(message, **kwargs):
    """Log a summary message for volume data collection"""
    log_volume_debug("SUMMARY", "N/A", "SUMMARY", message, **kwargs)

def log_volume_stats(stats):
    """
    Log volume data collection statistics.
    
    Args:
        stats (dict): Dictionary of statistics
    """
    log_volume_summary("Volume data collection statistics:", stats=stats)
    
    # Log individual stats
    for key, value in stats.items():
        log_volume_debug("STATS", "N/A", "STATS", f"{key}: {value}")
