"""
Range Debug Logger for 7-Minute Scanner

This module provides enhanced logging for opening range calculation steps
to help diagnose issues with range calculation and validation.
"""

import os
import logging
from datetime import datetime

# Configure logger
RANGE_DEBUG_LOGGER = None

def setup_range_debug_logger(log_base_path=None):
    """
    Set up a dedicated logger for range calculation debugging.
    
    Args:
        log_base_path (str): Base path for log files. If None, uses default.
    
    Returns:
        logging.Logger: Configured logger object
    """
    global RANGE_DEBUG_LOGGER
    
    if RANGE_DEBUG_LOGGER is not None:
        return RANGE_DEBUG_LOGGER
    
    # Create logger
    logger = logging.getLogger('range_debug')
    logger.setLevel(logging.DEBUG)
    
    # Remove any existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # Create log file path
    if log_base_path is None:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_dir = "logs/live"
        os.makedirs(log_dir, exist_ok=True)
        log_file = f"{log_dir}/scan_7min_{timestamp}_range_debug.log"
    else:
        log_file = f"{log_base_path}_range_debug.log"
    
    # Create file handler
    try:
        file_handler = logging.FileHandler(log_file, mode='a', encoding='utf-8')
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - [%(symbol)s] [%(stage)s] %(message)s')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        # Don't propagate to root logger to avoid duplicate entries
        logger.propagate = False
        
        # Log initialization
        logger.info("Range debug logger initialized", extra={'symbol': 'SYSTEM', 'stage': 'INIT'})
        
        RANGE_DEBUG_LOGGER = logger
        return logger
    
    except Exception as e:
        logging.error(f"Failed to set up range debug logger: {e}")
        return None

def log_range_debug(symbol, stage, message, level=logging.INFO, **kwargs):
    """
    Log a range calculation debug message with symbol and stage context.
    
    Args:
        symbol (str): The symbol being processed
        stage (str): The calculation stage (e.g., DATA_COLLECTION, CALCULATION, VALIDATION)
        message (str): The log message
        level (int): Logging level (default: INFO)
        **kwargs: Additional data to include in the log message
    """
    global RANGE_DEBUG_LOGGER
    
    if RANGE_DEBUG_LOGGER is None:
        setup_range_debug_logger()
    
    if RANGE_DEBUG_LOGGER is None:
        # Fallback to standard logging if setup failed
        logging.log(level, f"[RANGE_DEBUG] [{symbol}] [{stage}] {message} {kwargs}")
        return
    
    # Format additional data if provided
    data_str = ""
    if kwargs:
        data_str = " " + " ".join([f"{k}={v}" for k, v in kwargs.items()])
    
    # Log with context
    RANGE_DEBUG_LOGGER.log(level, f"{message}{data_str}", extra={'symbol': symbol, 'stage': 'STAGE'})

# Convenience functions for different stages
def log_range_data_collection(symbol, message, **kwargs):
    """Log a message for the data collection stage"""
    log_range_debug(symbol, "DATA_COLLECTION", message, **kwargs)

def log_range_calculation(symbol, message, **kwargs):
    """Log a message for the calculation stage"""
    log_range_debug(symbol, "CALCULATION", message, **kwargs)

def log_range_validation(symbol, message, **kwargs):
    """Log a message for the validation stage"""
    log_range_debug(symbol, "VALIDATION", message, **kwargs)

def log_range_error(symbol, message, **kwargs):
    """Log an error message for range calculation"""
    log_range_debug(symbol, "ERROR", message, level=logging.ERROR, **kwargs)

def log_range_warning(symbol, message, **kwargs):
    """Log a warning message for range calculation"""
    log_range_debug(symbol, "WARNING", message, level=logging.WARNING, **kwargs)
