"""
Rate limiter for Interactive Brokers API requests.

This module provides a rate limiter class to manage request rates
according to IBKR API limitations, particularly for historical data.
"""

import time
import logging
from collections import deque

class HistoricalDataRateLimiter:
    """
    Rate limiter for IBKR historical data requests.
    
    IBKR limits historical data requests to:
    - 50 requests per 10 minutes (global limit)
    - 6 requests per 2 minutes per symbol (per-symbol limit)
    """
    
    def __init__(self, max_requests=50, window_seconds=600, 
                 max_symbol_requests=6, symbol_window_seconds=120):
        """
        Initialize the rate limiter.
        
        Args:
            max_requests (int): Maximum number of requests in the time window (default: 50)
            window_seconds (int): Time window in seconds (default: 600 - 10 minutes)
            max_symbol_requests (int): Maximum requests per symbol in symbol window (default: 6)
            symbol_window_seconds (int): Symbol-specific time window in seconds (default: 120 - 2 minutes)
        """
        self.request_timestamps = deque()
        self.symbol_timestamps = {}
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.max_symbol_requests = max_symbol_requests
        self.symbol_window_seconds = symbol_window_seconds
        
        # Tracking for logging and reporting
        self.total_requests = 0
        self.delayed_requests = 0
        self.rate_limited_symbols = set()
        
        # Last time we logged rate limit status
        self.last_status_log = 0
        self.status_log_interval = 60  # Log status every 60 seconds
        
    def can_make_request(self, symbol=None):
        """
        Check if a request can be made within rate limits.
        
        Args:
            symbol (str, optional): Symbol for the request. If provided, 
                                   also checks symbol-specific rate limits.
        
        Returns:
            tuple: (can_request, reason)
                - can_request (bool): True if request can be made, False otherwise
                - reason (str): Reason why request can't be made, or None if it can
        """
        now = time.time()
        
        # Clean up expired timestamps from global window
        while self.request_timestamps and now - self.request_timestamps[0] > self.window_seconds:
            self.request_timestamps.popleft()
        
        # Check global rate limit
        if len(self.request_timestamps) >= self.max_requests:
            oldest = self.request_timestamps[0]
            wait_time = oldest + self.window_seconds - now
            return False, f"Global rate limit reached ({len(self.request_timestamps)}/{self.max_requests}). Wait {wait_time:.1f}s"
        
        # Check symbol-specific rate limit if symbol provided
        if symbol:
            # Initialize if this is first request for symbol
            if symbol not in self.symbol_timestamps:
                self.symbol_timestamps[symbol] = deque()
            
            # Clean up expired timestamps for this symbol
            symbol_times = self.symbol_timestamps[symbol]
            while symbol_times and now - symbol_times[0] > self.symbol_window_seconds:
                symbol_times.popleft()
            
            # Check symbol-specific limit
            if len(symbol_times) >= self.max_symbol_requests:
                oldest = symbol_times[0]
                wait_time = oldest + self.symbol_window_seconds - now
                return False, f"Symbol rate limit reached for {symbol} ({len(symbol_times)}/{self.max_symbol_requests}). Wait {wait_time:.1f}s"
        
        # Log status periodically
        if now - self.last_status_log > self.status_log_interval:
            self.log_status()
            self.last_status_log = now
            
        return True, None
    
    def record_request(self, symbol=None):
        """
        Record that a request was made.
        
        Args:
            symbol (str, optional): Symbol for the request.
        """
        now = time.time()
        self.request_timestamps.append(now)
        self.total_requests += 1
        
        if symbol:
            if symbol not in self.symbol_timestamps:
                self.symbol_timestamps[symbol] = deque()
            self.symbol_timestamps[symbol].append(now)
    
    def wait_if_needed(self, symbol=None):
        """
        Wait if necessary to comply with rate limits.
        
        Args:
            symbol (str, optional): Symbol for the request.
            
        Returns:
            float: Time waited in seconds, or 0 if no wait was needed.
        """
        can_request, reason = self.can_make_request(symbol)
        
        if can_request:
            return 0
            
        # We need to wait - calculate how long
        now = time.time()
        wait_time = 0
        
        # Check global limit
        if len(self.request_timestamps) >= self.max_requests:
            oldest = self.request_timestamps[0]
            global_wait = oldest + self.window_seconds - now
            wait_time = max(wait_time, global_wait)
        
        # Check symbol limit
        if symbol and symbol in self.symbol_timestamps:
            symbol_times = self.symbol_timestamps[symbol]
            if len(symbol_times) >= self.max_symbol_requests:
                oldest = symbol_times[0]
                symbol_wait = oldest + self.symbol_window_seconds - now
                wait_time = max(wait_time, symbol_wait)
        
        # Add a small buffer
        wait_time += 0.5
        
        # Log that we're waiting
        logging.warning(f"RATE LIMIT: Waiting {wait_time:.1f}s before next request. {reason}")
        
        # Track for reporting
        self.delayed_requests += 1
        if symbol:
            self.rate_limited_symbols.add(symbol)
            
        # Wait and return time waited
        time.sleep(wait_time)
        return wait_time
    
    def log_status(self):
        """Log current rate limit status."""
        now = time.time()
        
        # Clean up expired timestamps
        while self.request_timestamps and now - self.request_timestamps[0] > self.window_seconds:
            self.request_timestamps.popleft()
            
        # Calculate time until a slot opens up
        time_until_slot = 0
        if len(self.request_timestamps) >= self.max_requests and self.request_timestamps:
            time_until_slot = self.request_timestamps[0] + self.window_seconds - now
            
        # Log status
        status = {
            "current_usage": f"{len(self.request_timestamps)}/{self.max_requests}",
            "total_requests": self.total_requests,
            "delayed_requests": self.delayed_requests,
            "rate_limited_symbols_count": len(self.rate_limited_symbols),
            "time_until_next_slot": f"{time_until_slot:.1f}s" if time_until_slot > 0 else "Available now"
        }
        
        logging.info(f"RATE LIMIT STATUS: {status}")
        
        # Log symbol-specific status for rate-limited symbols
        if self.rate_limited_symbols:
            for symbol in list(self.rate_limited_symbols)[:5]:  # Log up to 5 symbols
                if symbol in self.symbol_timestamps:
                    symbol_times = self.symbol_timestamps[symbol]
                    # Clean up expired timestamps
                    while symbol_times and now - symbol_times[0] > self.symbol_window_seconds:
                        symbol_times.popleft()
                    
                    symbol_status = {
                        "symbol": symbol,
                        "current_usage": f"{len(symbol_times)}/{self.max_symbol_requests}",
                    }
                    logging.info(f"SYMBOL RATE LIMIT: {symbol_status}")
            
            if len(self.rate_limited_symbols) > 5:
                logging.info(f"SYMBOL RATE LIMIT: {len(self.rate_limited_symbols) - 5} more symbols rate limited")
