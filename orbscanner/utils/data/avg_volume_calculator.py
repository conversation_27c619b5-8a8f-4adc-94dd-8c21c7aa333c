"""
Simple Average Volume Calculator for 7-Minute Scanner

This module provides a simplified method for calculating average daily volume
for a small set of symbols during the finalizing phase, avoiding the issues
with the comprehensive volume data collection system.
"""

import logging
import threading
import time
from datetime import datetime
import pytz

def calculate_adv_for_candidates(app, symbols, days=30, timeout=45):
    """
    Calculate Average Daily Volume for final candidate symbols only.
    This is a simplified approach used during the FINALIZING phase.
    
    Args:
        app: The IBApp instance with API connection
        symbols: List of symbols to calculate ADV for
        days: Number of days to use for average calculation
        timeout: Timeout in seconds per batch of symbols
        
    Returns:
        dict: Dictionary of calculated ADVs by symbol
    """
    from orbscanner.utils.phase.manager import PhaseManager
    from orbscanner.utils.logging.logging_utils import log_to_phase
    
    if not symbols:
        logging.info("No symbols provided for ADV calculation")
        return {}
    
    # Log the operation
    logging.info(f"Calculating ADV for {len(symbols)} final candidates")
    log_to_phase(PhaseManager.FINALIZING, f"Calculating ADV for {len(symbols)} final candidates", level=logging.INFO)
    
    # Initialize result dictionary and tracking structures
    results = {}
    completion_events = {}
    data_buffers = {}
    
    # Initialize tracking dictionaries in app if needed
    if not hasattr(app, 'adv_req_id_to_symbol'):
        app.adv_req_id_to_symbol = {}
    
    # Process symbols in small batches (5 at a time) to avoid overwhelming the API
    batch_size = 5
    symbol_batches = [symbols[i:i+batch_size] for i in range(0, len(symbols), batch_size)]
    
    for batch_idx, symbol_batch in enumerate(symbol_batches):
        batch_results = {}
        
        # Process this batch
        for symbol in symbol_batch:
            # Generate a request ID
            req_id = app.get_next_req_id()
            app.adv_req_id_to_symbol[req_id] = symbol
            
            # Create event and buffer for this request
            completion_events[req_id] = threading.Event()
            data_buffers[req_id] = []
            
            # Create contract
            contract = app.create_standard_contract(symbol)
            if not contract:
                logging.error(f"Failed to create contract for {symbol}")
                continue
            
            # Get end time (now)
            end_time = datetime.now().strftime("%Y%m%d %H:%M:%S")
            
            # Define callback for historical data
            def on_historical_data(reqId, bar):
                if reqId in data_buffers:
                    data_buffers[reqId].append(bar)
            
            # Define callback for historical data end
            def on_historical_data_end(reqId, start, end):
                if reqId in completion_events:
                    completion_events[reqId].set()
            
            # Set up temporary callbacks
            original_historical_data = app.historicalData
            original_historical_data_end = app.historicalDataEnd
            
            app.historicalData = on_historical_data
            app.historicalDataEnd = on_historical_data_end
            
            try:
                # Request historical data
                logging.info(f"Requesting ADV data for {symbol} (reqId: {req_id})")
                app.reqHistoricalData(
                    req_id,
                    contract,
                    end_time,
                    f"{days} D",  # Duration string
                    "1 day",      # Bar size
                    "TRADES",     # What to show
                    1,            # Use regular trading hours
                    1,            # Date format (1 = yyyymmdd)
                    False,        # Keep up to date
                    []            # Chart options
                )
            except Exception as e:
                logging.error(f"Error requesting ADV data for {symbol}: {e}")
                continue
        
        # Wait for all requests in this batch to complete or timeout
        batch_start_time = time.time()
        for req_id in [rid for rid, sym in app.adv_req_id_to_symbol.items() if sym in symbol_batch]:
            if req_id in completion_events:
                # Wait with timeout
                completion_events[req_id].wait(timeout)
                
                # Process data if available
                if req_id in data_buffers and data_buffers[req_id]:
                    symbol = app.adv_req_id_to_symbol[req_id]
                    bars = data_buffers[req_id]
                    
                    # Calculate average volume
                    if bars:
                        # Sort bars by date (newest first)
                        sorted_bars = sorted(bars, key=lambda bar: bar.date, reverse=True)
                        
                        # Calculate average volume
                        total_volume = sum(bar.volume for bar in sorted_bars)
                        avg_volume = total_volume / len(sorted_bars) if sorted_bars else 0
                        
                        # Store result
                        batch_results[symbol] = avg_volume
                        logging.info(f"ADV for {symbol}: {avg_volume:.0f} (over {len(sorted_bars)} days)")
                    else:
                        logging.warning(f"No historical data received for {symbol}")
        
        # Restore original callbacks
        app.historicalData = original_historical_data
        app.historicalDataEnd = original_historical_data_end
        
        # Update results with batch results
        results.update(batch_results)
        
        # Small delay between batches to avoid overwhelming API
        if batch_idx < len(symbol_batches) - 1:
            time.sleep(5)  # 5 second delay between batches
    
    # Log summary
    successful_count = len(results)
    logging.info(f"Successfully calculated ADV for {successful_count}/{len(symbols)} symbols")
    log_to_phase(PhaseManager.FINALIZING, f"Calculated ADV for {successful_count}/{len(symbols)} final candidates", level=logging.INFO)
    
    return results

def integrate_adv_with_candidates(app, adv_data):
    """
    Integrate calculated ADV data with candidate objects and opening_range_data.
    
    Args:
        app: The IBApp instance
        adv_data: Dictionary of ADV by symbol
        
    Returns:
        int: Number of symbols updated
    """
    from orbscanner.utils.phase.manager import PhaseManager
    from orbscanner.utils.logging.logging_utils import log_to_phase
    
    updated_count = 0
    
    # Ensure avg_volumes exists
    if not hasattr(app, 'avg_volumes'):
        app.avg_volumes = {}
    
    # Update app.avg_volumes
    app.avg_volumes.update(adv_data)
    
    # Update opening_range_data
    if hasattr(app, 'opening_range_data'):
        for symbol, adv in adv_data.items():
            if symbol in app.opening_range_data and adv > 0:
                app.opening_range_data[symbol]['avg_volume'] = adv
                updated_count += 1
    
    # Update candidate objects
    candidate_lists = []
    if hasattr(app, 'longCandidates'):
        candidate_lists.append(app.longCandidates)
    if hasattr(app, 'shortCandidates'):
        candidate_lists.append(app.shortCandidates)
    
    for candidate_list in candidate_lists:
        for candidate in candidate_list:
            symbol = candidate.get('symbol')
            if symbol and symbol in adv_data and adv_data[symbol] > 0:
                candidate['avg_volume'] = adv_data[symbol]
    
    logging.info(f"Integrated ADV data for {updated_count} symbols")
    log_to_phase(PhaseManager.FINALIZING, f"Integrated ADV data for {updated_count} symbols", level=logging.INFO)
    
    return updated_count
