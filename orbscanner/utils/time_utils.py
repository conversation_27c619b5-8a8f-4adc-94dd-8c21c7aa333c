"""
Time utility functions for the ORB Scanner.

This module provides consistent timezone handling for the ORB Scanner,
ensuring that all datetime operations use the same timezone (US/Eastern).
"""

import logging
from datetime import datetime, timedelta
import pytz

# Define the Eastern timezone as a constant for reuse
EASTERN_TZ = pytz.timezone('US/Eastern')

def get_eastern_time():
    """
    Get the current time in Eastern timezone.

    Returns:
        datetime: Current time in US/Eastern timezone
    """
    return datetime.now(EASTERN_TZ)

def convert_to_eastern(dt):
    """
    Convert any datetime to Eastern timezone with DST handling.

    Args:
        dt (datetime): The datetime object to convert

    Returns:
        datetime: The datetime converted to US/Eastern timezone

    Raises:
        TypeError: If dt is not a datetime object
        ValueError: If dt is ambiguous or non-existent due to DST
    """
    if not isinstance(dt, datetime):
        raise TypeError(f"Expected datetime object, got {type(dt)}")

    # If dt has no timezone info, localize it to Eastern
    if dt.tzinfo is None:
        try:
            # Handle ambiguous times during DST transitions
            return EASTERN_TZ.localize(dt, is_dst=None)
        except pytz.exceptions.AmbiguousTimeError:
            # Log warning and use is_dst=False as default
            logging.warning(f"Ambiguous time during DST transition: {dt}. Using is_dst=False.")
            return EASTERN_TZ.localize(dt, is_dst=False)
        except pytz.exceptions.NonExistentTimeError:
            # Log warning and add 1 hour to handle spring forward
            logging.warning(f"Non-existent time during DST transition: {dt}. Adding 1 hour.")
            return EASTERN_TZ.localize(dt + timedelta(hours=1))

    # If dt has timezone info, convert it to Eastern
    return dt.astimezone(EASTERN_TZ)

def get_time_diff_seconds(dt1, dt2):
    """
    Get the time difference between two datetimes in seconds.

    Args:
        dt1 (datetime): First datetime
        dt2 (datetime): Second datetime

    Returns:
        float: Time difference in seconds (dt1 - dt2)

    Raises:
        TypeError: If dt1 or dt2 is not a datetime object
    """
    if not isinstance(dt1, datetime) or not isinstance(dt2, datetime):
        raise TypeError(f"Expected datetime objects, got {type(dt1)} and {type(dt2)}")

    # Ensure both datetimes have timezone info
    dt1_eastern = convert_to_eastern(dt1)
    dt2_eastern = convert_to_eastern(dt2)

    # Calculate the difference
    diff = dt1_eastern - dt2_eastern
    return diff.total_seconds()

def format_timestamp(dt):
    """
    Format a datetime with timezone information.

    Args:
        dt (datetime): The datetime to format

    Returns:
        str: Formatted datetime string with timezone information

    Raises:
        TypeError: If dt is not a datetime object
    """
    if dt is None:
        return "N/A"
        
    if not isinstance(dt, datetime):
        raise TypeError(f"Expected datetime object, got {type(dt)}")

    # Ensure the datetime has timezone information
    dt_eastern = convert_to_eastern(dt)

    # Format with timezone information
    return dt_eastern.strftime('%Y-%m-%d %H:%M:%S %Z')

def is_market_open(dt=None):
    """
    Check if the market is open at the given time.

    Args:
        dt (datetime, optional): The datetime to check. Defaults to current time.

    Returns:
        bool: True if market is open, False otherwise
    """
    if dt is None:
        dt = get_eastern_time()
    else:
        dt = convert_to_eastern(dt)

    # Market is open 9:30 AM - 4:00 PM Eastern, Monday-Friday
    # Check if it's a weekday (0 = Monday, 4 = Friday)
    if dt.weekday() > 4:  # Saturday or Sunday
        return False

    # Check if it's between 9:30 AM and 4:00 PM
    market_open = dt.replace(hour=9, minute=30, second=0, microsecond=0)
    market_close = dt.replace(hour=16, minute=0, second=0, microsecond=0)

    return market_open <= dt < market_close

def get_market_open_time(dt=None):
    """
    Get the market open time (9:30 AM Eastern) for the given date.

    Args:
        dt (datetime, optional): The date to get market open time for. Defaults to today.

    Returns:
        datetime: Market open time (9:30 AM Eastern)
    """
    if dt is None:
        dt = get_eastern_time()
    else:
        dt = convert_to_eastern(dt)

    # Set time to 9:30 AM
    return dt.replace(hour=9, minute=30, second=0, microsecond=0)

def get_market_close_time(dt=None):
    """
    Get the market close time (4:00 PM Eastern) for the given date.

    Args:
        dt (datetime, optional): The date to get market close time for. Defaults to today.

    Returns:
        datetime: Market close time (4:00 PM Eastern)
    """
    if dt is None:
        dt = get_eastern_time()
    else:
        dt = convert_to_eastern(dt)

    # Set time to 4:00 PM
    return dt.replace(hour=16, minute=0, second=0, microsecond=0)
