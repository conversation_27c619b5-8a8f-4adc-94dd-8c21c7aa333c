import os
import json
import csv
import logging
from datetime import datetime, timedelta
import pytz
import pandas as pd
import numpy as np
from pathlib import Path
import time

class TradeLogger:
    """
    Logs trades to CSV files with comprehensive breakout data.
    Enhanced to capture bid/ask spreads, volume analytics, and timing data.
    """

    def __init__(self, trades_file="trades.csv", logger=None):
        """
        Initialize the trade logger.

        Args:
            trades_file (str): Path to the trades CSV file
            logger (logging.Logger, optional): Logger instance
        """
        self.trades_file = trades_file
        self.logger = logger
        self.active_trades = {}  # Store active trades by trade_id
        self.trade_counter = 0
        
        # Ensure the trades file exists with headers
        self._ensure_trades_file()

    def _ensure_trades_file(self):
        """Ensure the trades CSV file exists with proper headers"""
        if not os.path.exists(self.trades_file):
            # Define comprehensive CSV headers including new fields
            headers = [
                'trade_id', 'timestamp', 'symbol', 'direction', 'entry_price',
                'opening_high', 'opening_low', 'volume', 'scanners', 'priority_score',
                'vix', 'sector', 'status',
                # Enhanced fields (NEW - all optional)
                'volume_ratio', 'volume_baseline', 'baseline_source', 'range_width_pct',
                'elapsed_minutes', 'breakout_buffer_pct', 'distance_from_threshold',
                'bid_price', 'ask_price', 'spread', 'avg_volume',
                # Future price tracking fields
                'current_price', 'current_volume', 'last_update',
                'max_price', 'min_price', 'pnl_pct'
            ]
            
            with open(self.trades_file, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(headers)
            
            if self.logger:
                self.logger.info(f"Created trades file: {self.trades_file}")

    def log_breakout(self, symbol, direction, entry_price, opening_high, opening_low, 
                    volume, scanners, priority_score, vix=None, sector='Unknown',
                    # Enhanced optional parameters (NEW)
                    volume_ratio=None, volume_baseline=None, baseline_source=None,
                    range_width_pct=None, elapsed_minutes=None, breakout_buffer_pct=None,
                    distance_from_threshold=None, bid_price=None, ask_price=None,
                    spread=None, avg_volume=None):
        """
        Log a breakout trade with comprehensive data.

        Args:
            symbol (str): Stock symbol
            direction (str): 'LONG' or 'SHORT'
            entry_price (float): Entry price
            opening_high (float): Opening range high
            opening_low (float): Opening range low
            volume (int): Current volume
            scanners (list): List of scanners that found this symbol
            priority_score (float): Priority score from scanners
            vix (float, optional): VIX level
            sector (str, optional): Stock sector
            # Enhanced parameters (all optional)
            volume_ratio (float, optional): Volume surge ratio
            volume_baseline (float, optional): Volume baseline used
            baseline_source (str, optional): Source of volume baseline
            range_width_pct (float, optional): Opening range width percentage
            elapsed_minutes (float, optional): Minutes since market open
            breakout_buffer_pct (float, optional): Breakout buffer percentage used
            distance_from_threshold (float, optional): How far past breakout threshold
            bid_price (float, optional): Bid price at breakout
            ask_price (float, optional): Ask price at breakout
            spread (float, optional): Bid-ask spread
            avg_volume (float, optional): Average daily volume

        Returns:
            str: Trade ID if successful, None if failed
        """
        try:
            # Generate unique trade ID
            self.trade_counter += 1
            trade_id = f"T{int(time.time())}-{self.trade_counter:03d}"
            
            # Get current timestamp
            eastern = pytz.timezone('US/Eastern')
            timestamp = datetime.now(eastern).strftime('%Y-%m-%d %H:%M:%S')
            
            # Convert scanners list to string if needed
            if isinstance(scanners, list):
                scanners_str = ','.join(scanners)
            else:
                scanners_str = str(scanners) if scanners else ''
            
            # Prepare trade data with defensive handling of new fields
            trade_data = [
                trade_id, timestamp, symbol, direction, entry_price,
                opening_high, opening_low, volume, scanners_str, priority_score,
                vix or '', sector, 'ACTIVE',
                # Enhanced fields - use defensive defaults
                volume_ratio if volume_ratio is not None else '',
                volume_baseline if volume_baseline is not None else '',
                baseline_source or '',
                range_width_pct if range_width_pct is not None else '',
                elapsed_minutes if elapsed_minutes is not None else '',
                breakout_buffer_pct if breakout_buffer_pct is not None else '',
                distance_from_threshold if distance_from_threshold is not None else '',
                bid_price if bid_price is not None else '',
                ask_price if ask_price is not None else '',
                spread if spread is not None else '',
                avg_volume if avg_volume is not None else '',
                # Price tracking fields (initially empty)
                entry_price, volume, timestamp,  # current_price, current_volume, last_update
                entry_price, entry_price, 0.0    # max_price, min_price, pnl_pct
            ]
            
            # Write to CSV file
            with open(self.trades_file, 'a', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(trade_data)
            
            # Store in active trades for price updates
            self.active_trades[trade_id] = {
                'symbol': symbol,
                'direction': direction,
                'entry_price': entry_price,
                'current_price': entry_price,
                'max_price': entry_price,
                'min_price': entry_price,
                'timestamp': timestamp
            }
            
            if self.logger:
                # Enhanced logging with new fields when available
                enhanced_info = []
                if volume_ratio is not None:
                    enhanced_info.append(f"VR:{volume_ratio:.2f}x")
                if baseline_source:
                    enhanced_info.append(f"VS:{baseline_source}")
                if bid_price is not None and ask_price is not None:
                    enhanced_info.append(f"B/A:{bid_price:.2f}/{ask_price:.2f}")
                if spread is not None:
                    enhanced_info.append(f"Spread:{spread:.3f}")
                
                enhanced_str = f" [{', '.join(enhanced_info)}]" if enhanced_info else ""
                
                self.logger.info(f"TRADE_LOGGED: {trade_id} {symbol} {direction} @ {entry_price:.2f}{enhanced_str}")
            
            return trade_id
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to log breakout for {symbol}: {e}")
            return None

    def update_price(self, trade_id, current_price, current_volume=None):
        """
        Update the current price for an active trade.

        Args:
            trade_id (str): Trade ID
            current_price (float): Current price
            current_volume (int, optional): Current volume
        """
        if trade_id not in self.active_trades:
            return
        
        trade = self.active_trades[trade_id]
        trade['current_price'] = current_price
        
        # Update max/min prices
        if current_price > trade['max_price']:
            trade['max_price'] = current_price
        if current_price < trade['min_price']:
            trade['min_price'] = current_price
        
        # Calculate P&L percentage
        entry_price = trade['entry_price']
        if trade['direction'] == 'LONG':
            pnl_pct = ((current_price / entry_price) - 1) * 100
        else:  # SHORT
            pnl_pct = ((entry_price / current_price) - 1) * 100
        
        trade['pnl_pct'] = pnl_pct
        trade['last_update'] = datetime.now(pytz.timezone('US/Eastern')).strftime('%Y-%m-%d %H:%M:%S')

    def get_active_trades(self):
        """Get all active trades"""
        return self.active_trades.copy()

    def close_trade(self, trade_id, exit_price, reason='MANUAL'):
        """
        Close an active trade.

        Args:
            trade_id (str): Trade ID
            exit_price (float): Exit price
            reason (str): Reason for closing
        """
        if trade_id in self.active_trades:
            # Calculate final P&L
            trade = self.active_trades[trade_id]
            entry_price = trade['entry_price']
            
            if trade['direction'] == 'LONG':
                pnl_pct = ((exit_price / entry_price) - 1) * 100
            else:  # SHORT
                pnl_pct = ((entry_price / exit_price) - 1) * 100
            
            # Log closure (would need to update CSV with exit data)
            if self.logger:
                self.logger.info(f"TRADE_CLOSED: {trade_id} {trade['symbol']} @ {exit_price:.2f} "
                               f"P&L: {pnl_pct:+.2f}% Reason: {reason}")
            
            # Remove from active trades
            del self.active_trades[trade_id]

    def get_trade_summary(self):
        """Get summary of all active trades"""
        if not self.active_trades:
            return "No active trades"
        
        summary = []
        for trade_id, trade in self.active_trades.items():
            pnl = trade.get('pnl_pct', 0)
            summary.append(f"{trade['symbol']}({trade['direction'][0]}): {pnl:+.1f}%")
        
        return f"Active trades ({len(self.active_trades)}): " + ", ".join(summary)
