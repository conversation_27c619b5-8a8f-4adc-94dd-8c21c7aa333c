import logging
import time
from datetime import datetime
import pytz

from orbscanner.core.trade_logger import TradeLogger

class BreakoutDetector:
    """
    Detects breakouts from opening range and generates trade signals.
    Works with the TradeLogger to record trades when breakouts are detected.
    """

    def __init__(self, trade_logger, vix_level=None, logger=None, app=None, enable_volume_fallback=False):  # Added enable_volume_fallback parameter
        """
        Initialize the breakout detector.

        Args:
            trade_logger: TradeLogger instance for logging trades
            vix_level (float, optional): Current VIX level
            logger (logging.Logger, optional): Logger instance to use. Defaults to module logger.
            app (object, optional): Application instance for accessing global data like avg_volumes
            enable_volume_fallback (bool, optional): Enable conservative volume fallback when ADV data is missing
        """
        if logger:
            self.logger = logger
        else:
            self.logger = logging.getLogger(__name__)
        self.trade_logger = trade_logger
        self.vix_level = vix_level
        self.breakout_signals = {}  # Store detected breakouts
        self.monitored_symbols = {}  # Symbols being monitored
        self.app = app  # Store reference to app for accessing global data
        self.enable_volume_fallback = enable_volume_fallback  # Store fallback configuration

        # Configuration
        self.volume_threshold = 1.5  # Minimum volume surge ratio
        self.min_range_width_pct = 0.5  # Minimum opening range width (%)
        self.max_range_width_pct = 3.0  # Maximum opening range width (%)
        self.breakout_buffer_pct = 0.1  # Buffer above/below range to confirm breakout
        self.volume_sanity_threshold = 0.7  # Minimum current volume relative to pre-market volume

        # Adjust parameters based on VIX
        self._adjust_parameters_for_vix()

        self.logger.info(f"Breakout detector initialized with VIX level: {vix_level}")
        self.logger.info(f"Volume threshold: {self.volume_threshold}x, Range width: {self.min_range_width_pct}%-{self.max_range_width_pct}%")
        self.logger.info(f"Volume sanity threshold: 5.0% of time-adjusted Average Daily Volume (ADV)")
        self.logger.info(f"Time-adjusted ADV scales with time of day (e.g., at 11:00 AM, only ~19% of full ADV is expected)")

    def _adjust_parameters_for_vix(self):
        """Adjust detection parameters based on VIX level"""
        if self.vix_level is None:
            return

        # Higher VIX = more volatility = stricter criteria
        if self.vix_level > 35:
            self.volume_threshold = 4.0  # Require stronger volume confirmation
            self.min_range_width_pct = 0.8  # Wider minimum range
            self.max_range_width_pct = 4.0  # Allow wider ranges
            self.breakout_buffer_pct = 0.2  # Larger buffer for confirmation
            self.volume_sanity_threshold = 0.9  # Stricter volume sanity check
            self.logger.info(f"High VIX ({self.vix_level}): Using stricter breakout criteria")
        elif self.vix_level > 25:
            self.volume_threshold = 2.5
            self.min_range_width_pct = 0.6
            self.max_range_width_pct = 3.5
            self.breakout_buffer_pct = 0.15
            self.volume_sanity_threshold = 0.8  # Stricter volume sanity check
            self.logger.info(f"Elevated VIX ({self.vix_level}): Using adjusted breakout criteria")

    def add_symbol_to_monitor(self, symbol, opening_range_data, scanner_data):
        """
        Add a symbol to the monitoring list.

        Args:
            symbol (str): Stock symbol
            opening_range_data (dict): Opening range data (high, low, volume)
            scanner_data (dict): Scanner data for this symbol

        Returns:
            bool: True if added successfully
        """
        # Extended logging - record candidate details before filtering
        candidate_type = scanner_data.get('candidate_type', 'UNKNOWN') if isinstance(scanner_data, dict) else 'UNKNOWN'
        self.logger.info(f"MONITOR_FILTER: Processing candidate {symbol} ({candidate_type}) for monitoring")
        
        # CRITICAL FIX: Check for ADV data in app if available
        avg_volume = opening_range_data.get('avg_volume', 0)
        if avg_volume <= 0 and hasattr(self, 'app') and self.app:
            # Try to get ADV from app.avg_volumes
            if hasattr(self.app, 'avg_volumes') and symbol in self.app.avg_volumes:
                avg_volume = self.app.avg_volumes.get(symbol, 0)
                if avg_volume > 0:
                    opening_range_data['avg_volume'] = avg_volume
                    self.logger.info(f"VOLUME_FIX: Added missing ADV for {symbol}: {avg_volume:,.0f} during symbol monitoring setup")

        # Missing opening range data check
        if not opening_range_data or 'high' not in opening_range_data or 'low' not in opening_range_data:
            self.logger.warning(f"MONITOR_FILTER: Rejected {symbol}: Missing opening range data fields {list(opening_range_data.keys() if opening_range_data else 'None')}")
            return False

        # Calculate range width percentage
        high = opening_range_data['high']
        low = opening_range_data['low']

        if low <= 0:
            self.logger.warning(f"MONITOR_FILTER: Rejected {symbol}: Invalid opening range (low: {low}, high: {high})")
            return False

        range_width_pct = ((high - low) / low) * 100

        # Log all range details regardless of acceptance
        self.logger.info(f"MONITOR_FILTER: {symbol} range details - Low: {low:.2f}, High: {high:.2f}, Width: {range_width_pct:.2f}%, " +
                        f"Volume: {opening_range_data.get('volume', 'unknown')}, " +
                        f"Limits: [{self.min_range_width_pct:.2f}% - {self.max_range_width_pct:.2f}%]")

        # Check if range width is within acceptable limits
        if range_width_pct < self.min_range_width_pct:
            self.logger.warning(f"MONITOR_FILTER: Rejected {symbol}: Range width too narrow ({range_width_pct:.2f}% < {self.min_range_width_pct:.2f}%)")
            return False

        if range_width_pct > self.max_range_width_pct:
            self.logger.warning(f"MONITOR_FILTER: Rejected {symbol}: Range width too wide ({range_width_pct:.2f}% > {self.max_range_width_pct:.2f}%)")
            return False

        # Store symbol data
        self.monitored_symbols[symbol] = {
            'opening_high': high,
            'opening_low': low,
            'range_width_pct': range_width_pct,
            'avg_volume': opening_range_data.get('avg_volume', 0),
            'opening_volume': opening_range_data.get('volume', 0),
            'last_price': opening_range_data.get('last_price', 0),
            'last_volume': opening_range_data.get('volume', 0),
            'scanner_data': scanner_data,
            'last_check_time': datetime.now(pytz.timezone('US/Eastern')),
            'breakout_detected': False,
            'candidate_type': scanner_data.get('candidate_type', 'UNKNOWN'),
            'initial_price': opening_range_data.get('last_price', 0),
            'price_history': [],
            'volume_history': []
        }

        self.logger.info(f"MONITOR_FILTER: Accepted {symbol} ({candidate_type}) for monitoring: Range {low:.2f}-{high:.2f} ({range_width_pct:.2f}%)")
        return True

    def check_for_breakouts(self, symbol_prices):
        """
        Check for breakouts among monitored symbols.

        Args:
            symbol_prices (dict): Dictionary of current prices and volumes
                {symbol: {'price': float, 'volume': int}}

        Returns:
            list: List of breakout signals detected
        """
        breakouts = []

        # Initialize volume sanity check tracking if it doesn't exist
        if not hasattr(self, 'volume_sanity_failures'):
            self.volume_sanity_failures = {}

        # Initialize last warning time tracking if it doesn't exist
        if not hasattr(self, 'last_volume_warning_time'):
            self.last_volume_warning_time = {}

        for symbol, data in self.monitored_symbols.items():
            # Skip if symbol not in price data
            if symbol not in symbol_prices:
                continue

            # Always update price data even after breakout detection
            current_data = symbol_prices[symbol]
            current_price = current_data.get('price', 0)
            current_volume = current_data.get('volume', 0)
            
            # Skip if invalid price or volume
            if current_price <= 0 or current_volume <= 0:
                continue

            # Update price data and record history
            self.update_price_data(symbol, current_data)
                
            # Skip breakout detection if breakout already detected
            if data['breakout_detected']:
                continue

            # Check for volume surge
            opening_volume = data['opening_volume']
            avg_volume = data['avg_volume']

            # Use the larger of opening volume or average volume as baseline
            volume_baseline = max(opening_volume, avg_volume)
            baseline_source = "normal"
            
            # CONDITIONAL FALLBACK: Only use if ENABLE_VOLUME_FALLBACK is True
            if volume_baseline <= 0:
                # Log the problematic state for analysis
                self.logger.warning(f"VOLUME_BASELINE_ISSUE: {symbol} - opening_volume: {opening_volume:,}, avg_volume: {avg_volume:,}")
                
                # Check if fallback is enabled in configuration
                enable_fallback = getattr(self, 'enable_volume_fallback', True)  # Default to True if not set
                
                if enable_fallback:
                    # Conservative fallback using scanner minimum requirements
                    conservative_daily_volume = 500000  # From scanner filter "avgVolumeAbove": "500000"
                    
                    # Time-adjust the baseline (realistic expectation for time of day)
                    eastern = pytz.timezone('US/Eastern')
                    current_time = datetime.now(eastern)
                    market_open_time = datetime(
                        current_time.year, current_time.month, current_time.day,
                        9, 30, 0, tzinfo=eastern
                    )
                    elapsed_minutes = max(1, (current_time - market_open_time).total_seconds() / 60)
                    time_factor = min(1.0, elapsed_minutes / 390)  # 390 minutes in trading day
                    
                    # Conservative: expect 20% of proportional daily volume by this time
                    expected_volume_by_now = conservative_daily_volume * time_factor * 0.2
                    fallback_baseline = max(expected_volume_by_now, 1000)  # Minimum 1000 shares
                    
                    # VALIDATION LOGGING: Record all calculation details
                    self.logger.info(f"VOLUME_FALLBACK_CALC: {symbol} - "
                                   f"scanner_min:{conservative_daily_volume:,}, "
                                   f"elapsed:{elapsed_minutes:.1f}min, "
                                   f"time_factor:{time_factor:.3f}, "
                                   f"expected_by_now:{expected_volume_by_now:.0f}, "
                                   f"fallback_baseline:{fallback_baseline:.0f}")
                    
                    # Apply the fallback
                    volume_baseline = fallback_baseline
                    baseline_source = "fallback"
                    
                    self.logger.warning(f"VOLUME_BASELINE_FALLBACK: {symbol} using conservative baseline {volume_baseline:.0f} "
                                      f"(based on scanner minimum {conservative_daily_volume:,} * time {time_factor:.3f} * 20%)")
                else:
                    # Fallback disabled - use minimal baseline to prevent crashes but log the issue
                    volume_baseline = 1
                    baseline_source = "disabled"
                    self.logger.error(f"VOLUME_BASELINE_DISABLED: {symbol} has no volume data and fallback is disabled - "
                                    f"using minimal baseline {volume_baseline} (will result in 0.0x ratio)")
            else:
                baseline_source = "opening" if opening_volume >= avg_volume else "avg"
                self.logger.debug(f"VOLUME_BASELINE_OK: {symbol} using {baseline_source} baseline {volume_baseline:.0f}")
            
            # Calculate volume ratio with comprehensive logging
            volume_ratio = current_volume / volume_baseline if volume_baseline > 0 else 0
            
            # VALIDATION LOGGING: Always log the ratio calculation for analysis
            self.logger.info(f"VOLUME_RATIO_CALC: {symbol} = {current_volume:,} / {volume_baseline:.0f}({baseline_source}) = {volume_ratio:.2f}x")
            
            # TRUST VERIFICATION: Compare old vs new method when fallback is used
            if baseline_source == "fallback":
                old_baseline = max(opening_volume, avg_volume)  # This would be 0 or very small
                old_ratio = current_volume / old_baseline if old_baseline > 0 else 0
                
                # Log the comparison for trust-building
                self.logger.info(f"TRUST_COMPARISON: {symbol} - "
                               f"Old method: {current_volume:,}/{old_baseline:.0f}={old_ratio:.2f}x, "
                               f"New method: {current_volume:,}/{volume_baseline:.0f}={volume_ratio:.2f}x, "
                               f"Improvement: {volume_ratio:.2f}x vs {old_ratio:.2f}x")
                
                # Flag for manual review if the difference is significant
                if volume_ratio > 0.5 and old_ratio < 0.1:
                    self.logger.warning(f"TRUST_FLAG: {symbol} - Fallback method preventing likely false negative "
                                      f"(new: {volume_ratio:.2f}x vs old: {old_ratio:.2f}x)")

            # Get average daily volume if available
            avg_volume = data.get('avg_volume', 0)
            
            # CRITICAL FIX: If avg_volume is missing or zero, attempt to load it from multiple sources
            if avg_volume <= 0:
                # First try: Check if we have a reference to the app and it has avg_volumes
                if hasattr(self, 'app') and self.app and hasattr(self.app, 'avg_volumes') and symbol in self.app.avg_volumes:
                    avg_volume = self.app.avg_volumes.get(symbol, 0)
                    if avg_volume > 0:
                        # Update the stored value for future checks
                        self.monitored_symbols[symbol]['avg_volume'] = avg_volume
                        self.logger.info(f"VOLUME_FIX: Loaded missing ADV for {symbol}: {avg_volume:,.0f} from app.avg_volumes")
                
                # If still zero and we have opening_range_data, try that
                if avg_volume <= 0 and hasattr(self.app, 'opening_range_data') and symbol in self.app.opening_range_data:
                    range_avg_volume = self.app.opening_range_data[symbol].get('avg_volume', 0)
                    if range_avg_volume > 0:
                        avg_volume = range_avg_volume
                        self.monitored_symbols[symbol]['avg_volume'] = avg_volume
                        self.logger.info(f"VOLUME_FIX: Loaded missing ADV for {symbol}: {avg_volume:,.0f} from opening_range_data")

            # ALWAYS use ADV as the main method for volume comparison
            adv_threshold = 0.05  # 5% of ADV as minimum threshold

            # Get current time to calculate time-adjusted ADV
            eastern = pytz.timezone('US/Eastern')
            current_time = datetime.now(eastern)
            market_open_time = datetime(
                current_time.year, current_time.month, current_time.day,
                9, 30, 0, tzinfo=eastern
            )

            # Calculate minutes elapsed since market open
            elapsed_minutes = max(0, (current_time - market_open_time).total_seconds() / 60)
            total_session_minutes = 390  # 6.5 hours = 390 minutes

            # Calculate time-adjusted ADV (expected volume so far today)
            time_factor = min(1.0, elapsed_minutes / total_session_minutes)
            expected_volume_so_far = avg_volume * time_factor

            # Calculate required volume based on time-adjusted ADV
            required_volume = expected_volume_so_far * adv_threshold

            # Log the first time we use ADV for a symbol
            if symbol not in getattr(self, 'adv_used_symbols', set()):
                if not hasattr(self, 'adv_used_symbols'):
                    self.adv_used_symbols = set()
                self.adv_used_symbols.add(symbol)
                
                # Add diagnostic info about ADV source
                adv_source = "unknown"
                if avg_volume > 0:
                    if 'avg_volume' in data and data['avg_volume'] == avg_volume:
                        adv_source = "monitored_symbols"
                    elif hasattr(self, 'app') and self.app and hasattr(self.app, 'avg_volumes') and symbol in self.app.avg_volumes and self.app.avg_volumes[symbol] == avg_volume:
                        adv_source = "app.avg_volumes"
                    elif hasattr(self, 'app') and self.app and hasattr(self.app, 'opening_range_data') and symbol in self.app.opening_range_data and self.app.opening_range_data[symbol].get('avg_volume') == avg_volume:
                        adv_source = "opening_range_data"
                
                self.logger.info(f"Using time-adjusted ADV for {symbol}: {required_volume:.0f} (5% of expected volume at {elapsed_minutes:.0f}min: {expected_volume_so_far:.0f}, full ADV: {avg_volume:.0f}, source: {adv_source})")

            # CRITICAL FIX: Fall back to pre-market volume if ADV is still not available after all attempts
            if avg_volume <= 0:
                # Fall back to pre-market volume if ADV is not available
                required_volume = opening_volume * self.volume_sanity_threshold
                self.logger.warning(f"VOLUME_FALLBACK: No ADV available for {symbol}, falling back to pre-market volume: {opening_volume:,.0f} * {self.volume_sanity_threshold} = {required_volume:.0f}")

            # Volume sanity check: Reject candidates where current_volume < required_volume
            # This prevents fade-outs where volume dries up after the open
            if required_volume > 0 and current_volume < required_volume:
                # Calculate the actual percentage based on time-adjusted ADV
                actual_percentage = (current_volume / expected_volume_so_far) * 100 if expected_volume_so_far > 0 else 0
                threshold_type = "time-adjusted ADV"
                threshold_value = expected_volume_so_far
                threshold_pct = adv_threshold * 100
                time_percentage = (elapsed_minutes / total_session_minutes) * 100

                # CRITICAL FIX: Use pre-market volume comparison if ADV is not available
                if avg_volume <= 0:
                    actual_percentage = (current_volume / opening_volume) * 100 if opening_volume > 0 else 0
                    threshold_type = "pre-market"
                    threshold_value = opening_volume
                    threshold_pct = self.volume_sanity_threshold * 100
                    self.logger.info(f"VOLUME_COMPARISON: Using pre-market volume for {symbol} due to missing ADV")

                current_time = time.time()
                # Only log message if first occurrence or if it's been at least 60 seconds since last warning for this symbol
                should_log = False

                if symbol not in self.volume_sanity_failures:
                    # First occurrence of this failure
                    should_log = True
                    self.volume_sanity_failures[symbol] = {
                        'count': 1,
                        'first_time': current_time,
                        'last_percentage': actual_percentage,
                        'threshold_type': threshold_type
                    }
                    self.last_volume_warning_time[symbol] = current_time
                else:
                    # Increment failure count
                    self.volume_sanity_failures[symbol]['count'] += 1

                    # Check if we should log again (at least 60 seconds since last warning)
                    if symbol not in self.last_volume_warning_time or (current_time - self.last_volume_warning_time[symbol]) >= 60:
                        should_log = True
                        # Ready to log a new warning

                        # Update last warning time and percentage
                        self.last_volume_warning_time[symbol] = current_time
                        self.volume_sanity_failures[symbol]['last_percentage'] = actual_percentage
                        self.volume_sanity_failures[symbol]['threshold_type'] = threshold_type

                if should_log:
                    # Either first occurrence or time to log another warning
                    if symbol in self.volume_sanity_failures and self.volume_sanity_failures[symbol]['count'] > 1:
                        # Not the first occurrence, include count and duration info
                        duration_secs = int(current_time - self.volume_sanity_failures[symbol]['first_time'])
                        self.logger.warning(f"VOLUME_FADE: {symbol} current:{current_volume:,} ({actual_percentage:.1f}%) < required:{threshold_pct:.0f}% of {threshold_type}:{threshold_value:,.0f} [At {elapsed_minutes:.0f}min ({time_percentage:.1f}% of day), Check #{self.volume_sanity_failures[symbol]['count']}, duration:{duration_secs}s]")
                    else:
                        # First occurrence, use original format
                        self.logger.warning(f"VOLUME_FADE: {symbol} current:{current_volume:,} ({actual_percentage:.1f}%) < required:{threshold_pct:.0f}% of {threshold_type}:{threshold_value:,.0f} [At {elapsed_minutes:.0f}min ({time_percentage:.1f}% of day)]")

                continue

            # Check if volume is sufficient
            volume_confirmed = volume_ratio >= self.volume_threshold
            
            # CRITICAL FIX: Log volume confirmation status with detailed diagnostics
            if volume_confirmed:
                self.logger.info(f"VOLUME_CONFIRMED: {symbol} volume ratio {volume_ratio:.1f}x >= threshold {self.volume_threshold:.1f}x (current: {current_volume:,}, baseline: {volume_baseline:,})")
            else:
                self.logger.info(f"VOLUME_INSUFFICIENT: {symbol} volume ratio {volume_ratio:.1f}x < threshold {self.volume_threshold:.1f}x (current: {current_volume:,}, baseline: {volume_baseline:,})")

            # Get opening range
            opening_high = data['opening_high']
            opening_low = data['opening_low']

            # Calculate breakout thresholds with buffer
            long_breakout_threshold = opening_high * (1 + self.breakout_buffer_pct/100)
            short_breakout_threshold = opening_low * (1 - self.breakout_buffer_pct/100)

            # For long candidates, check for upside breakout
            if data['candidate_type'] == 'LONG' and current_price > long_breakout_threshold and volume_confirmed:
                self.logger.info(f"LONG BREAKOUT: {symbol} broke above {opening_high:.2f} (Price: {current_price:.2f}, Volume: {volume_ratio:.1f}x)")

                # Log the breakout
                scanners = data['scanner_data'].get('scanners', '').split(',')
                priority_score = data['scanner_data'].get('priority_score', 0)
                sector = data['scanner_data'].get('sector', 'Unknown')

                # Calculate additional metrics for enhanced logging
                bid_price = current_data.get('bid')
                ask_price = current_data.get('ask')
                spread = (ask_price - bid_price) if (ask_price and bid_price) else None
                distance_from_threshold = ((current_price / long_breakout_threshold) - 1) * 100

                trade_id = self.trade_logger.log_breakout(
                    symbol=symbol,
                    direction='LONG',
                    entry_price=current_price,
                    opening_high=opening_high,
                    opening_low=opening_low,
                    volume=current_volume,
                    scanners=scanners,
                    priority_score=priority_score,
                    vix=self.vix_level,
                    sector=sector,
                    # Enhanced data - all from already calculated values
                    volume_ratio=volume_ratio,
                    volume_baseline=volume_baseline,
                    baseline_source=baseline_source,
                    range_width_pct=data['range_width_pct'],
                    elapsed_minutes=elapsed_minutes,
                    breakout_buffer_pct=self.breakout_buffer_pct,
                    distance_from_threshold=distance_from_threshold,
                    bid_price=bid_price,
                    ask_price=ask_price,
                    spread=spread,
                    avg_volume=avg_volume
                )

                if trade_id:
                    breakouts.append({
                        'trade_id': trade_id,
                        'symbol': symbol,
                        'direction': 'LONG',
                        'entry_price': current_price,
                        'opening_high': opening_high,
                        'opening_low': opening_low
                    })

                    # Mark as detected to avoid duplicate signals, but continue monitoring
                    self.monitored_symbols[symbol]['breakout_detected'] = True
                    self.monitored_symbols[symbol]['breakout_time'] = datetime.now(pytz.timezone('US/Eastern'))
                    self.monitored_symbols[symbol]['breakout_price'] = current_price

            # For short candidates, check for downside breakout
            elif data['candidate_type'] == 'SHORT' and current_price < short_breakout_threshold and volume_confirmed:
                self.logger.info(f"SHORT BREAKOUT: {symbol} broke below {opening_low:.2f} (Price: {current_price:.2f}, Volume: {volume_ratio:.1f}x)")

                # Log the breakout
                scanners = data['scanner_data'].get('scanners', '').split(',')
                priority_score = data['scanner_data'].get('priority_score', 0)
                sector = data['scanner_data'].get('sector', 'Unknown')

                # Calculate additional metrics for enhanced logging
                bid_price = current_data.get('bid')
                ask_price = current_data.get('ask')
                spread = (ask_price - bid_price) if (ask_price and bid_price) else None
                distance_from_threshold = ((current_price / short_breakout_threshold) - 1) * 100

                trade_id = self.trade_logger.log_breakout(
                    symbol=symbol,
                    direction='SHORT',
                    entry_price=current_price,
                    opening_high=opening_high,
                    opening_low=opening_low,
                    volume=current_volume,
                    scanners=scanners,
                    priority_score=priority_score,
                    vix=self.vix_level,
                    sector=sector,
                    # Enhanced data - all from already calculated values
                    volume_ratio=volume_ratio,
                    volume_baseline=volume_baseline,
                    baseline_source=baseline_source,
                    range_width_pct=data['range_width_pct'],
                    elapsed_minutes=elapsed_minutes,
                    breakout_buffer_pct=self.breakout_buffer_pct,
                    distance_from_threshold=distance_from_threshold,
                    bid_price=bid_price,
                    ask_price=ask_price,
                    spread=spread,
                    avg_volume=avg_volume
                )

                if trade_id:
                    breakouts.append({
                        'trade_id': trade_id,
                        'symbol': symbol,
                        'direction': 'SHORT',
                        'entry_price': current_price,
                        'opening_high': opening_high,
                        'opening_low': opening_low
                    })

                    # Mark as detected to avoid duplicate signals, but continue monitoring
                    self.monitored_symbols[symbol]['breakout_detected'] = True
                    self.monitored_symbols[symbol]['breakout_time'] = datetime.now(pytz.timezone('US/Eastern'))
                    self.monitored_symbols[symbol]['breakout_price'] = current_price

        return breakouts

    def update_prices(self, symbol_prices):
        """
        Update prices for all active trades.

        Args:
            symbol_prices (dict): Dictionary of current prices and volumes
                {symbol: {'price': float, 'volume': int}}
        """
        active_trades = self.trade_logger.get_active_trades()

        for trade_id, trade in active_trades.items():
            symbol = trade['symbol']

            if symbol in symbol_prices:
                current_price = symbol_prices[symbol].get('price', 0)
                current_volume = symbol_prices[symbol].get('volume', 0)

                if current_price > 0:
                    self.trade_logger.update_price(trade_id, current_price, current_volume)

    def get_monitored_symbols(self):
        """Get all symbols being monitored"""
        # Return a list of symbol names (keys) from the monitored_symbols dictionary
        if not self.monitored_symbols:
            return []

        # Return just the symbol names, not the full dictionary, including all symbols
        # regardless of breakout status (ensure continued monitoring)
        return list(self.monitored_symbols.keys())

    def get_breakout_signals(self):
        """Get all breakout signals detected"""
        return self.breakout_signals

    def report_detailed_status(self):
        """
        Generate a detailed status report of all monitored symbols
        including their current price, movement from opening range,
        and other relevant statistics.

        This should be called once per minute during the monitoring phase.
        """
        if not self.monitored_symbols:
            self.logger.info("MONITOR_STATUS: No symbols currently being monitored")
            return

        # Ready to report status for all monitored symbols

        for symbol, data in self.monitored_symbols.items():
            # Continue to report on all symbols, even after breakout detected
            # Previous condition was removing symbols from logs after breakout detection

            # Get basic data
            opening_high = data['opening_high']
            opening_low = data['opening_low']
            last_price = data['last_price']
            initial_price = data.get('initial_price', last_price) # Default to last_price if initial_price is not set
            candidate_type_full = data['candidate_type']
            candidate_type_short = candidate_type_full[0] if candidate_type_full else 'U' # U for UNKNOWN, L for LONG, S for SHORT

            # Calculate distances from range boundaries
            # Add checks for opening_high, opening_low, and initial_price being zero
            distance_from_high = ((last_price / opening_high) - 1) * 100 if opening_high > 0 else -100.0
            distance_from_low = ((last_price / opening_low) - 1) * 100 if opening_low > 0 else -100.0
            price_change_pct = ((last_price / initial_price) - 1) * 100 if initial_price > 0 else 0.0

            # Determine the closest boundary
            closest_boundary_char = ''
            if opening_high > 0 and opening_low > 0: # Ensure valid range before comparison
                if abs(distance_from_high) < abs(distance_from_low):
                    closest_boundary_char = "H"
                else:
                    closest_boundary_char = "L"
            elif opening_high > 0: # Only high is valid
                 closest_boundary_char = "H"
            elif opening_low > 0: # Only low is valid
                 closest_boundary_char = "L"

            # Get volume info
            last_volume = data.get('last_volume', 0)
            opening_volume = data.get('opening_volume', 1)  # Avoid division by zero by defaulting to 1
            volume_ratio = last_volume / opening_volume if opening_volume > 0 else 0

            # Check if breakout was detected
            breakout_info = ""
            if data.get('breakout_detected'):
                breakout_time = data.get('breakout_time', datetime.now(pytz.timezone('US/Eastern')))
                breakout_price = data.get('breakout_price', 0)
                price_since_breakout = ((last_price / breakout_price) - 1) * 100 if breakout_price > 0 else 0
                minutes_since_breakout = int((datetime.now(pytz.timezone('US/Eastern')) - breakout_time).total_seconds() / 60) if breakout_time else 0
                breakout_info = f" [BREAKOUT: {breakout_price:.2f}(Δ{price_since_breakout:+.1f}%/{minutes_since_breakout}min)]"

            # Log the status with rich information
            status_msg = (
                f"MONITOR_STATUS: {symbol}({candidate_type_short}) "
                f"P:{last_price:.2f}(Δ{price_change_pct:+.1f}%) "
                f"R:{opening_low:.2f}-{opening_high:.2f} "
                f"D H:{distance_from_high:.1f}% L:{distance_from_low:.1f}%({closest_boundary_char}) "
                f"V:{last_volume}({volume_ratio:.1f}x)"
                f"{breakout_info}"
            )
            self.logger.info(status_msg)

            # Store history for trend analysis
            price_history = data.get('price_history', [])
            price_history.append(last_price)
            data['price_history'] = price_history[-5:]

            volume_history = data.get('volume_history', [])
            volume_history.append(last_volume)
            data['volume_history'] = volume_history[-5:]

    def update_price_data(self, symbol, price_data):
        """
        Update price data for a monitored symbol and record history.

        Args:
            symbol (str): The symbol to update
            price_data (dict): Dict containing price and volume data
        """
        if symbol not in self.monitored_symbols:
            return

        current_price = price_data.get('price', 0)
        current_volume = price_data.get('volume', 0)

        # Skip invalid data
        if current_price <= 0:
            return

        # Update the data in monitored_symbols
        self.monitored_symbols[symbol]['last_price'] = current_price
        self.monitored_symbols[symbol]['last_volume'] = current_volume
        self.monitored_symbols[symbol]['last_check_time'] = datetime.now(pytz.timezone('US/Eastern'))

        # Set initial_price if it's not set yet or is zero
        if self.monitored_symbols[symbol].get('initial_price', 0) <= 0:
            self.monitored_symbols[symbol]['initial_price'] = current_price
            self.logger.debug(f"Setting initial price for {symbol} to {current_price:.2f}")
