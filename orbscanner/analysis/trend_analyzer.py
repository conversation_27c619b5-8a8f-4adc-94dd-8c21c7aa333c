"""
Trend Analysis module for the ORB Scanner.

This module provides functions for analyzing daily trend data, including VWAP and SMA200 calculations.
"""

import logging
import time
import threading
import math
import traceback
from datetime import datetime
import pytz
from ibapi.contract import Contract

from orbscanner.utils.logging.logging_utils import log_wrapper_call
from orbscanner.utils.data.tracing_integration import trace_data_request

# Constants
HISTORICAL_DATA_TIMEOUT = 30  # Timeout for historical data requests in seconds
HISTORICAL_DATA_RETRIES = 2   # Number of retries for historical data requests
MIN_DAYS = 20                 # Minimum number of days required for trend analysis
MAX_TIMEOUTS = 2              # Maximum number of timeouts allowed before excluding a symbol
MIN_VOLUME = 500000           # Minimum average daily volume required (500K shares)
MAX_SPREAD = 0.001            # Maximum bid-ask spread allowed (0.1%)
MIN_DOLLAR_VOLUME = 5000000   # Minimum dollar volume required ($5M)


def pre_screen_symbol(app, symbol):
    """
    Pre-screen a symbol to determine if it's eligible for trend data collection.

    Args:
        app: The IBApp instance
        symbol (str): The symbol to check

    Returns:
        tuple: (is_eligible, reason)
    """
    # Get symbol data
    symbol_data = app.get_symbol_data(symbol)

    # Check timeout history
    timeouts = app.timeout_history.get(symbol, 0)
    if timeouts >= MAX_TIMEOUTS:
        return False, f"Symbol {symbol} excluded: Too many timeouts ({timeouts} >= {MAX_TIMEOUTS})"

    # Check data availability (if we have this information)
    data_days = symbol_data.get('data_days', 0)
    if data_days > 0 and data_days < MIN_DAYS:
        return False, f"Symbol {symbol} excluded: Insufficient data ({data_days}d < {MIN_DAYS}d)"

    # Check volume (if available)
    volume = symbol_data.get('volume_30d_avg', 0)
    if volume > 0 and volume < MIN_VOLUME:
        # Track volume rejection
        app.rejected_candidates['volume_rejected'][symbol] = {
            'direction': 'UNKNOWN',
            'volume': volume,
            'threshold': MIN_VOLUME
        }
        return False, f"Symbol {symbol} excluded: Low volume ({volume} < {MIN_VOLUME})"

    # Enhanced spread check with dynamic logic
    price = symbol_data.get('last_price', 0)
    spread = symbol_data.get('spread_pct', float('inf'))

    # For low-priced stocks, use absolute spread instead of percentage
    if price < 20:
        absolute_spread = price * spread
        if absolute_spread > 0.02:  # $0.02 absolute spread for <$20 stocks
            # Track liquidity rejection
            app.rejected_candidates['liquidity_rejected'][symbol] = {
                'direction': 'UNKNOWN',
                'reason': f"Wide absolute spread (${absolute_spread:.3f} > $0.02)"
            }
            return False, f"Symbol {symbol} excluded: Wide absolute spread (${absolute_spread:.3f} > $0.02)"
    else:
        # For higher-priced stocks, use percentage spread
        if spread < float('inf') and spread > MAX_SPREAD:
            # Track liquidity rejection
            app.rejected_candidates['liquidity_rejected'][symbol] = {
                'direction': 'UNKNOWN',
                'reason': f"Wide percentage spread ({spread:.4f} > {MAX_SPREAD:.4f})"
            }
            return False, f"Symbol {symbol} excluded: Wide percentage spread ({spread:.4f} > {MAX_SPREAD:.4f})"

    # Check dollar volume (price * volume)
    price = symbol_data.get('last_price', 0)
    dollar_volume = price * volume
    if price > 0 and volume > 0 and dollar_volume < MIN_DOLLAR_VOLUME:
        # Track liquidity rejection
        app.rejected_candidates['liquidity_rejected'][symbol] = {
            'direction': 'UNKNOWN',
            'reason': f"Low dollar volume (${dollar_volume:,.0f} < ${MIN_DOLLAR_VOLUME:,.0f})"
        }
        return False, f"Symbol {symbol} excluded: Low dollar volume (${dollar_volume:,.0f} < ${MIN_DOLLAR_VOLUME:,.0f})"

    return True, "Symbol meets all criteria"


@trace_data_request(request_type="daily_trend_data")
def get_daily_trend_data(app, symbol):
    """
    Get daily trend data (VWAP, SMA200) for a symbol.

    Args:
        app: The IBApp instance
        symbol (str): The symbol to get trend data for

    Returns:
        dict: Dictionary with VWAP, SMA200, and trend direction data, or None if data retrieval failed
    """
    # Log wrapper call for method extraction verification
    log_wrapper_call("get_daily_trend_data", symbol)
    
    # Check if symbol requires subscription (if we have a subscription tracker)
    if hasattr(app, 'subscription_required_symbols') and symbol in app.subscription_required_symbols:
        logging.info(f"Symbol {symbol} excluded: Previously required subscription")
        return None

    # Pre-screen the symbol
    is_eligible, reason = pre_screen_symbol(app, symbol)
    if not is_eligible:
        logging.info(reason)
        return None

    logging.info(f"Requesting daily trend data for {symbol}")

    # Create contract
    contract = Contract()
    contract.symbol = symbol
    contract.secType = "STK"
    contract.exchange = "SMART"
    contract.currency = "USD"

    # Implement retry logic
    retry_count = 0
    max_retries = HISTORICAL_DATA_RETRIES

    # Check if we have a rate limiter
    has_rate_limiter = hasattr(app, 'rate_limiter')

    while retry_count < max_retries:
        # Create request ID and event
        req_id = app.get_next_req_id()
        app.daily_data_buffer[req_id] = []
        app.daily_data_finished[req_id] = False
        app.daily_data_events[req_id] = threading.Event()

        try:
            # Request 200 days of daily data for SMA200 calculation
            if retry_count == 0:
                logging.info(f"Requesting daily historical data for {symbol} (reqId: {req_id})")
            else:
                logging.info(f"Retry #{retry_count} for daily historical data for {symbol} (reqId: {req_id})")

            # Check rate limits if we have a rate limiter
            if has_rate_limiter:
                # Get the rate limiter instance
                rate_limiter = getattr(app, 'rate_limiter')

                # Wait if needed to comply with rate limits
                wait_time = rate_limiter.wait_if_needed(symbol)
                if wait_time > 0:
                    logging.info(f"Waited {wait_time:.1f}s for rate limit before requesting data for {symbol}")

            # Log detailed request information
            request_details = {
                "reqId": req_id,
                "symbol": symbol,
                "secType": contract.secType,
                "exchange": contract.exchange,
                "endDateTime": "",
                "durationStr": "200 D",
                "barSizeSetting": "1 day",
                "whatToShow": "TRADES",
                "timezone": "US/Eastern",
                "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
            }
            logging.info(f"API_REQUEST: {request_details}")

            # Make the request (timezone is included in endDateTime string)
            app.reqHistoricalData(
                reqId=req_id,
                contract=contract,
                endDateTime="",  # Current time (will be interpreted with US/Eastern timezone)
                durationStr="200 D",  # 200 days for SMA200
                barSizeSetting="1 day",
                whatToShow="TRADES",
                useRTH=1,
                formatDate=1,
                keepUpToDate=False,
                chartOptions=[]
            )

            # Record the request in the rate limiter
            if has_rate_limiter:
                rate_limiter.record_request(symbol)

            # Store the request ID to symbol mapping
            app.reqId_to_symbol[req_id] = symbol

            # Store request timestamp
            request_time = time.time()

            # Wait for data to arrive with timeout
            received = app.daily_data_events[req_id].wait(timeout=HISTORICAL_DATA_TIMEOUT)

            # Calculate response time
            response_time = time.time() - request_time

            if not received or not app.daily_data_finished.get(req_id, False):
                retry_count += 1

                # Update timeout history
                app.timeout_history[symbol] = app.timeout_history.get(symbol, 0) + 1
                current_timeouts = app.timeout_history[symbol]

                # Log detailed timeout information
                timeout_data = {
                    "reqId": req_id,
                    "symbol": symbol,
                    "attempt": retry_count,
                    "max_retries": max_retries,
                    "total_timeouts": current_timeouts,
                    "wait_time": HISTORICAL_DATA_TIMEOUT,
                    "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
                }
                logging.warning(f"TIMEOUT: {timeout_data}")

                # Clean up before retry
                if req_id in app.daily_data_buffer:
                    del app.daily_data_buffer[req_id]
                if req_id in app.daily_data_finished:
                    del app.daily_data_finished[req_id]
                if req_id in app.daily_data_events:
                    del app.daily_data_events[req_id]
                if req_id in app.reqId_to_symbol:
                    del app.reqId_to_symbol[req_id]

                # If we've reached max retries, give up on this symbol
                if retry_count >= max_retries:
                    logging.warning(f"Symbol {symbol} failed after {max_retries} attempts - skipping")
                    return None

                # Continue to next retry
                continue

            # Process the bars
            bars = app.daily_data_buffer.get(req_id, [])
            bars_count = len(bars) if bars else 0

            # Log successful response
            response_data = {
                "reqId": req_id,
                "symbol": symbol,
                "bars_count": bars_count,
                "response_time_ms": int(response_time * 1000),
                "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
            }
            logging.info(f"API_RESPONSE: {response_data}")

            if not bars or bars_count < 20:  # Need at least 20 days for meaningful data
                retry_count += 1

                # Update symbol data with the number of days available
                symbol_data = app.get_symbol_data(symbol)
                symbol_data['data_days'] = bars_count

                # Log detailed insufficient data information
                insufficient_data = {
                    "reqId": req_id,
                    "symbol": symbol,
                    "bars_count": bars_count,
                    "minimum_required": 20,
                    "attempt": retry_count,
                    "max_retries": max_retries,
                    "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
                }
                logging.warning(f"INSUFFICIENT_DATA: {insufficient_data}")

                # Clean up before retry
                if req_id in app.daily_data_buffer:
                    del app.daily_data_buffer[req_id]
                if req_id in app.daily_data_finished:
                    del app.daily_data_finished[req_id]
                if req_id in app.daily_data_events:
                    del app.daily_data_events[req_id]
                if req_id in app.reqId_to_symbol:
                    del app.reqId_to_symbol[req_id]

                # If we've reached max retries, give up on this symbol
                if retry_count >= max_retries:
                    logging.warning(f"Symbol {symbol} had insufficient data after {max_retries} attempts - skipping")
                    return None

                # Continue to next retry
                continue

            # If we got here, we have valid data, so break out of retry loop
            break

        except Exception as e:
            retry_count += 1

            # Log detailed error information
            error_data = {
                "reqId": req_id,
                "symbol": symbol,
                "attempt": retry_count,
                "max_retries": max_retries,
                "error": str(e),
                "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
            }
            logging.error(f"API_EXCEPTION: {error_data}")

            # Clean up before retry
            if req_id in app.daily_data_buffer:
                del app.daily_data_buffer[req_id]
            if req_id in app.daily_data_finished:
                del app.daily_data_finished[req_id]
            if req_id in app.daily_data_events:
                del app.daily_data_events[req_id]
            if req_id in app.reqId_to_symbol:
                del app.reqId_to_symbol[req_id]

            # If we've reached max retries, give up on this symbol
            if retry_count >= max_retries:
                logging.warning(f"Symbol {symbol} failed with errors after {max_retries} attempts - skipping")
                return None

            # Continue to next retry
            continue

    # If we get here, we have valid data
    bars = app.daily_data_buffer.get(req_id, [])

    try:
        # Calculate SMA200 (or as many days as available, minimum 20)
        sma_period = min(200, len(bars))
        sma_values = [float(bar.close) for bar in bars[:sma_period]]
        sma200 = sum(sma_values) / float(sma_period) if sma_period > 0 else None

        # Calculate VWAP for the last 20 trading days
        vwap_period = min(20, len(bars))
        vwap_bars = bars[:vwap_period]

        total_volume = sum(float(bar.volume) for bar in vwap_bars)
        if total_volume > 0:
            vwap_sum = sum(((float(bar.high) + float(bar.low) + float(bar.close)) / 3.0) * float(bar.volume) for bar in vwap_bars)
            vwap = float(vwap_sum) / float(total_volume)
        else:
            vwap = None

        # Get current price (most recent close)
        current_price = float(bars[0].close) if bars else None

        # Calculate price correlation with VWAP and SMA200
        price_vwap_correlation = 0.0
        price_sma200_correlation = 0.0

        # Calculate correlation if we have enough data (at least 5 days)
        if len(bars) >= 5:
            # Get recent prices for correlation calculation (last 20 days or as many as available)
            correlation_period = min(20, len(bars))
            recent_prices = [float(bar.close) for bar in bars[:correlation_period]]

            # Calculate VWAP values for each day
            vwap_values = []
            for i in range(correlation_period):
                day_bars = bars[i:i+1]  # Just the current day
                day_volume = sum(float(bar.volume) for bar in day_bars)
                if day_volume > 0:
                    day_vwap_sum = sum(((float(bar.high) + float(bar.low) + float(bar.close)) / 3.0) * float(bar.volume) for bar in day_bars)
                    day_vwap = float(day_vwap_sum) / float(day_volume)
                    vwap_values.append(day_vwap)
                else:
                    vwap_values.append(float(day_bars[0].close) if day_bars else 0)

            # Calculate SMA200 values for each day (or as many days as available)
            sma200_values = []
            for i in range(correlation_period):
                day_sma_period = min(200, len(bars) - i)
                if day_sma_period > 0:
                    day_sma_values = [float(bar.close) for bar in bars[i:i+day_sma_period]]
                    day_sma200 = sum(day_sma_values) / float(day_sma_period)
                    sma200_values.append(day_sma200)
                else:
                    sma200_values.append(0)

            # Calculate correlation if we have enough data
            if len(recent_prices) > 1 and len(vwap_values) > 1 and len(recent_prices) == len(vwap_values):
                try:
                    # Calculate mean and standard deviation for price and VWAP
                    price_mean = sum(recent_prices) / len(recent_prices)
                    vwap_mean = sum(vwap_values) / len(vwap_values)
                    price_std = (sum((p - price_mean) ** 2 for p in recent_prices) / len(recent_prices)) ** 0.5
                    vwap_std = (sum((v - vwap_mean) ** 2 for v in vwap_values) / len(vwap_values)) ** 0.5

                    # Calculate covariance and correlation
                    if price_std > 0 and vwap_std > 0:
                        covariance = sum((recent_prices[i] - price_mean) * (vwap_values[i] - vwap_mean) for i in range(len(recent_prices))) / len(recent_prices)
                        price_vwap_correlation = covariance / (price_std * vwap_std)
                except Exception as e:
                    logging.warning(f"Error calculating price-VWAP correlation for {symbol}: {e}")

            # Calculate correlation with SMA200 if we have enough data
            if len(recent_prices) > 1 and len(sma200_values) > 1 and len(recent_prices) == len(sma200_values):
                try:
                    # Calculate mean and standard deviation for price and SMA200
                    price_mean = sum(recent_prices) / len(recent_prices)
                    sma200_mean = sum(sma200_values) / len(sma200_values)
                    price_std = (sum((p - price_mean) ** 2 for p in recent_prices) / len(recent_prices)) ** 0.5
                    sma200_std = (sum((s - sma200_mean) ** 2 for s in sma200_values) / len(sma200_values)) ** 0.5

                    # Calculate covariance and correlation
                    if price_std > 0 and sma200_std > 0:
                        covariance = sum((recent_prices[i] - price_mean) * (sma200_values[i] - sma200_mean) for i in range(len(recent_prices))) / len(recent_prices)
                        price_sma200_correlation = covariance / (price_std * sma200_std)
                except Exception as e:
                    logging.warning(f"Error calculating price-SMA200 correlation for {symbol}: {e}")

        # Determine trend direction
        trend = "UNKNOWN"
        if current_price is not None and vwap is not None and sma200 is not None:
            # Above both VWAP and SMA200 = strong uptrend
            if current_price > vwap and current_price > sma200:
                trend = "STRONG_UP"
            # Above VWAP but below SMA200 = weak uptrend
            elif current_price > vwap and current_price <= sma200:
                trend = "WEAK_UP"
            # Below VWAP but above SMA200 = weak downtrend
            elif current_price <= vwap and current_price > sma200:
                trend = "WEAK_DOWN"
            # Below both VWAP and SMA200 = strong downtrend
            else:
                trend = "STRONG_DOWN"

        vwap_str = f"{vwap:.2f}" if vwap is not None else "N/A"
        sma200_str = f"{sma200:.2f}" if sma200 is not None else "N/A"
        vwap_corr_str = f"{price_vwap_correlation:.2f}" if price_vwap_correlation else "N/A"
        sma200_corr_str = f"{price_sma200_correlation:.2f}" if price_sma200_correlation else "N/A"

        logging.info(f"Daily trend data for {symbol}: VWAP={vwap_str}, SMA200={sma200_str}, Trend={trend}")
        logging.info(f"Correlation data for {symbol}: VWAP-Corr={vwap_corr_str}, SMA200-Corr={sma200_corr_str}")

        return {
            'vwap': float(vwap) if vwap is not None else None,
            'sma200': float(sma200) if sma200 is not None else None,
            'current_price': float(current_price) if current_price is not None else None,
            'trend': trend,
            'vwap_correlation': float(price_vwap_correlation) if price_vwap_correlation else 0.0,
            'sma200_correlation': float(price_sma200_correlation) if price_sma200_correlation else 0.0,
            'bars_analyzed': len(bars)
        }

    except Exception as e:
        logging.error(f"Error getting daily trend data for {symbol}: {e}")
        return None
    finally:
        # Clean up
        if req_id in app.daily_data_buffer:
            del app.daily_data_buffer[req_id]
        if req_id in app.daily_data_finished:
            del app.daily_data_finished[req_id]
        if req_id in app.daily_data_events:
            del app.daily_data_events[req_id]
        if req_id in app.reqId_to_symbol:
            del app.reqId_to_symbol[req_id]
