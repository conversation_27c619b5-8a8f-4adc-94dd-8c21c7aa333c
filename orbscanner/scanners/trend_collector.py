#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Trend Data Collector for the ORB Scanner.

This module provides functions for collecting trend data for candidate symbols
to ensure the finalizing report has complete trend information.
"""

import logging
import time
from datetime import datetime

def collect_trend_data_for_candidates(app, candidates):
    """
    Collect trend data for all candidate symbols to ensure the finalizing report
    has complete trend information.

    Args:
        app: The IBApp instance
        candidates: List of candidate dictionaries
    
    Returns:
        int: Number of symbols for which trend data was collected
    """
    from orbscanner.analysis.trend_analyzer import get_daily_trend_data
    
    logging.info(f"Collecting trend data for {len(candidates)} candidates")
    
    # Initialize daily_trend_data if it doesn't exist
    if not hasattr(app, 'daily_trend_data'):
        app.daily_trend_data = {}
        logging.info("Created app.daily_trend_data dictionary")
        
    symbols_processed = 0
    symbols_with_data = 0
    symbols_updated = 0
        
    for candidate in candidates:
        symbol = candidate.get('symbol')
        if not symbol or not isinstance(symbol, str):
            logging.warning(f"Invalid symbol in candidate: {symbol}")
            continue
            
        # Check if we already have trend data for this symbol
        if symbol in app.daily_trend_data:
            logging.info(f"Found existing trend data for {symbol}")
            # IMPROVEMENT: Copy existing trend data to candidate if not already there
            trend_data = app.daily_trend_data[symbol]
            if trend_data and 'trend' not in candidate:
                candidate['trend'] = trend_data.get('trend', 'N/A')
                candidate['trend_vwap'] = trend_data.get('vwap', 0)
                candidate['trend_sma200'] = trend_data.get('sma200', 0)
                candidate['vwap_correlation'] = trend_data.get('vwap_correlation', 0.0)
                symbols_updated += 1
                logging.info(f"Copied existing trend data to candidate for {symbol}: {trend_data.get('trend', 'N/A')}")
                
            symbols_with_data += 1
            continue
            
        # Get trend data
        logging.info(f"Collecting trend data for candidate: {symbol}")
        trend_data = get_daily_trend_data(app, symbol)
        symbols_processed += 1
        
        if trend_data:
            # Store the trend data
            app.daily_trend_data[symbol] = trend_data
            
            # Also store directly in the candidate for easy access
            candidate['trend'] = trend_data.get('trend', 'N/A')
            candidate['trend_vwap'] = trend_data.get('vwap', 0)
            candidate['trend_sma200'] = trend_data.get('sma200', 0) 
            candidate['vwap_correlation'] = trend_data.get('vwap_correlation', 0.0)
            
            symbols_with_data += 1
            logging.info(f"Successfully collected trend data for {symbol}: {trend_data.get('trend', 'N/A')}")
        else:
            logging.warning(f"Failed to collect trend data for {symbol}")
            
        # Add a short sleep to avoid overwhelming the API
        time.sleep(0.1)
    
    logging.info(f"Trend data collection complete: {symbols_with_data}/{len(candidates)} symbols have trend data")
    logging.info(f"Processed {symbols_processed} symbols, reused data for {len(candidates) - symbols_processed} symbols, updated {symbols_updated} candidates")
    
    return symbols_with_data
