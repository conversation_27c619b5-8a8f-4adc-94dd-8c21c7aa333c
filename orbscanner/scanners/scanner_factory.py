#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Scanner Factory for the ORB Scanner.

This module provides functions for creating and managing scanner subscriptions,
including scanner type selection, filter configuration, and subscription management.
"""

import logging
import time
import traceback
from ibapi.scanner import ScannerSubscription
from ibapi.tag_value import TagValue

from orbscanner.utils.logging.logging_utils import log_wrapper_call

# Constants from scanner.py
SCANNER_BATCH_SIZE = 4  # Maximum simultaneous scanner subscriptions (reduced from 8)
SCANNER_BATCH_DELAY = 3.0  # Seconds to wait between scanner batches

# Scanner types to run
SCANNER_TYPES = {
    "PRE_MARKET": [  # Pre-market scanner types
        "TOP_PERC_GAIN",
        "HOT_BY_VOLUME",
        "MOST_ACTIVE"
    ],
    "LOW_VIX": [  # VIX <= 25
        "HOT_BY_VOLUME",
        "TOP_PERC_GAIN",
        "TOP_TRADE_RATE",
        "MOST_ACTIVE",
        "GAP_SCANNER"  # Added gap scanner for ORB confirmation
    ],
    "HIGH_VIX": [  # VIX > 25
        "HOT_BY_VOLUME",
        "MOST_ACTIVE",
        "TOP_TRADE_RATE",
        "GAP_SCANNER"  # Added gap scanner for ORB confirmation
    ]
}


def get_scanner_filter_options(app, vix_level=None, scanner_type=None):
    """
    Get scanner filter options based on VIX level and scanner type

    Args:
        app: The IBApp instance
        vix_level (float, optional): Current VIX level for dynamic filter adjustment
        scanner_type (str, optional): Type of scanner to get filters for

    Note: This method returns TagValue filters that will be passed as scannerSubscriptionFilterOptions.
    These are different from scannerSubscriptionOptions and use a different format.

    IMPORTANT: Only include filters that can't be set directly on ScannerSubscription object.
    The following parameters should be set directly on ScannerSubscription instead of here:
    - abovePrice, belowPrice, aboveVolume, marketCapAbove, changePercAbove, volumeRateAbove

    DISABLED FILTERS: The following filters are disabled in IBKR accounts and should not be used:
    - averageDailyVolume, exDividendDate, hasSparseData, peRatioAbove

    VALID ADVANCED FILTERS: The following filters must be passed as TagValue objects:
    - openGapAbove, priceBelowValue, priceAboveValue

    Returns:
        tuple: (filter_options, gap_threshold) where:
            - filter_options is a list of TagValue objects for scannerSubscriptionFilterOptions
            - gap_threshold is the gap threshold value for GAP_SCANNER (or None for other scanners)
    """
    # Initialize empty filter options list
    filter_options = []
    gap_threshold = None

    # Special handling for GAP_SCANNER
    if scanner_type == "GAP_SCANNER":
        # Set gap threshold based on VIX level
        gap_threshold = 1.0  # Default 1% for normal conditions

        if vix_level is not None:
            if vix_level > 35:
                gap_threshold = 2.0  # 2% for high volatility
            elif vix_level > 25:
                gap_threshold = 1.5  # 1.5% for moderate volatility

        # IMPORTANT: We're not adding openGapAbove as a TagValue filter anymore
        # Instead, we'll return the gap_threshold and use _filter_strings approach
        # which has been proven to work in testing
        logging.info(f"Gap scanner configured with {gap_threshold}% threshold")
        logging.info(f"Using _filter_strings approach for gap filter instead of TagValue")

    # Add price ceiling for certain scanner types if needed
    # This demonstrates using priceBelowValue as a valid TagValue filter
    if scanner_type in ["MOST_ACTIVE", "TOP_TRADE_RATE"] and vix_level is not None and vix_level > 30:
        # During high volatility, limit price for these scanners
        filter_options.append(TagValue("priceBelowValue", "100"))
        logging.info(f"Added price ceiling of $100 for {scanner_type} during high volatility")

    # Log the filters being used
    if filter_options:
        logging.info(f"Using advanced scanner filters: {[f.tag + '=' + f.value for f in filter_options]}")
    else:
        logging.info("No advanced scanner filters applied")

    # Add warning about disabled filters
    logging.info("Note: averageDailyVolume, exDividendDate, hasSparseData, and peRatioAbove filters are disabled by IBKR")

    return filter_options, gap_threshold


def get_scanner_filters(app, vix_level=None, scanner_type=None):
    """
    DEPRECATED: Use get_scanner_filter_options instead.
    This method is kept for backward compatibility.

    Get scanner filters based on VIX level and scanner type.

    Args:
        app: The IBApp instance
        vix_level (float, optional): Current VIX level for dynamic filter adjustment
        scanner_type (str, optional): Type of scanner to get filters for

    Note: This method returns TagValue filters that will be passed as scannerSubscriptionFilterOptions.
    Parameters that can be set directly on the ScannerSubscription object (like changePercAbove)
    should be set in the start_scanners method instead.
    """
    logging.warning(f"DEPRECATED: get_scanner_filters is deprecated. Use get_scanner_filter_options instead.")
    return []


def get_scanner_types(app, vix_level=None, phase=None):
    """
    Get scanner types based on VIX level and phase

    Args:
        app: The IBApp instance
        vix_level (float, optional): Current VIX level
        phase (str, optional): Current scanner phase (PRE_MARKET_PHASE, ORB_PHASE, etc.)

    Returns:
        list: List of scanner types to run
    """
    # If in pre-market phase, use pre-market scanner types
    if phase == "PRE_MARKET_PHASE" or phase == "PRE_MARKET":
        scanner_list = SCANNER_TYPES["PRE_MARKET"]
        logging.info(f"Using PRE_MARKET scanner types: {scanner_list}")
        return scanner_list

    # Otherwise use VIX-based scanner types
    if vix_level is not None and vix_level > 25:
        scanner_list = SCANNER_TYPES["HIGH_VIX"]
        logging.info(f"Using HIGH_VIX scanner types: {scanner_list}")
    else:
        scanner_list = SCANNER_TYPES["LOW_VIX"]
        logging.info(f"Using LOW_VIX scanner types: {scanner_list}")

    return scanner_list


def start_scanners(app, vix_level=None, is_continuous=False, phase=None):
    """
    Start multiple scanner subscriptions based on VIX level and phase

    Args:
        app: The IBApp instance
        vix_level (float, optional): Current VIX level
        is_continuous (bool): Whether this is part of continuous scanning
        phase (str, optional): Current scanner phase (PRE_MARKET_PHASE, ORB_PHASE, etc.)
    """
    # Log wrapper call for method extraction verification
    log_wrapper_call("start_scanners", vix_level, is_continuous, phase)

    # CRITICAL FIX: Add safety check to prevent scanner subscriptions in non-ORB phases
    # This provides an additional layer of protection against unwanted scanner subscriptions
    if hasattr(app, 'phase_manager') and app.phase_manager.current_phase not in ["ORB_PHASE", "ORB", "PRE_MARKET_PHASE", "PRE_MARKET"]:
        current_phase = app.phase_manager.current_phase
        logging.warning(f"CRITICAL FIX: Refusing to start scanners in non-scanner phase: {current_phase}")
        logging.warning(f"Scanners should only run in PRE_MARKET or ORB phases")
        # Ensure the flag is set to False
        app.active_scanner_subscriptions = False
        return

    scanner_types = get_scanner_types(app, vix_level, phase)
    # We'll get scanner filters for each scanner type individually

    logging.info(f"Starting {len(scanner_types)} scanner subscriptions (Continuous: {is_continuous})...")

    # IMPORTANT: Always stop any existing scanners first to avoid hitting the 10 subscription limit
    stop_scanners(app)

    # Wait a moment for cancellations to be processed by the IBKR server
    logging.info("Waiting 2 seconds for previous scanner cancellations to be processed...")
    time.sleep(2)

    # Set flag to indicate active scanner subscriptions
    app.active_scanner_subscriptions = True

    # For continuous scanning, we don't reset all results
    if not is_continuous:
        # Reset scanner results and status
        app.scannerResults = {"UNKNOWN": []}  # Always initialize UNKNOWN key
        app.scannerFinished = {}
        app.scannerRequestIds = {}
        app.allScannersFinished = False
        app.combinedResults = []
        app.longCandidates = []
        app.shortCandidates = []
    else:
        # Just reset the current scan status
        app.scannerFinished = {}
        app.scannerRequestIds = {}
        app.allScannersFinished = False
        # Make sure UNKNOWN key exists
        if "UNKNOWN" not in app.scannerResults:
            app.scannerResults["UNKNOWN"] = []
        app.scan_count += 1
        logging.info(f"Continuous scan iteration #{app.scan_count}")

    # Apply VIX-based adjustments to scanner parameters
    base_rows = 50
    min_price = 10.00
    max_price = 500.00
    min_volume = 500000

    logging.info(f"Applying VIX-based adjustments (Current VIX: {vix_level})")
    if vix_level is None:
        logging.warning("No VIX level provided, using default scanner parameters.")
    elif vix_level > 35:
        logging.info("VIX > 35: Applying High Volatility adjustments")
        base_rows = 20
        min_volume = 1000000  # Higher volume requirement in high volatility
    elif vix_level > 30:
        logging.info("VIX > 30: Applying Moderate-High Volatility adjustments")
        base_rows = 35
        min_volume = 750000
    elif vix_level > 20:
        logging.info("VIX > 20: Applying Elevated Volatility adjustments")
        # No specific changes to base parameters
    else:
        logging.info("VIX <= 20: Using standard parameters.")

    # IBKR API has a limit of 10 simultaneous scanner subscriptions
    # Process scanner types in batches to stay within this limit
    MAX_SIMULTANEOUS_SCANNERS = SCANNER_BATCH_SIZE  # Using 4 instead of 10 to provide a large safety margin

    # Create scanner subscription objects for all scanner types
    scanner_subscriptions = []
    for scanner_type in scanner_types:
        req_id = app.get_next_req_id()
        app.scannerRequestIds[scanner_type] = req_id
        app.scannerFinished[req_id] = False

        sub = ScannerSubscription()
        sub.instrument = "STK"
        sub.locationCode = "STK.US.MAJOR"
        sub.scanCode = scanner_type
        sub.abovePrice = min_price
        sub.belowPrice = max_price

        # Special handling for GAP_SCANNER
        if scanner_type == "GAP_SCANNER":
            # For gap scanner, use TOP_PERC_GAIN as the base scan code
            sub.scanCode = "TOP_PERC_GAIN"
            logging.info(f"Gap scanner using TOP_PERC_GAIN as base scan code")

        # Implement Momentum & Volume Combo approach with dynamic thresholds
        # Set appropriate volume threshold based on VIX level
        if vix_level is not None and vix_level > 35:
            sub.aboveVolume = 1000000  # 1M minimum volume for high volatility
            rvol_multiplier = 3.0
            gap_threshold = 2.0
        elif vix_level is not None and vix_level > 25:
            sub.aboveVolume = 500000   # 500K minimum volume for moderate volatility
            rvol_multiplier = 2.0
            gap_threshold = 1.5
        else:
            sub.aboveVolume = 300000   # 300K minimum volume for normal conditions
            rvol_multiplier = 1.5
            gap_threshold = 1.0

        # Set momentum parameter (price change) based on scanner type and VIX level
        if scanner_type == "TOP_PERC_GAIN":
            # For TOP_PERC_GAIN, use higher changePercAbove values
            if vix_level is not None and vix_level > 35:
                sub.changePercAbove = 2.0  # 2% minimum price change for high volatility
            elif vix_level is not None and vix_level > 25:
                sub.changePercAbove = 1.5  # 1.5% minimum price change for moderate volatility
            else:
                sub.changePercAbove = 1.0  # 1% minimum price change for normal conditions
        elif scanner_type == "GAP_SCANNER":
            # For GAP_SCANNER, use same values as TOP_PERC_GAIN
            if vix_level is not None and vix_level > 35:
                sub.changePercAbove = 2.0  # 2% minimum price change for high volatility
            elif vix_level is not None and vix_level > 25:
                sub.changePercAbove = 1.5  # 1.5% minimum price change for moderate volatility
            else:
                sub.changePercAbove = 1.0  # 1% minimum price change for normal conditions
        elif scanner_type == "HOT_BY_VOLUME":
            # For HOT_BY_VOLUME, use lower changePercAbove values
            if vix_level is not None and vix_level > 35:
                sub.changePercAbove = 1.5  # 1.5% minimum price change for high volatility
            elif vix_level is not None and vix_level > 25:
                sub.changePercAbove = 1.0  # 1% minimum price change for moderate volatility
            else:
                sub.changePercAbove = 0.5  # 0.5% minimum price change for normal conditions

        # Log dynamic parameters for this scanner
        if scanner_type == "GAP_SCANNER":
            logging.info(f"Gap scanner parameters: gap_threshold={gap_threshold}%, rvol_multiplier={rvol_multiplier}x")

        # Adjust number of rows based on scanner type
        if scanner_type == "HOT_BY_VOLUME":
            sub.numberOfRows = base_rows
        elif scanner_type == "TOP_PERC_GAIN":
            sub.numberOfRows = base_rows
        elif scanner_type == "TOP_TRADE_RATE":
            sub.numberOfRows = int(base_rows * 0.8)  # Slightly fewer rows
        elif scanner_type == "MOST_ACTIVE":
            sub.numberOfRows = int(base_rows * 0.8)  # Slightly fewer rows
        else:
            sub.numberOfRows = base_rows

        # Log the subscription parameters for debugging
        logging.info(f"Scanner {scanner_type} parameters:")
        logging.info(f"  - aboveVolume: {sub.aboveVolume}")
        if hasattr(sub, 'changePercAbove'):
            logging.info(f"  - changePercAbove: {sub.changePercAbove}%")

        scanner_subscriptions.append((req_id, scanner_type, sub))

    # Process scanner subscriptions in batches
    total_scanners = len(scanner_subscriptions)
    batch_count = (total_scanners + MAX_SIMULTANEOUS_SCANNERS - 1) // MAX_SIMULTANEOUS_SCANNERS

    logging.info(f"Processing {total_scanners} scanners in {batch_count} batches (max {MAX_SIMULTANEOUS_SCANNERS} per batch)")

    for batch_idx in range(batch_count):
        batch_start = batch_idx * MAX_SIMULTANEOUS_SCANNERS
        batch_end = min(batch_start + MAX_SIMULTANEOUS_SCANNERS, total_scanners)
        batch = scanner_subscriptions[batch_start:batch_end]

        logging.info(f"Starting batch {batch_idx+1}/{batch_count} with {len(batch)} scanners")

        # Start each scanner in this batch
        active_req_ids = []
        for req_id, scanner_type, sub in batch:
            try:
                # Get scanner filter options and gap threshold
                scanner_filter_options, gap_threshold = get_scanner_filter_options(app, vix_level, scanner_type)

                # If this is a GAP_SCANNER and we have a gap threshold, use _filter_strings approach
                if scanner_type == "GAP_SCANNER" and gap_threshold is not None:
                    if not hasattr(sub, '_filter_strings'):
                        sub._filter_strings = []

                    # Add the openGapAbove filter string
                    filter_str = f"openGapAbove={gap_threshold}"
                    sub._filter_strings.append(filter_str)
                    logging.info(f"Added filter string: {filter_str}")

                logging.info(f"Starting {scanner_type} scanner (Req ID: {req_id})")
                logging.info(f"  Parameters: Price=${sub.abovePrice}-${sub.belowPrice}, Volume>{sub.aboveVolume}, Rows={sub.numberOfRows}")

                # Log filter strings if present
                if hasattr(sub, '_filter_strings') and sub._filter_strings:
                    logging.info(f"  Filter strings: {sub._filter_strings}")

                # Log the advanced filter options separately
                if scanner_filter_options:
                    logging.info(f"  Advanced filter options: {[f.tag + '=' + f.value for f in scanner_filter_options]}")
                else:
                    logging.info("  No advanced filter options applied")

                # Use the correct parameter ordering for reqScannerSubscription
                # The correct order is: reqId, subscription, scannerSubscriptionOptions, scannerSubscriptionFilterOptions
                app.reqScannerSubscription(req_id, sub, [], scanner_filter_options)
                active_req_ids.append(req_id)
            except Exception as e:
                logging.error(f"Error requesting {scanner_type} scanner subscription: {e}", exc_info=True)
                app.scannerFinished[req_id] = True

        # Wait for this batch to complete before starting the next batch
        if batch_idx < batch_count - 1:  # Don't wait after the last batch
            logging.info(f"Waiting for batch {batch_idx+1} scanners to complete...")

            # Wait for all scanners in this batch to finish or timeout
            batch_start_time = time.time()
            batch_timeout = 30  # 30 seconds timeout per batch

            while time.time() - batch_start_time < batch_timeout:
                # Check if all scanners in this batch are finished
                if all(app.scannerFinished.get(req_id, False) for req_id in active_req_ids):
                    logging.info(f"Batch {batch_idx+1} completed successfully")
                    break

                # Small sleep to prevent CPU spinning
                time.sleep(0.5)

            # Cancel any remaining active scanners in this batch
            for req_id in active_req_ids:
                if not app.scannerFinished.get(req_id, False):
                    try:
                        scanner_type = next((st for st, rid in app.scannerRequestIds.items() if rid == req_id), "UNKNOWN")
                        logging.warning(f"Scanner {scanner_type} (Req ID: {req_id}) did not complete in time, forcefully cancelling")
                        app.cancelScannerSubscription(req_id)
                        app.scannerFinished[req_id] = True
                    except Exception as e:
                        logging.error(f"Error cancelling scanner subscription {req_id}: {e}")
                        # Still mark as finished even if cancellation failed
                        app.scannerFinished[req_id] = True

            # CRITICAL FIX: Add a longer delay between batches to allow the API to process cancellations
            if batch_idx < batch_count - 1:  # If this is not the last batch
                logging.info(f"CRITICAL FIX: Waiting {SCANNER_BATCH_DELAY} seconds before starting next batch to avoid rate limits...")
                time.sleep(SCANNER_BATCH_DELAY)

    # Check if all scanners finished
    completed_scanners = sum(1 for finished in app.scannerFinished.values() if finished)
    logging.info(f"Completed {completed_scanners}/{total_scanners} scanner subscriptions")

    # Set allScannersFinished flag if all scanners are done
    app.allScannersFinished = all(app.scannerFinished.values())


def stop_scanners(app):
    """
    Cancel all active scanner subscriptions with verification

    ENHANCED FIX: Improved to ensure proper cancellation and verification
    to prevent lingering subscriptions that could hit rate limits

    Args:
        app: The IBApp instance
    """
    # Log the initial state of the active_scanner_subscriptions flag
    scanner_active_flag = getattr(app, 'active_scanner_subscriptions', None)
    logging.info(f"SCANNER FLAG STATUS [Before stop_scanners]: active_scanner_subscriptions={scanner_active_flag}")

    # Make a copy of the dictionary items to avoid modification during iteration
    scanner_items = list(app.scannerRequestIds.items()) if hasattr(app, 'scannerRequestIds') else []

    if not scanner_items:
        logging.info("No active scanner subscriptions to cancel")
        app.allScannersFinished = True
        app.active_scanner_subscriptions = False
        logging.info(f"SCANNER FLAG STATUS [After no items]: active_scanner_subscriptions=False")
        return

    logging.info(f"ENHANCED FIX: Cancelling {len(scanner_items)} active scanner subscriptions with verification...")

    # Track successful cancellations
    cancelled_count = 0
    failed_cancellations = []

    # Cancel each subscription
    for scanner_type, req_id in scanner_items:
        try:
            logging.info(f"Cancelling {scanner_type} scanner subscription (Req ID: {req_id}).")
            app.cancelScannerSubscription(req_id)
            # Immediately mark as finished to ensure our internal state is updated
            app.scannerFinished[req_id] = True
            cancelled_count += 1
        except Exception as e:
            logging.error(f"Error cancelling {scanner_type} scanner: {e}")
            # Still mark as finished even if there was an error
            app.scannerFinished[req_id] = True
            failed_cancellations.append((scanner_type, req_id))

    # Also check for any unknown active subscriptions by req_id
    for req_id in list(app.scannerFinished.keys()) if hasattr(app, 'scannerFinished') else []:
        if not app.scannerFinished.get(req_id, True):
            try:
                logging.warning(f"Cancelling unknown scanner subscription (Req ID: {req_id}).")
                app.cancelScannerSubscription(req_id)
                app.scannerFinished[req_id] = True
                cancelled_count += 1
            except Exception as e:
                logging.error(f"Error cancelling unknown scanner: {e}")
                # Still mark as finished
                app.scannerFinished[req_id] = True
                failed_cancellations.append(("UNKNOWN", req_id))

    # Retry failed cancellations once
    if failed_cancellations:
        logging.warning(f"Retrying {len(failed_cancellations)} failed cancellations...")
        retry_success = 0

        for scanner_type, req_id in failed_cancellations:
            try:
                logging.info(f"Retry: Cancelling {scanner_type} scanner subscription (Req ID: {req_id}).")
                app.cancelScannerSubscription(req_id)
                retry_success += 1
            except Exception as e:
                logging.error(f"Retry failed for {scanner_type} scanner (Req ID: {req_id}): {e}")

        if retry_success > 0:
            logging.info(f"Successfully cancelled {retry_success} subscriptions on retry")
            cancelled_count += retry_success

    # Update state - ENSURE these flags are set properly
    app.allScannersFinished = True
    app.active_scanner_subscriptions = False

    # Clear the request IDs dictionary to ensure we don't try to cancel them again
    if hasattr(app, 'scannerRequestIds'):
        app.scannerRequestIds.clear()

    # Verify all scanners are marked as finished
    unfinished_scanners = [req_id for req_id, finished in app.scannerFinished.items()] if hasattr(app, 'scannerFinished') else []
    unfinished_scanners = [req_id for req_id in unfinished_scanners if not app.scannerFinished.get(req_id, True)]

    if unfinished_scanners:
        logging.warning(f"WARNING: {len(unfinished_scanners)} scanner subscriptions still marked as unfinished")
        # Force mark them as finished
        for req_id in unfinished_scanners:
            app.scannerFinished[req_id] = True
            logging.info(f"Forced scanner with Req ID {req_id} to finished state")

    # Additional verification for active_scanner_subscriptions flag
    if getattr(app, 'active_scanner_subscriptions', False):
        logging.warning("WARNING: active_scanner_subscriptions flag is still True after stop_scanners() execution")
        app.active_scanner_subscriptions = False
        logging.info("FIXED: Forcibly set active_scanner_subscriptions to False")

    logging.info(f"ENHANCED FIX: Successfully cancelled and verified {cancelled_count} scanner subscriptions")
    logging.info(f"SCANNER FLAG STATUS [After stop_scanners]: active_scanner_subscriptions={app.active_scanner_subscriptions}")
