#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Scanner Result Processor for the ORB Scanner.

This module provides functions for processing scanner results and calculating
priority scores for symbols.
"""

import logging
import math
import traceback
import time
from datetime import datetime, timedelta
import pytz

from orbscanner.utils.logging.logging_utils import log_wrapper_call
from orbscanner.utils.phase.manager import PhaseManager
from orbscanner.utils.phase.logging import log_to_phase
from orbscanner.utils.data.priority_logger import (
    log_priority_input,
    log_priority_calculation,
    log_priority_component,
    log_priority_result,
    log_priority_error,
    log_priority_validation
)
from orbscanner.utils.data.volume_logger import (
    log_volume_processing,
    log_volume_error,
    log_volume_fallback
)

# Constants from scanner.py
SKIP_VOLUME_DATA = True  # Hard-coded flag to disable volume data collection
TREND_DATA_COLLECTION_DURATION = timedelta(minutes=2, seconds=30)  # 2:30 for data collection
BATCH_SIZE = 5  # Reduced from 20 to 5 symbols for IBKR compliance
MAX_TRACKED_SYMBOLS = 25  # maximum number of symbols to track for opening range


def calculate_priority_score(app, symbol_data):
    """
    Calculate dynamic priority score for a symbol using the weighted scoring system:
    Total Score = (Relative Volume × 0.4) + (Price Change % × 0.3) + (Gap Size % × 0.3)

    When SKIP_VOLUME_DATA is enabled, this function will use an alternative
    scoring method that doesn't depend on volume ratios.

    Args:
        app: The IBApp instance
        symbol_data (dict): Dictionary containing symbol data for priority calculation

    Returns:
        float or None: Priority score as a float, or None if calculation fails
    """
    # CRITICAL FIX: Validate symbol_data is a dictionary
    if not isinstance(symbol_data, dict):
        logging.error(f"CRITICAL ERROR: symbol_data is not a dictionary: {type(symbol_data)}")
        logging.error(f"symbol_data value: {symbol_data}")
        # Log the call stack to help identify where this is happening
        stack_trace = ''.join(traceback.format_stack())
        logging.error(f"Call stack for invalid symbol_data in calculate_priority_score:\n{stack_trace}")
        return None

    # CRITICAL FIX: Validate symbol exists in symbol_data and is a string
    if 'symbol' not in symbol_data:
        logging.error(f"CRITICAL ERROR: 'symbol' key missing from symbol_data: {symbol_data}")
        # Log the call stack to help identify where this is happening
        stack_trace = ''.join(traceback.format_stack())
        logging.error(f"Call stack for missing symbol key in calculate_priority_score:\n{stack_trace}")
        return None

    # Get base data
    symbol_raw = symbol_data['symbol']

    # CRITICAL FIX: Normalize the symbol using ensure_symbol_string
    symbol = app.ensure_symbol_string(symbol_raw)

    # Check if normalization failed
    if symbol == "UNKNOWN":
        logging.error(f"CRITICAL ERROR: Failed to normalize symbol in calculate_priority_score")
        logging.error(f"Original symbol value: {symbol_raw} (type: {type(symbol_raw)})")
        logging.error(f"symbol_data: {symbol_data}")
        # Log the call stack to help identify where this is happening
        stack_trace = ''.join(traceback.format_stack())
        logging.error(f"Call stack for symbol normalization failure in calculate_priority_score:\n{stack_trace}")
        return None

    # Update the symbol in symbol_data to ensure consistency
    symbol_data['symbol'] = symbol

    # Log successful normalization if the symbol changed
    if symbol != symbol_raw:
        logging.info(f"CRITICAL FIX: Normalized symbol in calculate_priority_score: {symbol_raw} -> {symbol}")

    # CRITICAL FIX: Validate symbol is a string (should always be true after normalization)
    if not isinstance(symbol, str):
        logging.error(f"CRITICAL ERROR: Normalized symbol is not a string: {type(symbol)}")
        logging.error(f"symbol value: {symbol}")
        logging.error(f"symbol_data: {symbol_data}")
        # Log the call stack to help identify where this is happening
        stack_trace = ''.join(traceback.format_stack())
        logging.error(f"Call stack for non-string normalized symbol in calculate_priority_score:\n{stack_trace}")
        return None

    # CRITICAL FIX: Validate that we have opening range data for this symbol
    if not hasattr(app, 'opening_range_data') or symbol not in app.opening_range_data:
        logging.error(f"CRITICAL ERROR: No opening range data for {symbol}")
        return None

    # CRITICAL FIX: Validate that the opening range data is valid
    or_data = app.opening_range_data[symbol]
    if not or_data or or_data.get('high', 0) <= 0 or or_data.get('low', 0) <= 0 or or_data.get('volume', 0) <= 0:
        logging.error(f"CRITICAL ERROR: Invalid opening range data for {symbol}: {or_data}")
        return None

    # Get scanner count with type safety
    try:
        scanner_count = int(symbol_data.get('scanner_count', 0))
    except (ValueError, TypeError):
        logging.error(f"CRITICAL ERROR: Invalid scanner_count format in symbol_data for {symbol}")
        logging.error(f"scanner_count value: {symbol_data.get('scanner_count')}")
        scanner_count = 0  # Default to 0 if invalid

    # Log start of priority calculation
    log_priority_input(symbol, "Starting priority score calculation",
                      input_data=str(symbol_data))

    # Check if this symbol appeared in the GAP_SCANNER with type safety
    in_gap_scanner = bool(symbol_data.get('in_gap_scanner', False))
    log_priority_input(symbol, "GAP_SCANNER appearance", in_gap_scanner=in_gap_scanner)

    # Enhanced logging for scanner count
    logging.info(f"PRIORITY_DATA: {symbol} scanner_count={scanner_count} (raw value from symbol_data)")
    log_priority_input(symbol, "Scanner count", scanner_count=scanner_count)

    if scanner_count == 0:
        logging.error(f"CRITICAL_ERROR: {symbol} has zero scanner_count - API data collection issue detected")
        log_priority_error(symbol, "Zero scanner count detected")
        # Instead of raising an exception, return None to indicate invalid data
        return None

    # Get price data with enhanced error checking
    price = symbol_data.get('price', None)
    log_priority_input(symbol, "Price data", price=price)

    if price is None:
        logging.error(f"CRITICAL_ERROR: {symbol} missing price data - API data collection issue detected")
        log_priority_error(symbol, "Missing price data")
        return None

    try:
        price = float(price)
        logging.info(f"PRIORITY_DATA: {symbol} price={price:.2f} (raw value from symbol_data)")
        log_priority_validation(symbol, "Price data validated", price=price)
    except (ValueError, TypeError):
        logging.error(f"CRITICAL_ERROR: {symbol} has invalid price format: {price} - API data collection issue detected")
        log_priority_error(symbol, "Invalid price format", price=price)
        return None

    if price <= 0:
        logging.error(f"CRITICAL_ERROR: {symbol} has zero or negative price={price} - API data collection issue detected")
        log_priority_error(symbol, "Zero or negative price", price=price)
        return None

    # Get pre-market high for price ratio calculation
    pre_market_high = app.pre_market_highs.get(symbol, None)
    if pre_market_high is None or pre_market_high <= 0:
        # Use current price as fallback
        logging.warning(f"Missing pre_market_high for {symbol}, using current price as fallback")
        pre_market_high = price

    # Calculate price change percentage
    price_change_pct = ((price / pre_market_high) - 1.0) * 100
    logging.info(f"PRIORITY_DATA: {symbol} price_change_pct={price_change_pct:.2f}%")

    # Calculate gap size percentage
    gap_pct = 0.0
    opening_range = app.opening_range_data.get(symbol, None)
    if opening_range:
        prev_close = opening_range.get('prev_close', None)
        open_price = opening_range.get('open', None)

        if prev_close is not None and open_price is not None and prev_close > 0:
            gap_pct = ((open_price / prev_close) - 1.0) * 100
            logging.info(f"PRIORITY_DATA: {symbol} gap_pct={gap_pct:.2f}%")

    # Check if we're using the alternative scoring method (no volume data)
    if SKIP_VOLUME_DATA:
        logging.info(f"SKIP_VOLUME_DATA is enabled - Using weighted scoring system for {symbol}")

        # Get opening range data
        opening_range = app.opening_range_data.get(symbol, None)

        # Initialize range component
        range_component = 0.0

        # Calculate range width if opening range data is available
        if opening_range:
            range_high = opening_range.get('high', None)
            range_low = opening_range.get('low', None)

            if range_high is not None and range_low is not None and range_low > 0:
                range_width = (range_high - range_low) / range_low
                logging.info(f"PRIORITY_DATA: {symbol} range_width={range_width:.4f} ({range_width*100:.2f}%)")
                range_component = min(range_width * 100, 5.0)  # Cap at 5.0

        # Use absolute value of price change for scoring
        price_change_abs = abs(price_change_pct)

        # Use absolute value of gap for scoring
        gap_abs = abs(gap_pct)

        # Calculate weighted score components
        # We use scanner count as a proxy for relative volume when SKIP_VOLUME_DATA is enabled
        volume_component = 0.4 * min(scanner_count * 0.5, 5.0)  # 40% weight, cap at 5.0
        log_priority_component(symbol, "Volume", scanner_count * 0.5, weight=0.4, cap=5.0)

        price_change_component = 0.3 * min(price_change_abs * 0.5, 5.0)  # 30% weight, cap at 5.0
        log_priority_component(symbol, "Price Change", price_change_abs * 0.5, weight=0.3, cap=5.0)

        gap_component = 0.3 * min(gap_abs * 0.5, 5.0)  # 30% weight, cap at 5.0
        log_priority_component(symbol, "Gap", gap_abs * 0.5, weight=0.3, cap=5.0)

        # Apply bonus for appearing in gap scanner
        gap_scanner_bonus = 1.0 if in_gap_scanner else 0.0
        log_priority_component(symbol, "Gap Scanner Bonus", gap_scanner_bonus)

        # Calculate final priority score with type safety
        try:
            priority_score = volume_component + price_change_component + gap_component + gap_scanner_bonus

            # Validate the result is a valid number
            if math.isnan(priority_score) or math.isinf(priority_score):
                logging.error(f"CRITICAL ERROR: Invalid priority score for {symbol}: {priority_score}")
                # Return a default score instead of an invalid value
                priority_score = 0.0

            log_priority_calculation(symbol, "Intermediate score", score=priority_score)

            # Apply scanner count bonus (logarithmic scaling) with type safety
            try:
                # Ensure scanner_count is a positive number before taking log
                safe_scanner_count = max(0, int(scanner_count))
                scanner_bonus = math.log(safe_scanner_count + 1) * 0.5
            except (ValueError, TypeError):
                logging.error(f"Error calculating scanner bonus for {symbol}: Invalid scanner_count {scanner_count}")
                scanner_bonus = 0.0

            log_priority_component(symbol, "Scanner Count Bonus", scanner_bonus)
            priority_score += scanner_bonus

            # Enhanced logging with detailed component breakdown
            logging.info(f"PRIORITY_SCORE (Weighted): {symbol} = {priority_score:.2f}")
            logging.info(f"  Volume Component: {volume_component:.2f} (40% weight)")
            logging.info(f"  Price Change Component: {price_change_component:.2f} (30% weight)")
            logging.info(f"  Gap Component: {gap_component:.2f} (30% weight)")
            logging.info(f"  Gap Scanner Bonus: {gap_scanner_bonus:.2f}")
            logging.info(f"  Scanner Bonus: {scanner_bonus:.2f}")

            # Log final result
            log_priority_result(symbol, priority_score,
                               volume_component=volume_component,
                               price_change_component=price_change_component,
                               gap_component=gap_component,
                               gap_scanner_bonus=gap_scanner_bonus,
                               scanner_bonus=scanner_bonus)
        except Exception as e:
            logging.error(f"CRITICAL ERROR: Exception calculating priority score for {symbol}: {e}")
            logging.error(traceback.format_exc())
            priority_score = 0.0

        # Ensure we return a valid float
        try:
            return float(priority_score)
        except (ValueError, TypeError):
            logging.error(f"CRITICAL ERROR: Could not convert priority score to float: {priority_score}")
            return 0.0

    # Standard scoring method (with volume data)
    # Get volume data with enhanced error checking and debugging
    volume = symbol_data.get('volume', None)

    # Log the start of volume data processing
    log_volume_processing(symbol, "VOLUME_START", "Starting volume data processing for priority calculation")

    # Log the complete symbol_data for debugging
    logging.info(f"VOLUME_DEBUG: {symbol} complete symbol_data: {symbol_data}")
    log_volume_processing(symbol, "SYMBOL_DATA", f"Complete symbol data: {symbol_data}")

    # Check if opening_range_data exists and log it
    opening_range = app.opening_range_data.get(symbol, None)
    if opening_range:
        logging.info(f"VOLUME_DEBUG: {symbol} opening_range_data: {opening_range}")
        log_volume_processing(symbol, "OPENING_RANGE", f"Opening range data: {opening_range}")
    else:
        logging.error(f"VOLUME_DEBUG: {symbol} has NO opening_range_data")
        log_volume_error(symbol, "MISSING_DATA", "No opening range data available")

    if volume is None:
        logging.error(f"CRITICAL_ERROR: {symbol} missing volume data - API data collection issue detected")
        logging.error(f"VOLUME_DEBUG: {symbol} volume is None - Check if volume is being set in symbol_data")
        log_volume_error(symbol, "MISSING_VOLUME", "Volume is None - Check if volume is being set in symbol_data")

        # Check if volume exists in opening_range_data
        if opening_range and 'volume' in opening_range:
            or_volume = opening_range.get('volume', 0)
            logging.info(f"VOLUME_DEBUG: {symbol} has volume={or_volume} in opening_range_data but not in symbol_data")
            log_volume_processing(symbol, "ALTERNATE_SOURCE", f"Found volume={or_volume} in opening_range_data")
            log_volume_fallback(symbol, "OR_VOLUME", f"Attempting to use opening range volume: {or_volume}")
        else:
            log_volume_error(symbol, "NO_FALLBACK", "No volume data available in any source")

        # Instead of raising an exception, return None to indicate invalid data
        return None

    try:
        volume = float(volume)
        logging.info(f"PRIORITY_DATA: {symbol} volume={volume:.0f} (raw value from symbol_data)")
        log_volume_processing(symbol, "VOLUME_PARSED", f"Successfully parsed volume: {volume:.0f}")
    except (ValueError, TypeError):
        logging.error(f"CRITICAL_ERROR: {symbol} has invalid volume format: {volume} - API data collection issue detected")
        logging.error(f"VOLUME_DEBUG: {symbol} volume type: {type(volume)}, value: {volume}")
        log_volume_error(symbol, "INVALID_FORMAT", f"Invalid volume format: type={type(volume)}, value={volume}")
        return None

    # Get avg_volume - NO FALLBACKS, REAL DATA ONLY
    avg_volume = app.avg_volumes.get(symbol, None)
    log_volume_processing(symbol, "AVG_VOLUME_LOOKUP", f"Looking up average volume: {avg_volume}")

    # If avg_volume is missing or invalid, log error and return None
    if avg_volume is None or avg_volume <= 0:
        logging.error(f"CRITICAL_ERROR: {symbol} missing or invalid avg_volume={avg_volume} - API data collection issue detected")
        logging.error(f"VOLUME_DEBUG: {symbol} app.avg_volumes keys: {list(app.avg_volumes.keys())}")
        log_volume_error(symbol, "MISSING_AVG_VOLUME", f"Missing or invalid avg_volume: {avg_volume}")
        log_volume_processing(symbol, "AVG_VOLUME_KEYS", f"Available keys: {list(app.avg_volumes.keys())[:20]}")
        # Instead of raising an exception, return None to indicate invalid data
        return None

    logging.info(f"PRIORITY_DATA: {symbol} avg_volume={avg_volume:.0f} (from app.avg_volumes)")
    log_volume_processing(symbol, "AVG_VOLUME_FOUND", f"Found valid average volume: {avg_volume:.0f}")

    # Calculate volume ratio with strict validation
    if volume <= 0:
        logging.error(f"CRITICAL_ERROR: {symbol} has zero or negative volume={volume} - API data collection issue detected")
        logging.error(f"VOLUME_DEBUG: {symbol} volume is zero or negative - Check if volume is being set correctly")
        log_volume_error(symbol, "INVALID_VOLUME", f"Zero or negative volume: {volume}")
        # Instead of raising an exception, return None to indicate invalid data
        return None

    volume_ratio = volume / avg_volume
    logging.info(f"PRIORITY_DATA: {symbol} volume_ratio={volume_ratio:.2f}")
    log_volume_processing(symbol, "VOLUME_RATIO", f"Calculated volume ratio: {volume_ratio:.2f}")

    # Calculate priority components
    volume_component = 0.4 * min(volume_ratio, 10.0)  # Cap at 10x
    log_priority_component(symbol, "Volume", volume_ratio, weight=0.4, cap=10.0)

    price_component = 0.3 * min(price / pre_market_high, 1.5)     # Cap at 1.5x
    log_priority_component(symbol, "Price", price / pre_market_high, weight=0.3, cap=1.5)

    range_width = (opening_range['high'] - opening_range['low']) / opening_range['low']
    range_component = 0.2 * min(range_width * 10, 2.0)  # Cap at 2.0
    log_priority_component(symbol, "Range Width", range_width * 10, weight=0.2, cap=2.0)

    # Calculate scanner component with type safety
    try:
        safe_scanner_count = max(0, int(scanner_count))
        scanner_component = 0.1 * safe_scanner_count
    except (ValueError, TypeError):
        logging.error(f"Error calculating scanner component for {symbol}: Invalid scanner_count {scanner_count}")
        scanner_component = 0.0

    log_priority_component(symbol, "Scanner Count", safe_scanner_count, weight=0.1)

    # Calculate final priority score with type safety
    try:
        priority_score = volume_component + price_component + range_component + scanner_component

        # Validate the result is a valid number
        if math.isnan(priority_score) or math.isinf(priority_score):
            logging.error(f"CRITICAL ERROR: Invalid priority score for {symbol}: {priority_score}")
            # Return a default score instead of an invalid value
            priority_score = 0.0

        log_priority_calculation(symbol, "Final score calculation", score=priority_score)

        # Enhanced logging with detailed component breakdown
        logging.info(f"PRIORITY_SCORE: {symbol} = {priority_score:.2f}")
        logging.info(f"  Volume Component: {volume_component:.2f} (40% of min({volume_ratio:.2f}, 10.0))")
        logging.info(f"  Price Component: {price_component:.2f} (30% of min({price/pre_market_high:.2f}, 1.5))")
        logging.info(f"  Range Component: {range_component:.2f} (20% of min({range_width*10:.2f}, 2.0))")
        logging.info(f"  Scanner Component: {scanner_component:.2f} (10% of {safe_scanner_count})")

        # Log final result with all components
        log_priority_result(symbol, priority_score,
                           volume_component=volume_component,
                           price_component=price_component,
                           range_component=range_component,
                           scanner_component=scanner_component,
                           volume_ratio=volume_ratio,
                           price_ratio=price/pre_market_high,
                           range_width=range_width)
    except Exception as e:
        logging.error(f"CRITICAL ERROR: Exception calculating priority score for {symbol}: {e}")
        logging.error(traceback.format_exc())
        priority_score = 0.0

    # Ensure we return a valid float
    try:
        final_score = float(priority_score)
        if math.isnan(final_score) or math.isinf(final_score):
            logging.error(f"CRITICAL ERROR: Priority score is NaN or Inf for {symbol}, returning 0.0")
            return 0.0
        return final_score
    except (ValueError, TypeError):
        logging.error(f"CRITICAL ERROR: Could not convert priority score to float: {priority_score}")
        return 0.0


def collect_trend_data_for_candidates(app, candidates):
    """
    Collect trend data for all candidate symbols to ensure the finalizing report
    has complete trend information.

    Args:
        app: The IBApp instance
        candidates: List of candidate dictionaries
    
    Returns:
        int: Number of symbols for which trend data was collected
    """
    from orbscanner.analysis.trend_analyzer import get_daily_trend_data
    
    logging.info(f"Collecting trend data for {len(candidates)} candidates")
    
    # Initialize daily_trend_data if it doesn't exist
    if not hasattr(app, 'daily_trend_data'):
        app.daily_trend_data = {}
        
    symbols_processed = 0
    symbols_with_data = 0
        
    for candidate in candidates:
        symbol = candidate.get('symbol')
        if not symbol or not isinstance(symbol, str):
            logging.warning(f"Invalid symbol in candidate: {symbol}")
            continue
            
        # Skip if we already have trend data for this symbol
        if symbol in app.daily_trend_data:
            logging.debug(f"Already have trend data for {symbol}")
            symbols_with_data += 1
            continue
            
        # Get trend data
        logging.info(f"Collecting trend data for candidate: {symbol}")
        trend_data = get_daily_trend_data(app, symbol)
        symbols_processed += 1
        
        if trend_data:
            # Store the trend data
            app.daily_trend_data[symbol] = trend_data
            
            # Also store directly in the candidate for easy access
            candidate['trend'] = trend_data.get('trend', 'N/A')
            candidate['trend_vwap'] = trend_data.get('vwap', 0)
            candidate['trend_sma200'] = trend_data.get('sma200', 0) 
            candidate['vwap_correlation'] = trend_data.get('vwap_correlation', 0.0)
            
            symbols_with_data += 1
            logging.info(f"Successfully collected trend data for {symbol}: {trend_data.get('trend', 'N/A')}")
        else:
            logging.warning(f"Failed to collect trend data for {symbol}")
    
    logging.info(f"Trend data collection complete: {symbols_with_data}/{len(candidates)} symbols have trend data")
    logging.info(f"Processed {symbols_processed} symbols, skipped {len(candidates) - symbols_processed} (already had data)")
    
    return symbols_with_data


def process_scanner_results(app, vix_level=None, is_continuous=False, apply_trend_filtering=False):
    """
    Process and combine results from all scanners with enhanced scoring

    Args:
        app: The IBApp instance
        vix_level (float, optional): Current VIX level for volatility-based adjustments
        is_continuous (bool): Whether this is part of continuous scanning mode
        apply_trend_filtering (bool): Whether to apply trend data filtering
                                     Should only be True after trend data collection in finalizing phase
    """
    # At the very beginning of the process_scanner_results method
    # Point 3: Log at the Entry of process_scanner_results
    _args_summary_rp = f"Args(vix_level={vix_level}, is_continuous={is_continuous}, apply_trend_filtering={apply_trend_filtering})" # Adapt if other args like current_phase are passed
    _lc_len_entry_rp = len(app.longCandidates) # Assuming these are app.longCandidates
    _sc_len_entry_rp = len(app.shortCandidates)
    _lc_entry_sample_rp = [(c.get('symbol') if isinstance(c, dict) else c) for c in app.longCandidates[:2]]
    _sc_entry_sample_rp = [(c.get('symbol') if isinstance(c, dict) else c) for c in app.shortCandidates[:2]]

    _log_detail_entry_rp = (
        f"{_args_summary_rp}, "
        f"ListsBeforeReset: Longs={_lc_len_entry_rp} (e.g., {_lc_entry_sample_rp}), Shorts={_sc_len_entry_rp} (e.g., {_sc_entry_sample_rp})"
    )

    if hasattr(app, 'log_to_phase'):
        app.log_to_phase(
            PhaseManager.FINALIZING, 
            f"RP_DEBUG: PROCESS_SCANNER_RESULTS_ENTRY: {_log_detail_entry_rp}",
            level=logging.INFO
        )
    else:
        # Fallback if direct phase logging isn’t straightforward
        logging.info(f"RP_DEBUG (General Log): PROCESS_SCANNER_RESULTS_ENTRY: {_log_detail_entry_rp}")

    logging.critical(
        f"CDG_TRACKING [RP_ENTRY]: {_log_detail_entry_rp} "
        f"[file: result_processor.py, line: RP_ENTRY_LOG_LINE]"
    )

    # Log wrapper call for method extraction verification
    log_wrapper_call("process_scanner_results", vix_level, is_continuous, apply_trend_filtering)

    logging.info("Processing and combining scanner results with enhanced scoring...")

    # DIAGNOSTIC LOGGING: Log current phase and tracked symbols state
    current_phase = None
    if hasattr(app, 'phase_manager') and app.phase_manager is not None:
        current_phase = app.phase_manager.current_phase
        logging.debug(f"process_scanner_results called in phase: {current_phase}")

    # Log tracked_symbols state
    if hasattr(app, 'tracked_symbols'):
        # Log the first 5 symbols and their types to avoid excessive logging
        sample_symbols = list(app.tracked_symbols.keys())[:5]
        logging.debug(f"Tracked symbols before processing in process_scanner_results: { {s: type(s).__name__ for s in sample_symbols} }...")
        logging.debug(f"Total tracked symbols: {len(app.tracked_symbols)}")

        # Check for non-string keys
        non_string_keys = [k for k in app.tracked_symbols.keys() if not isinstance(k, str)]
        if non_string_keys:
            logging.warning(f"Found {len(non_string_keys)} non-string keys in tracked_symbols at start of process_scanner_results")
            for k in non_string_keys[:3]:  # Log first 3 only
                logging.warning(f"Non-string key: {str(k)}, type: {type(k).__name__}")

            # Check specifically for self
            if app in app.tracked_symbols:
                logging.critical("CRITICAL ERROR: Found 'app' (IBApp object) in tracked_symbols at start of process_scanner_results!")
                logging.critical("This is a programming error - the IBApp object cannot be used as a symbol")
                # Log the call stack to help identify where this is happening
                stack_trace = ''.join(traceback.format_stack())
                logging.critical(f"Call stack for 'app' in tracked_symbols:\n{stack_trace}")

    # STEP 1: Always track symbols from scanner results, regardless of opening range data
    # This breaks the circular dependency between tracking symbols and updating opening ranges
    total_symbols = 0
    newly_tracked = 0

    # Process all scanner results to track symbols
    for scanner_type, results in app.scannerResults.items():
        total_symbols += len(results)

        for result in results:
            # CRITICAL FIX: Ensure symbol is a string using ensure_symbol_string
            symbol_raw = result.get('symbol')
            symbol_to_track = app.ensure_symbol_string(symbol_raw)

            if symbol_to_track == "UNKNOWN":
                logging.error(f"CRITICAL ERROR: Invalid symbol in scanner results: {symbol_raw} (type: {type(symbol_raw)})")
                continue

            # Update the symbol in the result to ensure it's normalized
            if symbol_to_track != symbol_raw:
                logging.warning(f"CRITICAL FIX: Normalized symbol in scanner results: {symbol_raw} -> {symbol_to_track}")
                result['symbol'] = symbol_to_track

            # CRITICAL FIX: Final check to ensure symbol_to_track is a string
            if not isinstance(symbol_to_track, str):
                logging.critical(f"CRITICAL ERROR: Non-string symbol about to be added to tracked_symbols: {symbol_to_track} (type: {type(symbol_to_track)})")
                try:
                    symbol_to_track = app.ensure_symbol_string(symbol_to_track)
                    if symbol_to_track == "UNKNOWN":
                        logging.critical("CRITICAL ERROR: Could not normalize symbol, skipping")
                        continue
                    logging.critical(f"CRITICAL FIX: Normalized symbol to: {symbol_to_track}")
                except Exception as e:
                    logging.critical(f"CRITICAL ERROR: Exception while normalizing symbol: {e}")
                    continue

            # Add to tracked symbols for opening range tracking
            if symbol_to_track not in app.tracked_symbols and len(app.tracked_symbols) < MAX_TRACKED_SYMBOLS:
                app.tracked_symbols[symbol_to_track] = {
                    'first_seen': datetime.now().strftime('%H:%M:%S'),
                    'last_price': float(result.get('price', 0)) if 'price' in result else 0,
                    'scanner_type': scanner_type
                }
                newly_tracked += 1

    logging.info(f"Symbol tracking: Found {total_symbols} total symbols across all scanners")
    logging.info(f"Symbol tracking: Added {newly_tracked} new symbols to tracked_symbols (total: {len(app.tracked_symbols)})")

    # STEP 2: Verify that opening ranges have been updated before calculating priorities
    # This is a safety check to ensure we're following the correct sequence
    if not hasattr(app, 'opening_range_data') or not app.opening_range_data:
        logging.error("CRITICAL ERROR: Opening ranges have not been updated before processing scanner results")
        logging.error("This is a sequencing error - opening ranges must be updated BEFORE calculating priority scores")
        logging.error("Skipping priority calculation to prevent invalid scores, but symbols are now being tracked")
        logging.info(f"Tracked symbols: {len(app.tracked_symbols)} symbols are now being tracked for opening range data")

        # Additional logging to help diagnose the issue
        if hasattr(app, 'historical_data_processed_for_orb'):
            if app.historical_data_processed_for_orb:
                logging.error("Historical data processing was attempted but did not populate opening_range_data")
            else:
                logging.error("Historical data processing has not been attempted yet")
        else:
            logging.error("historical_data_processed_for_orb flag not found - historical processing status unknown")
            # Initialize the flag to avoid this error in the future
            app.historical_data_processed_for_orb = False
            logging.warning("CRITICAL FIX: Initialized missing historical_data_processed_for_orb flag")

        # Return empty list instead of None to prevent TypeError in process_scanner_results()
        return []

    # Initialize selection logger if not already done
    if not hasattr(app, 'selection_logger'):
        from orbscanner.utils.reporting.selection import SelectionLogger
        app.selection_logger = SelectionLogger()
        logging.info(f"Created consolidated selection log at {app.selection_logger.log_file}")

    # Get current phase if phase manager is initialized
    current_phase = None
    if hasattr(app, 'phase_manager') and app.phase_manager is not None:
        current_phase = app.phase_manager.current_phase

    # Start a new iteration in the selection log
    app.selection_logger.start_iteration(vix_level, is_continuous, apply_trend_filtering, phase=current_phase)

    # Log scanner results
    app.selection_logger.log_scanner_results(app.scannerResults)

    # Initialize filtering statistics for this processing run
    if not hasattr(app, 'filtering_stats'):
        app.filtering_stats = {}

    if 'initial_scan' not in app.filtering_stats:
        # Count total scanner results (raw count before deduplication)
        total_scanner_results = sum(len(results) for scanner_type, results in app.scannerResults.items()
                                  if scanner_type != 'COMBINED')

        app.filtering_stats['initial_scan'] = {
            'before': total_scanner_results,
            'after': 0  # Will be updated at the end
        }

    # Define scanner weights based on historical performance
    scanner_weights = {
        'HOT_BY_VOLUME': 1.2,  # Best performer
        'TOP_PERC_GAIN': 1.1,  # Strong performer
        'MOST_ACTIVE': 1.0,    # Standard weight
        'TOP_TRADE_RATE': 0.9  # Slightly less valuable
    }

    # Define which scanners are better for longs vs shorts
    long_scanners = ['HOT_BY_VOLUME', 'TOP_PERC_GAIN']
    short_scanners = ['TOP_TRADE_RATE', 'MOST_ACTIVE']

    # Count symbol appearances across all scanners
    symbol_appearances = {}
    symbol_data = {}
    gap_scanner_symbols = set()  # Track symbols that appear in the GAP_SCANNER

    for scanner_type, results in app.scannerResults.items():
        logging.info(f"Scanner {scanner_type} returned {len(results)} results")

        # If this is the gap scanner, track its symbols separately
        if scanner_type == "GAP_SCANNER":
            for result in results:
                # CRITICAL FIX: Ensure symbol is a string
                symbol_raw = result.get('symbol')
                symbol = app.ensure_symbol_string(symbol_raw)

                if symbol == "UNKNOWN":
                    logging.error(f"CRITICAL ERROR: Invalid symbol in GAP_SCANNER results: {symbol_raw} (type: {type(symbol_raw)})")
                    continue

                # Update the symbol in the result to ensure it's normalized
                if symbol != symbol_raw:
                    logging.warning(f"CRITICAL FIX: Normalized symbol in GAP_SCANNER results: {symbol_raw} -> {symbol}")
                    result['symbol'] = symbol

                gap_scanner_symbols.add(symbol)
            logging.info(f"GAP_SCANNER found {len(gap_scanner_symbols)} symbols with significant gaps")

        for result in results:
            # CRITICAL FIX: Ensure symbol is a string
            symbol_raw = result.get('symbol')
            symbol = app.ensure_symbol_string(symbol_raw)

            if symbol == "UNKNOWN":
                logging.error(f"CRITICAL ERROR: Invalid symbol in scanner results: {symbol_raw} (type: {type(symbol_raw)})")
                continue

            # Update the symbol in the result to ensure it's normalized
            if symbol != symbol_raw:
                logging.warning(f"CRITICAL FIX: Normalized symbol in scanner results: {symbol_raw} -> {symbol}")
                result['symbol'] = symbol

            # Count appearances
            if symbol not in symbol_appearances:
                symbol_appearances[symbol] = 0
                symbol_data[symbol] = []

            symbol_appearances[symbol] += 1
            symbol_data[symbol].append(result)

    # Process all unique symbols
    combined_results = []
    
    # Initialize candidate lists
    # If we're applying trend filtering and in continuous mode, preserve existing candidates
    if is_continuous and apply_trend_filtering:
        # Make a copy of existing candidates to preserve them
        existing_long = getattr(app, 'longCandidates', [])
        existing_short = getattr(app, 'shortCandidates', [])
        long_candidates = list(existing_long)
        short_candidates = list(existing_short)
        
        # Get symbol names for logging
        long_symbols = [c.get('symbol', str(c)) if isinstance(c, dict) else str(c) for c in existing_long[:3]]
        short_symbols = [c.get('symbol', str(c)) if isinstance(c, dict) else str(c) for c in existing_short[:3]]
        
        logging.critical(f"CDG_TRACKING [RP_CANDIDATE_INIT]: Preserved existing candidates: {len(long_candidates)} long (e.g. {long_symbols}), {len(short_candidates)} short (e.g. {short_symbols})")
    else:
        # Otherwise start with empty lists
        long_candidates = []
        short_candidates = []
        logging.debug(f"Started with empty candidate lists (is_continuous={is_continuous}, apply_trend_filtering={apply_trend_filtering})")

    # Track symbols with missing or invalid opening ranges
    symbols_with_missing_ranges = []
    symbols_with_invalid_ranges = []

    # Log symbol appearances to the selection logger
    app.selection_logger.log_symbol_appearances(symbol_appearances)

    for symbol, appearances in symbol_appearances.items():
        # CRITICAL FIX: Ensure symbol is a string using ensure_symbol_string
        symbol_str = app.ensure_symbol_string(symbol)

        if symbol_str == "UNKNOWN":
            logging.error(f"CRITICAL ERROR: Invalid symbol in symbol_appearances: {symbol} (type: {type(symbol)})")
            continue

        # Log if we had to normalize the symbol
        if symbol_str != symbol and isinstance(symbol, str):
            logging.warning(f"CRITICAL FIX: Normalized symbol in symbol_appearances: {symbol} -> {symbol_str}")

        # Get all occurrences of this symbol
        symbol_results = symbol_data[symbol]

        # Use the first result as the base
        base_result = symbol_results[0].copy()

        # CRITICAL FIX: Ensure symbol in base_result is a string
        base_result['symbol'] = symbol_str

        # Calculate long and short scores
        long_score = 0
        short_score = 0

        # Process each occurrence for scoring
        for result in symbol_results:
            scanner_type = result['scanner_type']
            rank = result['rank']
            weight = scanner_weights.get(scanner_type, 1.0)

            # Base score from scanner type and rank
            base_score = weight * (50 - rank)

            if scanner_type in long_scanners:
                long_score += base_score
            if scanner_type in short_scanners:
                short_score += base_score

        # Add multi-scanner bonus with quality weighting
        scanner_bonus = 0
        for result in symbol_results:
            scanner_type = result['scanner_type']
            weight = scanner_weights.get(scanner_type, 1.0)
            scanner_bonus += 5 * weight  # Quality-weighted bonus

        # Add scanner bonus to both scores
        long_score += scanner_bonus
        short_score += scanner_bonus

        # Add scores to the result
        base_result['scanner_count'] = appearances
        base_result['long_score'] = long_score
        base_result['short_score'] = short_score

        # Add gap scanner flag
        base_result['in_gap_scanner'] = symbol in gap_scanner_symbols
        if symbol in gap_scanner_symbols:
            logging.info(f"Symbol {symbol} appeared in GAP_SCANNER - adding bonus in scoring")

        # Calculate priority score for continuous scanning
        if is_continuous:
            # Track valid symbols for data quality reporting
            if not hasattr(app, 'valid_priority_score_count'):
                app.valid_priority_score_count = 0
                app.total_priority_score_attempts = 0
                # Initialize priority score calculation statistics
                app.priority_score_stats = {
                    'success': 0,
                    'failure': 0,
                    'failure_reasons': {},
                    'success_rate': 0.0
                }

            app.total_priority_score_attempts += 1

            # Log priority score calculation attempt
            log_priority_calculation(symbol, "Starting priority score calculation attempt",
                                    attempt_number=app.total_priority_score_attempts)

            # CRITICAL FIX: Check if symbol_data has volume_30d_avg and copy to avg_volumes
            if hasattr(app, 'symbol_data') and symbol in app.symbol_data and app.symbol_data[symbol].get('volume_30d_avg', 0) > 0:
                volume_30d_avg = app.symbol_data[symbol]['volume_30d_avg']
                if symbol not in app.avg_volumes or app.avg_volumes[symbol] <= 0:
                    app.avg_volumes[symbol] = volume_30d_avg
                    logging.info(f"VOLUME_DEBUG: Transferred volume_30d_avg to avg_volumes for {symbol}: {volume_30d_avg:.0f}")
                    log_volume_processing(symbol, "VOLUME_TRANSFER",
                                         f"Transferred volume_30d_avg to avg_volumes: {volume_30d_avg:.0f}")

            # VOLUME DEBUG: Check if opening range data exists and has volume
            if symbol in app.opening_range_data:
                or_data = app.opening_range_data[symbol]
                or_volume = or_data.get('volume', 0)
                logging.info(f"VOLUME_DEBUG: Before priority calculation - {symbol} has opening_range volume: {or_volume}")

                # CRITICAL FIX: Copy volume from opening_range_data to base_result
                if or_volume > 0 and ('volume' not in base_result or base_result.get('volume', 0) <= 0):
                    logging.info(f"VOLUME_DEBUG: Copying volume {or_volume} from opening_range_data to base_result for {symbol}")
                    base_result['volume'] = or_volume
            else:
                logging.error(f"VOLUME_DEBUG: {symbol} has NO opening_range_data before priority calculation")
                # CRITICAL FIX: Skip priority calculation if opening range data is missing
                logging.error(f"CRITICAL FIX: Skipping priority calculation for {symbol} due to missing opening range data")

                # Track failure in statistics
                app.priority_score_stats['failure'] += 1
                failure_reason = "Missing opening range data"
                app.priority_score_stats['failure_reasons'][failure_reason] = app.priority_score_stats['failure_reasons'].get(failure_reason, 0) + 1

                # Set default values and skip to next symbol
                base_result['priority_score'] = 0.0
                base_result['priority'] = 0.0
                base_result['data_quality_issue'] = True
                continue  # Skip to next symbol

            # Log the complete base_result for debugging
            logging.info(f"VOLUME_DEBUG: {symbol} base_result before priority calculation: {base_result}")

            # CRITICAL FIX: Ensure symbol in base_result is a string using ensure_symbol_string
            symbol_raw = base_result.get('symbol')
            symbol_str = app.ensure_symbol_string(symbol_raw)

            if symbol_str == "UNKNOWN":
                logging.error(f"CRITICAL ERROR: Invalid symbol in base_result: {symbol_raw} (type: {type(symbol_raw)})")
                logging.error(f"Setting default priority score and skipping calculation")
                base_result['priority_score'] = 0.0
                base_result['priority'] = 0.0
                base_result['data_quality_issue'] = True
                continue

            # Update the symbol in base_result if it was normalized
            if symbol_str != symbol_raw:
                logging.warning(f"CRITICAL FIX: Normalized symbol in base_result: {symbol_raw} -> {symbol_str}")
                base_result['symbol'] = symbol_str

            try:
                priority_score = calculate_priority_score(app, base_result)

                # Handle the case where calculate_priority_score returns None (invalid data)
                if priority_score is None:
                    logging.warning(f"Symbol {symbol} has invalid data for priority score calculation - using fallback")
                    logging.error(f"VOLUME_DEBUG: {symbol} priority calculation failed - using fallback method")

                    # Track failure in statistics
                    app.priority_score_stats['failure'] += 1
                    failure_reason = "Missing or invalid data"
                    app.priority_score_stats['failure_reasons'][failure_reason] = app.priority_score_stats['failure_reasons'].get(failure_reason, 0) + 1

                    # Log detailed failure information
                    log_priority_error(symbol, "Priority calculation failed - using fallback",
                                      reason=failure_reason,
                                      attempt=app.total_priority_score_attempts)

                    # ENHANCED FALLBACK: Calculate a more comprehensive fallback priority score
                    # Get scanner count with better fallback
                    scanner_count = 0
                    if isinstance(base_result.get('scanners'), list):
                        scanner_count = len(base_result.get('scanners'))
                    elif 'scanner_count' in base_result:
                        # Use scanner_count if directly available
                        try:
                            scanner_count = int(base_result.get('scanner_count', 0))
                        except (ValueError, TypeError):
                            scanner_count = 1
                    else:
                        scanner_count = 1
                    
                    # Get range width from opening_range_data if available
                    range_component = 0
                    if symbol in app.opening_range_data:
                        or_data = app.opening_range_data[symbol]
                        high = or_data.get('high', 0)
                        low = or_data.get('low', 0)
                        if low > 0 and high > low:
                            range_width = ((high - low) / low) * 100
                            range_component = min(range_width * 0.1, 2.0)  # Cap at 2.0
                            logging.info(f"FALLBACK PRIORITY: Using range width component {range_component:.2f} for {symbol}")
                    
                    # Calculate a more comprehensive fallback priority
                    priority_score = 0.5 * scanner_count + range_component
                    
                    # Apply gap scanner bonus if applicable
                    gap_scanner_bonus = 0
                    if base_result.get('in_gap_scanner', False):
                        gap_scanner_bonus = 1.0
                        priority_score += gap_scanner_bonus
                        logging.info(f"FALLBACK PRIORITY: Adding gap scanner bonus of 1.0 for {symbol}")
                    
                    logging.info(f"FALLBACK PRIORITY: {symbol} = {priority_score:.2f} (scanner={scanner_count}*0.5, range={range_component:.2f}, gap_bonus={gap_scanner_bonus})")
                    
                    # Don't skip, use the fallback priority score instead
                    base_result['priority_score'] = priority_score
                    base_result['priority'] = priority_score
                    base_result['data_quality_issue'] = True

                # Valid priority score
                app.valid_priority_score_count += 1
                app.priority_score_stats['success'] += 1

                # Calculate success rate
                total_calcs = app.priority_score_stats['success'] + app.priority_score_stats['failure']
                app.priority_score_stats['success_rate'] = (app.priority_score_stats['success'] / total_calcs) * 100 if total_calcs > 0 else 0

                # Log success information
                log_priority_validation(symbol, "Priority calculation successful",
                                      score=priority_score,
                                      success_rate=f"{app.priority_score_stats['success_rate']:.1f}%")

                base_result['priority_score'] = priority_score
                base_result['priority'] = priority_score
                base_result['data_quality_issue'] = False

                # CRITICAL FIX: Copy trend data to base_result if it exists
                if symbol in app.daily_trend_data:
                    trend_data = app.daily_trend_data[symbol]
                    base_result['trend'] = trend_data.get('trend', 'N/A')
                    base_result['trend_vwap'] = trend_data.get('vwap', 0)
                    base_result['trend_sma200'] = trend_data.get('sma200', 0)
                    base_result['vwap_correlation'] = trend_data.get('vwap_correlation', 0)
                    logging.info(f"TREND_DATA: Copied trend data for {symbol}: {trend_data.get('trend', 'N/A')}")
                else:
                    logging.warning(f"TREND_DATA: No trend data found for {symbol}")
                    
                # Boost score based on priority for continuous scanning
                priority_boost = priority_score * 10
                long_score += priority_boost
                short_score += priority_boost
            except Exception as e:
                # Catch any unexpected exceptions that might still occur
                logging.error(f"Error calculating priority score for {symbol}: {e}")
                logging.error(f"Exception details: {traceback.format_exc()}")

                # Track exception in statistics
                app.priority_score_stats['failure'] += 1
                failure_reason = f"Exception: {type(e).__name__}"
                app.priority_score_stats['failure_reasons'][failure_reason] = app.priority_score_stats['failure_reasons'].get(failure_reason, 0) + 1

                # Log detailed exception information
                log_priority_error(symbol, "Priority calculation exception",
                                  exception_type=type(e).__name__,
                                  exception_msg=str(e),
                                  attempt=app.total_priority_score_attempts)

                # Calculate updated success rate
                total_calcs = app.priority_score_stats['success'] + app.priority_score_stats['failure']
                app.priority_score_stats['success_rate'] = (app.priority_score_stats['success'] / total_calcs) * 100 if total_calcs > 0 else 0

                base_result['priority_score'] = 0.0
                base_result['priority'] = 0.0
                base_result['data_quality_issue'] = True
                continue  # Skip this symbol and move to the next one

        # Check if symbol has valid opening range data
        has_valid_range = False
        range_width_pct = 0.0

        if symbol_str in app.opening_range_data:
            or_data = app.opening_range_data[symbol_str]
            high = or_data.get('high', 0)
            low = or_data.get('low', 0)
            
            # Store opening range data directly in the candidate
            base_result['opening_range_high'] = high
            base_result['opening_range_low'] = low

            # Calculate range width percentage
            if low > 0 and high > low:
                range_width_pct = ((high - low) / low) * 100
                base_result['range_width_pct'] = range_width_pct
                # CRITICAL FIX: Store the range width directly in the candidate
                base_result['range_width'] = range_width_pct

                # Check if range width is valid (not too narrow or too wide)
                if 0.1 <= range_width_pct <= 10.0:
                    has_valid_range = True
                else:
                    symbols_with_invalid_ranges.append((symbol_str, range_width_pct))
            else:
                symbols_with_invalid_ranges.append((symbol_str, 0))
        else:
            symbols_with_missing_ranges.append(symbol_str)

        # Add range validity flag to the result
        base_result['has_valid_range'] = has_valid_range
        
        # CRITICAL FIX: Check if trend data exists and store it directly in the candidate
        if hasattr(app, 'daily_trend_data') and symbol_str in app.daily_trend_data:
            trend_data = app.daily_trend_data[symbol_str]
            base_result['trend'] = trend_data.get('trend', 'N/A')
            base_result['trend_vwap'] = trend_data.get('vwap', 0)
            base_result['trend_sma200'] = trend_data.get('sma200', 0)
            base_result['vwap_correlation'] = trend_data.get('vwap_correlation', 0)
            logging.info(f"TREND_DATA: Stored trend data in candidate for {symbol_str}")
        else:
            # Record missing trend data
            logging.warning(f"TREND_DATA: No trend data available for {symbol_str}")

        # Add to combined results
        combined_results.append(base_result)

        # Track range width filtering statistics
        if is_continuous and 'range_width_filtering' not in app.filtering_stats:
            # Initialize range width filtering stats with the total number of unique symbols
            # This should happen only once at the beginning of processing
            app.filtering_stats['range_width_filtering'] = {
                'before': len(symbol_appearances),  # Total unique symbols before any filtering
                'after': 0  # Will be updated at the end
            }

        # Only add to candidates if it has a valid opening range in continuous mode
        # For the initial scan (not continuous), we don't filter by opening range
        if has_valid_range or not is_continuous:
            # Assign to long or short based on higher score
            if long_score >= short_score:
                base_result['final_score'] = long_score
                base_result['candidate_type'] = 'LONG'
                long_candidates.append(base_result)
            else:
                base_result['final_score'] = short_score
                base_result['candidate_type'] = 'SHORT'
                short_candidates.append(base_result)

    # Sort candidates by score (highest first)
    long_candidates.sort(key=lambda x: x.get('final_score', 0), reverse=True)
    short_candidates.sort(key=lambda x: x.get('final_score', 0), reverse=True)

    # Update app's combined results
    app.combinedResults = combined_results
    
    # When in continuous mode with trend filtering, we need to preserve existing candidates
    # while also adding any new ones found in this scan
    if is_continuous and apply_trend_filtering:
        logging.critical(f"CDG_TRACKING [RP_CANDIDATE_MERGE]: New candidates found: {len(long_candidates)} long, {len(short_candidates)} short")
        
        # If we already have longCandidates and/or shortCandidates attributes in app,
        # we've already preserved them in our local long_candidates/short_candidates variables
        # during initialization. Now we just need to update the app with these merged lists.
        
        # Log the merged candidates
        logging.critical(f"CDG_TRACKING [RP_CANDIDATE_MERGE]: Final merged candidates: {len(long_candidates)} long, {len(short_candidates)} short")
    
    # Update candidates
    app.longCandidates = long_candidates
    app.shortCandidates = short_candidates

    # Log the results
    logging.info(f"Processed {len(combined_results)} unique symbols")
    logging.info(f"Found {len(long_candidates)} long candidates and {len(short_candidates)} short candidates")
    logging.info(f"Symbols with missing opening ranges: {len(symbols_with_missing_ranges)}")
    logging.info(f"Symbols with invalid opening ranges: {len(symbols_with_invalid_ranges)}")
    
    # Add detailed candidate logging for debugging
    if len(long_candidates) > 0:
        long_symbols = [c.get('symbol', str(c)) if isinstance(c, dict) else str(c) for c in long_candidates[:5]]
        logging.critical(f"CDG_TRACKING [RP_FINAL_CANDIDATES]: Long candidates: {len(long_candidates)} (e.g. {long_symbols})")
    
    if len(short_candidates) > 0:
        short_symbols = [c.get('symbol', str(c)) if isinstance(c, dict) else str(c) for c in short_candidates[:5]]
        logging.critical(f"CDG_TRACKING [RP_FINAL_CANDIDATES]: Short candidates: {len(short_candidates)} (e.g. {short_symbols})")

    # CRITICAL FIX: Collect trend data for all candidates if we're in finalizing phase 
    if apply_trend_filtering:  # This indicates we're in the finalizing phase
        logging.info("In finalizing phase - collecting trend data for all candidates")
        # Import the trend collector
        from orbscanner.scanners.trend_collector import collect_trend_data_for_candidates
        
        # Combine long and short candidates for trend data collection
        all_candidates = long_candidates + short_candidates
        if all_candidates:
            logging.info(f"Starting trend data collection for {len(all_candidates)} candidates")
            try:
                symbols_with_data = collect_trend_data_for_candidates(app, all_candidates)
                logging.info(f"Successfully collected trend data for {symbols_with_data} candidates")
                
                # CRITICAL FIX: Now fill in any missing data from daily_trend_data to candidates
                # since trend data might have been collected but not copied to all candidates
                if hasattr(app, 'daily_trend_data'):
                    copied_count = 0
                    for candidate in all_candidates:
                        symbol = candidate.get('symbol')
                        if symbol and symbol in app.daily_trend_data:
                            # Check if trend data isn't already in the candidate
                            if 'trend' not in candidate or candidate['trend'] in (None, 'N/A', ''):
                                trend_data = app.daily_trend_data[symbol]
                                candidate['trend'] = trend_data.get('trend', 'N/A')
                                candidate['trend_vwap'] = trend_data.get('vwap', 0)
                                candidate['trend_sma200'] = trend_data.get('sma200', 0)
                                candidate['vwap_correlation'] = trend_data.get('vwap_correlation', 0.0)
                                copied_count += 1
                                logging.info(f"Copied missing trend data from daily_trend_data to candidate for {symbol}")
                    
                    if copied_count > 0:
                        logging.info(f"Filled in missing trend data for {copied_count} additional candidates")
            except Exception as e:
                logging.error(f"Error collecting trend data: {e}")
                logging.error(traceback.format_exc())
        else:
            logging.warning("No candidates found for trend data collection")
            
    # Update filtering statistics
    if 'initial_scan' in app.filtering_stats:
        app.filtering_stats['initial_scan']['after'] = len(combined_results)

    if 'range_width_filtering' in app.filtering_stats:
        app.filtering_stats['range_width_filtering']['after'] = len(long_candidates) + len(short_candidates)

    # Log the top candidates
    if long_candidates:
        top_long = long_candidates[0]
        logging.info(f"Top LONG candidate: {top_long['symbol']} (Score: {top_long.get('final_score', 0):.2f})")

    if short_candidates:
        top_short = short_candidates[0]
        logging.info(f"Top SHORT candidate: {top_short['symbol']} (Score: {top_short.get('final_score', 0):.2f})")

    # Log to selection logger
    app.selection_logger.log_final_candidates(long_candidates, short_candidates)

    return combined_results
