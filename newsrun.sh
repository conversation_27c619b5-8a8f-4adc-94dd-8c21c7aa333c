#!/bin/bash

# newsrun.sh - <PERSON><PERSON><PERSON> to manage news monitoring

SCREEN_NAME="news_monitor"

# Function to display menu
show_menu() {
    clear
    echo "==== News Monitor Management ===="
    echo "1. Start News Monitor (in screen)"
    echo "2. Connect to News Monitor screen"
    echo "3. Kill News Monitor process and screen"
    echo "0 or q. Exit"
    echo "==============================="
    echo -n "Enter your choice [0-3,q]: "
}

# Function to start the news monitor
start_monitor() {
    echo "Starting News Monitor in screen session..."
    
    # Check if screen session already exists
    if screen -list | grep -q "$SCREEN_NAME"; then
        echo "Error: Screen session '$SCREEN_NAME' already exists."
        echo "Use option 2 to connect or option 3 to kill it first."
        read -p "Press Enter to continue..."
        return
    fi
    
    # Create new screen session and run the monitor
    screen -dmS $SCREEN_NAME bash -c "source /home/<USER>/Scan7/venv/bin/activate && python3 /home/<USER>/Scan7/mon_news.py; exec bash"
    
    echo "News Monitor started in screen session '$SCREEN_NAME'"
    echo "Use option 2 to connect to the screen session"
    read -p "Press Enter to continue..."
}

# Function to connect to the screen session
connect_screen() {
    if ! screen -list | grep -q "$SCREEN_NAME"; then
        echo "Error: Screen session '$SCREEN_NAME' not found."
        echo "Use option 1 to start the monitor first."
        read -p "Press Enter to continue..."
        return
    fi
    
    echo "Connecting to screen session '$SCREEN_NAME'..."
    echo "Use Ctrl+A, D to detach from the screen session"
    read -p "Press Enter to connect..."
    screen -r $SCREEN_NAME
}

# Function to kill the monitor and screen
kill_monitor() {
    echo "Checking for all News Monitor related processes and screen sessions..."
    
    # Display all news-related processes for information
    echo "Current news-related processes:"
    ps -ef | grep -E "news|mon_news.py" | grep -v grep
    
    # Find and kill Python processes running mon_news.py
    PYTHON_PIDS=$(ps -ef | grep "python3.*mon_news.py" | grep -v grep | awk '{print $2}')
    if [ -n "$PYTHON_PIDS" ]; then
        echo "Found mon_news.py processes: $PYTHON_PIDS"
        for PID in $PYTHON_PIDS; do
            echo "Killing Python process $PID..."
            kill -9 $PID
        done
        echo "All mon_news.py Python processes terminated."
    else
        echo "No mon_news.py Python processes found."
    fi
    
    # Find and kill SCREEN processes related to news_monitor
    SCREEN_PIDS=$(ps -ef | grep "SCREEN.*$SCREEN_NAME" | grep -v grep | awk '{print $2}')
    if [ -n "$SCREEN_PIDS" ]; then
        echo "Found SCREEN processes: $SCREEN_PIDS"
        for PID in $SCREEN_PIDS; do
            echo "Killing SCREEN process $PID..."
            kill -9 $PID
        done
        echo "All SCREEN processes terminated."
    else
        echo "No SCREEN processes found."
    fi
    
    # Kill screen -r connection processes
    SCREEN_R_PIDS=$(ps -ef | grep "screen -r $SCREEN_NAME" | grep -v grep | awk '{print $2}')
    if [ -n "$SCREEN_R_PIDS" ]; then
        echo "Found screen -r connection processes: $SCREEN_R_PIDS"
        for PID in $SCREEN_R_PIDS; do
            echo "Killing screen -r process $PID..."
            kill -9 $PID
        done
        echo "All screen -r connection processes terminated."
    else
        echo "No screen -r connection processes found."
    fi
    
    # Also terminate screen sessions through screen command
    SCREENS=$(screen -list | grep "$SCREEN_NAME" | awk '{print $1}')
    if [ -n "$SCREENS" ]; then
        echo "Found screen sessions: $SCREENS"
        for SCR in $SCREENS; do
            echo "Terminating screen session $SCR..."
            screen -S $SCR -X quit 2>/dev/null || true
        done
        
        # Check for dead screens and wipe them
        if screen -list | grep -q "(Dead ???)"; then
            echo "Detected dead screen sessions, wiping them..."
            screen -wipe
        fi
        
        echo "All screen sessions terminated."
    else
        echo "No screen sessions found."
    fi
    
    # Verify all processes are gone
    echo "Verification - checking for remaining processes:"
    # Exclude the current newsrun.sh process from the check
    CURRENT_PID=$
    PARENT_PID=$PPID
    REMAINING=$(ps -ef | grep -E "news|mon_news.py|SCREEN.*$SCREEN_NAME" | grep -v grep | grep -v "$CURRENT_PID" | grep -v "$PARENT_PID")
    if [ -n "$REMAINING" ]; then
        echo "WARNING: Some processes may still be running:"
        echo "$REMAINING"
        echo "Attempting to kill remaining processes..."
        
        # Extract PIDs and kill them
        REMAINING_PIDS=$(echo "$REMAINING" | awk '{print $2}')
        for PID in $REMAINING_PIDS; do
            echo "Killing process $PID..."
            kill -9 $PID 2>/dev/null || true
        done
        
        # Final verification
        sleep 1
        FINAL_CHECK=$(ps -ef | grep -E "news|mon_news.py|SCREEN.*$SCREEN_NAME" | grep -v grep | grep -v "$CURRENT_PID" | grep -v "$PARENT_PID")
        if [ -n "$FINAL_CHECK" ]; then
            echo "WARNING: Some processes could not be terminated:"
            echo "$FINAL_CHECK"
        else
            echo "All processes successfully terminated."
        fi
    else
        echo "Verification complete: All processes successfully terminated."
    fi
    
    read -p "Press Enter to continue..."
}

# Main loop
while true; do
    show_menu
    read choice
    
    case $choice in
        1) start_monitor ;;
        2) connect_screen ;;
        3) kill_monitor ;;
        0|q|Q) echo "Exiting..."; exit 0 ;;
        *) echo "Invalid option. Press Enter to continue..."; read ;;
    esac
done