#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Monitoring Loop - Implements continuous monitoring of symbols during the MONITORING phase.
"""

import logging
import time
import threading
from datetime import datetime
import traceback

from orbscanner.utils.phase.manager import PhaseManager
from orbscanner.utils.phase.logging import log_to_phase

class MonitoringLoop:
    """
    Continuous monitoring loop for the MONITORING phase.
    This runs as a separate thread and periodically checks for breakouts,
    updates prices, and reports status for all monitored symbols.
    """
    
    def __init__(self, app):
        """
        Initialize the monitoring loop.
        
        Args:
            app: The main application instance (scanner_main.IBApp)
        """
        self.app = app
        self.is_running = False
        self.thread = None
        self.monitoring_interval = 1  # Check symbols every 1 second
        self.status_report_interval = 60  # Report status every 60 seconds
        self.last_status_report_time = 0
        self.logger = logging.getLogger(__name__)
        
    def start(self):
        """
        Start the monitoring loop in a separate thread.
        """
        if self.is_running:
            log_to_phase(PhaseManager.MONITORING, "Monitoring loop is already running", level=logging.WARNING)
            return False
            
        self.is_running = True
        self.thread = threading.Thread(target=self._run_loop)
        self.thread.daemon = True
        self.thread.start()
        
        log_to_phase(PhaseManager.MONITORING, "Started continuous monitoring loop", level=logging.INFO)
        return True
        
    def stop(self):
        """
        Stop the monitoring loop.
        """
        self.is_running = False
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
            
        log_to_phase(PhaseManager.MONITORING, "Stopped continuous monitoring loop", level=logging.INFO)
        
    def _run_loop(self):
        """
        Main monitoring loop.
        Continuously checks for breakouts and reports status.
        """
        log_to_phase(PhaseManager.MONITORING, "Monitoring loop started", level=logging.INFO)
        
        while self.is_running:
            try:
                # Skip if we're not in the monitoring phase
                if not hasattr(self.app, 'phase_manager') or self.app.phase_manager.current_phase != PhaseManager.MONITORING:
                    time.sleep(self.monitoring_interval)
                    continue
                    
                # Skip if breakout_detector is not initialized
                if not hasattr(self.app, 'breakout_detector') or self.app.breakout_detector is None:
                    log_to_phase(PhaseManager.MONITORING, "Breakout detector not initialized, cannot monitor symbols", level=logging.ERROR)
                    time.sleep(self.monitoring_interval)
                    continue
                
                # Collect current prices for monitored symbols
                symbol_prices = {}
                
                # Get monitored symbols from breakout detector
                monitored_symbols = self.app.breakout_detector.get_monitored_symbols()
                if not monitored_symbols:
                    # If no symbols being monitored, report it once a minute
                    current_time = time.time()
                    if current_time - self.last_status_report_time >= self.status_report_interval:
                        log_to_phase(PhaseManager.MONITORING, "No symbols currently being monitored", level=logging.WARNING)
                        self.last_status_report_time = current_time
                    
                    time.sleep(self.monitoring_interval)
                    continue
                
                # Collect current prices from real-time data
                if hasattr(self.app, 'real_time_data'):
                    for symbol in monitored_symbols:
                        if symbol in self.app.real_time_data:
                            rt_data = self.app.real_time_data[symbol]
                            last_price = rt_data.get('last_price', 0)
                            volume = rt_data.get('volume', 0)
                            
                            if last_price > 0:
                                symbol_prices[symbol] = {
                                    'price': last_price,
                                    'volume': volume
                                }
                
                # Check for breakouts if we have price data
                if symbol_prices:
                    # Check for breakouts
                    breakouts = self.app.breakout_detector.check_for_breakouts(symbol_prices)
                    
                    # Log any breakouts detected
                    if breakouts:
                        for breakout in breakouts:
                            log_to_phase(PhaseManager.MONITORING, f"BREAKOUT DETECTED: {breakout['symbol']} ({breakout['direction']})", level=logging.INFO)
                            log_to_phase(PhaseManager.MONITORING, f"Entry price: {breakout['entry_price']:.2f}, Range: {breakout['opening_low']:.2f}-{breakout['opening_high']:.2f}", level=logging.INFO)
                            
                    # Update prices for active trades
                    self.app.breakout_detector.update_prices(symbol_prices)
                
                # Report detailed status at regular intervals
                current_time = time.time()
                if current_time - self.last_status_report_time >= self.status_report_interval:
                    # Report monitoring status
                    log_to_phase(PhaseManager.MONITORING, f"Monitoring {len(monitored_symbols)} symbols for breakouts", level=logging.INFO)
                    
                    # Generate detailed status report
                    self.app.breakout_detector.report_detailed_status()
                    
                    # Update last report time
                    self.last_status_report_time = current_time
                
                # Short sleep to avoid high CPU usage
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                log_to_phase(PhaseManager.MONITORING, f"Error in monitoring loop: {str(e)}", level=logging.ERROR)
                log_to_phase(PhaseManager.MONITORING, f"Traceback: {traceback.format_exc()}", level=logging.ERROR)
                time.sleep(5)  # Sleep longer on error to avoid endless error loops
