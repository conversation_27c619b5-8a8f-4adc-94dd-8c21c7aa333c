from ibapi.client import *
from ibapi.common import TickerId
from ibapi.wrapper import *

port = 7497  # Changed from 7496 to match our environment

class TestApp(EClient, EWrapper):
    def __init__(self):
        EClient.__init__(self, self)
        self.nextValidOrderId = None  # Store the next valid order ID

    def nextValidId(self, orderId: OrderId):
        self.nextValidOrderId = orderId  # Save the order ID

        # Create a contract for AAPL
        mycontract = Contract()
        mycontract.symbol = "AAPL"  # Using AAPL instead of contract ID
        mycontract.secType = "STK"
        mycontract.exchange = "SMART"
        mycontract.currency = "USD"

        print(f"Requesting historical data for AAPL (reqId: {orderId})")
        self.reqHistoricalData(orderId, mycontract, "", "1 W", "1 day", "TRADES", 0, 1, False, [])

    def historicalData(self, reqId, bar):
        print(bar)


    def historicalDataEnd(self, reqId, start, end):
        print(f"Historical Data Ended for {reqId}. Started at {start}, ending at {end}")
        self.cancelHistoricalData(reqId)
        self.disconnect()

    def error(self, reqId: TickerId, errorCode: int, errorString: str, advancedOrderRejectJson=""):
        print(f"Error: {reqId}, Code: {errorCode}, Message: {errorString}")
        if advancedOrderRejectJson != "":
            print(f"AdvancedOrderRejectJson: {advancedOrderRejectJson}")

app = TestApp()
app.connect("127.0.0.1", port, 0)
app.run()