#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script to verify market data permissions for both historical and real-time data
Based on IBKR sample code
"""

import time
import threading
from datetime import datetime
import pytz
from ibapi.client import *
from ibapi.wrapper import *
from ibapi.contract import Contract
from ibapi.ticktype import TickTypeEnum

# Configuration
TWS_HOST = "127.0.0.1"
TWS_PORT = 7497  # 7497 for TWS paper trading, 7496 for TWS live, 4001/4002 for Gateway
CLIENT_ID = 123  # Random client ID
TIMEOUT = 30  # Seconds to wait for data before timing out (increased from 15 to 30)

class MarketDataTester(EWrapper, EClient):
    """
    Test application to verify market data permissions
    Tests both historical and real-time data requests
    """

    def __init__(self):
        EWrapper.__init__(self)
        EClient.__init__(self, self)

        # Request tracking
        self.next_req_id = 1
        self.historical_data_received = False
        self.historical_data_end_received = False
        self.historical_data_event = threading.Event()

        self.market_data_received = False
        self.market_data_event = threading.Event()

        self.error_messages = []

    def nextValidId(self, orderId: int):
        """Called when connection is established"""
        print(f"Connected to TWS/Gateway with client ID: {orderId}")

        # Start tests
        self.test_historical_data()

    def test_historical_data(self):
        """Test historical data request"""
        print("\n=== TESTING HISTORICAL DATA ===")

        # Create contract for AAPL
        contract = Contract()
        contract.symbol = "AAPL"
        contract.secType = "STK"
        contract.exchange = "SMART"
        contract.currency = "USD"

        # Request historical data
        req_id = self.next_req_id
        self.next_req_id += 1

        # Format end time (current time)
        et_timezone = pytz.timezone('US/Eastern')
        current_time = datetime.now(et_timezone)
        end_time_str = current_time.strftime('%Y%m%d %H:%M:%S') + " US/Eastern"

        print(f"Requesting 5-minute bars for AAPL ending at {end_time_str}")
        print(f"Parameters: durationStr=1 D, barSize=5 mins, whatToShow=TRADES")

        self.reqHistoricalData(
            reqId=req_id,
            contract=contract,
            endDateTime=end_time_str,
            durationStr="1 D",  # 1 day
            barSizeSetting="5 mins",
            whatToShow="TRADES",
            useRTH=1,
            formatDate=1,
            keepUpToDate=False,
            chartOptions=[]
        )

        # Wait for data with timeout
        print(f"Waiting up to {TIMEOUT} seconds for historical data...")
        received = self.historical_data_event.wait(timeout=TIMEOUT)

        if received:
            print("✅ HISTORICAL DATA TEST PASSED: Successfully received historical data")
        else:
            print("❌ HISTORICAL DATA TEST FAILED: No data received within timeout period")

        # Start real-time data test after historical test completes
        self.test_real_time_data()

    def test_real_time_data(self):
        """Test real-time market data request"""
        print("\n=== TESTING REAL-TIME MARKET DATA ===")

        # Create contract for AAPL
        contract = Contract()
        contract.symbol = "AAPL"
        contract.secType = "STK"
        contract.exchange = "SMART"
        contract.currency = "USD"

        # Request market data
        req_id = self.next_req_id
        self.next_req_id += 1

        print(f"Requesting real-time market data for AAPL (Req ID: {req_id})")

        # Request market data with common tick types
        self.reqMktData(
            reqId=req_id,
            contract=contract,
            genericTickList="233",  # 233 = RTVolume (real-time volume, price, and time)
            snapshot=False,
            regulatorySnapshot=False,
            mktDataOptions=[]
        )

        # Wait for data with timeout
        print(f"Waiting up to {TIMEOUT} seconds for real-time market data...")
        received = self.market_data_event.wait(timeout=TIMEOUT)

        if received:
            print("✅ REAL-TIME MARKET DATA TEST PASSED: Successfully received market data")
        else:
            print("❌ REAL-TIME MARKET DATA TEST FAILED: No data received within timeout period")

        # Disconnect after tests complete
        print("\n=== TEST SUMMARY ===")
        print(f"Historical Data Test: {'PASSED' if self.historical_data_received else 'FAILED'}")
        print(f"Real-Time Market Data Test: {'PASSED' if self.market_data_received else 'FAILED'}")

        if self.error_messages:
            print("\n=== ERROR MESSAGES ===")
            for error in self.error_messages:
                print(f"- {error}")

        self.disconnect()

    # EWrapper callbacks for historical data
    def historicalData(self, reqId, bar):
        """Called when historical data bar is received"""
        if not self.historical_data_received:
            print(f"Received historical data bar: {bar}")
            self.historical_data_received = True
            self.historical_data_event.set()

    def historicalDataEnd(self, reqId, start, end):
        """Called when all historical data is received"""
        print(f"Historical data request complete. Start: {start}, End: {end}")
        self.historical_data_end_received = True

        # In case we received the end but no data bars
        if not self.historical_data_received:
            print("Warning: Received historicalDataEnd but no data bars")
            self.historical_data_event.set()

    # EWrapper callbacks for real-time market data
    def tickPrice(self, reqId, tickType, price, attrib):
        """Called when price tick is received"""
        try:
            # Handle both old and new API versions
            tick_type_str = TickTypeEnum.to_str(tickType) if hasattr(TickTypeEnum, 'to_str') else str(tickType)
            print(f"Received price tick: {tick_type_str} = {price}")
        except Exception as e:
            print(f"Received price tick: Type {tickType} = {price} (Error formatting tick type: {e})")
        self.market_data_received = True
        self.market_data_event.set()

    def tickSize(self, reqId, tickType, size):
        """Called when size tick is received"""
        try:
            # Handle both old and new API versions
            tick_type_str = TickTypeEnum.to_str(tickType) if hasattr(TickTypeEnum, 'to_str') else str(tickType)
            print(f"Received size tick: {tick_type_str} = {size}")
        except Exception as e:
            print(f"Received size tick: Type {tickType} = {size} (Error formatting tick type: {e})")
        self.market_data_received = True
        self.market_data_event.set()

    def tickString(self, reqId, tickType, value):
        """Called when string tick is received"""
        try:
            # Handle both old and new API versions
            tick_type_str = TickTypeEnum.to_str(tickType) if hasattr(TickTypeEnum, 'to_str') else str(tickType)
            print(f"Received string tick: {tick_type_str} = {value}")
        except Exception as e:
            print(f"Received string tick: Type {tickType} = {value} (Error formatting tick type: {e})")
        self.market_data_received = True
        self.market_data_event.set()

    # Error handling
    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """Called when an error occurs"""
        error_msg = f"Error {errorCode}: {errorString}"
        print(f"❌ {error_msg}")
        self.error_messages.append(error_msg)

        # Set events for specific error codes to avoid hanging
        if errorCode in [10167, 321, 10071]:  # Market data permission errors
            if reqId == 1:  # Historical data request
                self.historical_data_event.set()
            else:  # Real-time data request
                self.market_data_event.set()

def main():
    """Main function to run the test"""
    print("=== IBKR MARKET DATA PERMISSION TEST ===")
    print(f"Connecting to TWS/Gateway at {TWS_HOST}:{TWS_PORT}")

    app = MarketDataTester()
    app.connect(TWS_HOST, TWS_PORT, CLIENT_ID)

    # Start the client thread
    api_thread = threading.Thread(target=app.run, daemon=True)
    api_thread.start()

    # Wait for the thread to complete or timeout
    max_wait = 60  # Maximum seconds to wait for all tests
    start_time = time.time()

    while api_thread.is_alive() and time.time() - start_time < max_wait:
        time.sleep(1)

    # Force disconnect if still running
    if app.isConnected():
        print("Test timeout reached, disconnecting...")
        app.disconnect()

if __name__ == "__main__":
    main()
