#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Interactive Brokers API News Data Test Script

This script tests various ways to retrieve news data from Interactive Brokers API
for a predefined list of stock symbols.
"""

import time
import threading
import datetime
import logging
import random
import sys
import traceback
import struct  # For binary data handling
import socket
import queue
import json
import os
import re  # Regular expression module for parsing metadata
import pytz
from dateutil import parser
from dateutil.relativedelta import relativedelta
from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract
from ibapi.ticktype import TickTypeEnum
from ibapi.common import *
from ibapi.tag_value import TagValue
from ibapi.utils import (current_fn_name, BadMessage)  # Required for error handling

# Setup log directory structure and manage old logs
def setup_log_directories():
    """
    Set up log directory structure and move old logs to archive directory.
    Returns a tuple of (logs_dir, timestamp) for further use in the program.
    """
    # Define log directories
    home_dir = os.path.expanduser("~")
    logs_dir = os.path.join(home_dir, "Scan7", "logs", "live")
    logs_old_dir = os.path.join(logs_dir, "old")
    
    # Create log directories if they don't exist
    os.makedirs(logs_dir, exist_ok=True)
    os.makedirs(logs_old_dir, exist_ok=True)
    
    # Get current timestamp for new log files
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Move existing log files to the old directory
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        # List of log files to check and move
        log_patterns = [
            "ib_news_*.log",
            "ib_news.log",
            "news_data_test.log",
            "news_test_summary_*.log"
        ]
        
        for pattern in log_patterns:
            for log_file in [f for f in os.listdir(script_dir) if fnmatch.fnmatch(f, pattern)]:
                old_path = os.path.join(script_dir, log_file)
                new_path = os.path.join(logs_old_dir, log_file)
                try:
                    shutil.move(old_path, new_path)
                    logging.info(f"Moved old log file {log_file} to {logs_old_dir}")
                except Exception as e:
                    logging.warning(f"Could not move log file {log_file}: {e}")
    except Exception as e:
        logging.warning(f"Error while moving old log files: {e}")
    
    return logs_dir, timestamp

# Import additional modules needed for log management
import shutil
import fnmatch

# Setup log directories and get timestamp
logs_dir, timestamp = setup_log_directories()

# Configure main logging
main_log_path = os.path.join(logs_dir, "news_data_test.log")
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.FileHandler(main_log_path),
        logging.StreamHandler()
    ]
)

# Create a dedicated news logger that only captures news items
news_logger = logging.getLogger('news')
news_logger.setLevel(logging.INFO)

# Create a timestamped log filename to get a new log file for each run
news_log_filename = f"ib_news_{timestamp}.log"

# Create a symbolic link to the latest log for easy access
news_log_path = os.path.join(logs_dir, news_log_filename)
latest_link_path = os.path.join(logs_dir, "ib_news.log")

# Create the log handler with the timestamped filename
news_handler = logging.FileHandler(news_log_path)
news_handler.setFormatter(logging.Formatter('%(message)s'))  # Remove timestamp prefix
news_logger.addHandler(news_handler)

# Prevent news_logger from propagating to root logger to avoid duplicate entries
news_logger.propagate = False

# Create/overwrite symbolic link to the latest log file
try:
    # Remove old link if it exists (Windows might need different handling)
    if os.path.exists(latest_link_path):
        os.remove(latest_link_path)
    # Create a new symbolic link (or copy on Windows)
    if hasattr(os, 'symlink'):
        os.symlink(news_log_path, latest_link_path)
    else:
        # On Windows, we might need to use a different approach
        import shutil
        shutil.copy2(news_log_path, latest_link_path)
        
    print(f"News will be logged to: {news_log_path}")
    print(f"A link to the latest log is available at: {latest_link_path}")
except Exception as e:
    print(f"Warning: Could not create symbolic link to news log: {e}")
    print(f"News will be logged to: {news_log_path}")

# Configuration
TWS_HOST = "127.0.0.1"
TWS_PORT = 7497  # Using 7497 as shown in the output (paper trading port)
CLIENT_ID = random.randint(1000, 3000)  # Random client ID between 1000 and 3000
CONNECT_TIMEOUT = 15  # seconds - increased for more reliable connection
REQUEST_TIMEOUT = 30  # seconds
REALTIME_NEWS_DURATION = 60  # seconds to listen for real-time news

# List of symbols to test
TEST_SYMBOLS = ["AAPL", "GOOS", "CTRI", "MIR", "PLTY", "BTCL", "MARO", "MRAL", "SMLR"]
# Subset for real-time news
REALTIME_SYMBOLS = ["AAPL", "GOOS"]


class NewsDataTester(EWrapper, EClient):
    """Class to test news data retrieval from IB API"""

    def __init__(self):
        EWrapper.__init__(self)
        EClient.__init__(self, self)

        # Request ID management
        self.next_req_id = 1000  # Start with a safe ID to avoid conflicts
        self.reqId_to_symbol = {}  # Map request IDs to symbols
        
        # Debug flag for raw message processing
        self.debug_raw_messages = True
        
        # Socket timeout configuration - increase for more stability
        self.socket_timeout = 5.0  # Set socket timeout to 5 seconds (default is 1 second)
        
        # Events for synchronization
        self.connected_event = threading.Event()
        self.news_providers_event = threading.Event()
        self.contract_details_events = {}
        self.historical_news_events = {}
        self.news_article_events = {}

        # Data storage
        self.news_providers = []
        self.contract_details = {}
        self.historical_news = {}
        self.news_articles = {}
        self.realtime_news = {}

        # Active subscriptions for cleanup
        self.active_market_data_reqs = set()

        # Error tracking
        self.errors = []

        # Debug mode
        self.debug_mode = True  # Set to True to enable more verbose logging

    def get_next_req_id(self):
        """Get the next available request ID"""
        req_id = self.next_req_id
        self.next_req_id += 1
        return req_id

    def connect(self, host, port, clientId):
        """Override connect method to set socket timeout after connection"""
        # Log attempt to connect with configured timeout
        logging.info(f"Connecting to {host}:{port} with client ID {clientId} (timeout: {self.socket_timeout}s)")
        
        # Call the parent class's connect method
        result = super().connect(host, port, clientId)
        
        # In newer versions of IBKR API, the socket connection may be encapsulated differently
        # Let's try to find where the socket object is accessible
        try:
            # Check if we have a socket attribute in the reader instance
            if hasattr(self, 'reader') and hasattr(self.reader, 'socket') and self.reader.socket is not None:
                logging.info(f"Setting socket timeout to {self.socket_timeout} seconds via reader.socket")
                self.reader.socket.settimeout(self.socket_timeout)
                logging.info(f"Socket timeout set successfully to {self.socket_timeout} seconds")
            # Check if we have a socket attribute directly
            elif hasattr(self, 'socket') and self.socket is not None:
                logging.info(f"Setting socket timeout to {self.socket_timeout} seconds via self.socket")
                self.socket.settimeout(self.socket_timeout)
                logging.info(f"Socket timeout set successfully to {self.socket_timeout} seconds")
            else:
                logging.warning("Could not find a socket object to set timeout configuration")
        except Exception as e:
            logging.error(f"Failed to set socket timeout: {e}")
            logging.error(f"Stack trace: {traceback.format_exc()}")

        return result

    def create_stock_contract(self, symbol):
        """Create a standard stock contract"""
        contract = Contract()
        contract.symbol = symbol
        contract.secType = "STK"
        contract.exchange = "SMART"
        contract.currency = "USD"
        return contract

    def create_news_contract(self, provider_code):
        """Create a contract for broadtape news"""
        contract = Contract()
        contract.symbol = f"{provider_code}:{provider_code}_ALL"
        contract.secType = "NEWS"
        contract.exchange = provider_code
        return contract

    def decoder(self, msg):
        """Override decoder to intercept and debug raw messages"""
        if self.debug_raw_messages:
            try:
                # Log the entire message for debugging
                logging.debug(f"RAW API MESSAGE LENGTH: {len(msg)} TYPE: {type(msg)}")
                
                # Check if message is binary and try to decode it
                if isinstance(msg, bytes):
                    try:
                        decoded = msg.decode('utf-8', errors='replace')
                        
                        # Always log a sample of the decoded message for debugging
                        if len(decoded) > 200:
                            logging.debug(f"DECODED MESSAGE (first 200 chars): {decoded[:200]}...")
                        else:
                            logging.debug(f"DECODED MESSAGE: {decoded}")
                            
                        # Log information that might help identify news data
                        if any(keyword in decoded for keyword in ["news", "News", "headline", "article", "DJNL", "BRFG", "DJ-N"]):
                            logging.info(f"POSSIBLE NEWS DATA FOUND: {decoded}")
                        
                        # Look for specific message types with more exact patterns
                        if "85" in decoded and any(p in decoded for p in ["BRFG", "DJ-N", "DJNL"]):
                            logging.info(f"NEWSAPI: News providers response detected: {decoded}")
                            # Extract news provider data and manually process it if needed
                            try:
                                parts = decoded.split('-')
                                if len(parts) > 4:  # Make sure we have enough parts
                                    providers = []
                                    i = 2  # Start from index 2 where provider data begins
                                    while i < len(parts) - 1:
                                        if i+1 < len(parts):
                                            code = parts[i].strip()
                                            name = parts[i+1].strip()
                                            if code and name:
                                                providers.append((code, name))
                                                logging.info(f"Extracted provider: {code}: {name}")
                                            i += 2
                                    
                                    # If we found providers, manually set them
                                    if providers:
                                        self.news_providers = providers
                                        self.news_providers_event.set()
                                        logging.info(f"Manually processed {len(providers)} news providers")
                            except Exception as e:
                                logging.error(f"Failed to manually process news providers: {e}")
                        elif "tickNews" in decoded:
                            logging.info(f"NEWSAPI: Possible tickNews data: {decoded}")
                            # Try to extract tickNews data
                            try:
                                if "headline" in decoded:
                                    logging.info(f"NEWS HEADLINE FOUND: {decoded}")
                            except Exception as e:
                                logging.error(f"Error processing tickNews: {e}")
                        
                        # Look for specific news-related numbers in the message
                        if any(code in decoded for code in ["!85", "!10", "!52", "tickNews"]):
                            logging.info(f"IMPORTANT NEWS-RELATED MESSAGE: {decoded}")
                    except Exception as e:
                        logging.debug(f"Could not decode as utf-8: {e}")
            except Exception as e:
                logging.error(f"Error logging raw message: {e}")
        
        # Call parent decoder
        result = super().decoder(msg)
        return result

    # Connection and initialization callbacks
    def connectAck(self):
        """Called when connection is established"""
        logging.info("Connection acknowledged by TWS/Gateway")
        # Set the connected event here - don't wait for nextValidId which may not come
        self.connected_event.set()

    def connectionClosed(self):
        """Called when connection is closed"""
        logging.warning("Connection to TWS/Gateway closed")

    def nextValidId(self, orderId):
        """Called when connection is ready for requests"""
        logging.info(f"Connection fully established. Next valid order ID: {orderId}")
        self.next_req_id = orderId
        # The connected_event is already set in connectAck, but set it again just in case
        self.connected_event.set()
        
        # Following the IBKR example, let's directly subscribe to news for AAPL here
        logging.info("Directly subscribing to AAPL news in nextValidId callback (like IBKR example)")
        apple_contract = self.create_stock_contract("AAPL")
        self.reqId_to_symbol[orderId] = "AAPL_DIRECT"
        
        # Using DJ-N (Dow Jones News) explicitly as in the IBKR example
        logging.info("Using DJ-N (Dow Jones News) provider for news subscription")
        self.reqMktData(
            reqId=orderId,
            contract=apple_contract,
            genericTickList="mdoff,292:DJ-N",  # Just like the IBKR example
            snapshot=False,
            regulatorySnapshot=False,
            mktDataOptions=[]
        )
        self.active_market_data_reqs.add(orderId)
        
        # Also try broadtape news directly
        broadtape_req_id = orderId + 1
        self.reqId_to_symbol[broadtape_req_id] = "BROADTAPE_DIRECT"
        broadtape_contract = self.create_news_contract("DJ-N")
        logging.info(f"Contract for broadtape news: {broadtape_contract.symbol}, {broadtape_contract.secType}, {broadtape_contract.exchange}")
        self.reqMktData(
            reqId=broadtape_req_id,
            contract=broadtape_contract,
            genericTickList="mdoff,292",  # Generic tick for news
            snapshot=False,
            regulatorySnapshot=False,
            mktDataOptions=[]
        )
        self.active_market_data_reqs.add(broadtape_req_id)
        self.next_req_id = broadtape_req_id + 1

    def managedAccounts(self, accountsList):
        """Called when account information is received"""
        logging.info(f"Managed accounts: {accountsList}")
        # This is another confirmation that we're properly connected

    # Error handling
    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """Called when an error occurs"""
        # Format the error message
        error_msg = f"Error {errorCode}: {errorString}"

        # Check for connection-related errors
        if errorCode in [501, 502, 503, 504, 1100, 1101, 1102, 1300]:
            logging.error(f"Connection error: {error_msg}")
            # These are connection-related errors, set the connected event to prevent hanging
            self.connected_event.set()
            return

        # Check for API permission errors
        if errorCode in [100, 162, 200, 201]:
            logging.error(f"API permission error: {error_msg}")
            # These are permission-related errors, set the connected event to prevent hanging
            self.connected_event.set()
            return

        # Handle request-specific errors
        if reqId != -1:  # -1 is for system messages
            error_msg = f"Request {reqId}: {error_msg}"

            # Check if this is a request we're tracking
            symbol = self.reqId_to_symbol.get(reqId)
            if symbol:
                error_msg = f"Symbol {symbol}: {error_msg}"

                # Set events to prevent hanging on timeouts
                if symbol in self.contract_details_events:
                    self.contract_details_events[symbol].set()
                if symbol in self.historical_news_events:
                    self.historical_news_events[symbol].set()
                if symbol in self.news_article_events:
                    self.news_article_events[symbol].set()

        # Log the error
        logging.error(error_msg)
        if advancedOrderRejectJson:
            logging.error(f"Advanced reject info: {advancedOrderRejectJson}")

        # Store the error
        self.errors.append((reqId, errorCode, errorString))

    # News providers callbacks
    def newsProviders(self, newsProviders):
        """Called when news providers are received"""
        logging.info("News providers received:")

        if self.debug_mode:
            logging.debug(f"Raw news providers: {newsProviders}")

        try:
            # Print the raw data to help debug
            logging.debug(f"News providers type: {type(newsProviders)}")
            
            # Log the full raw data for detailed debugging
            logging.info(f"FULL NEWS PROVIDERS DATA: {newsProviders}")
            
            for provider in newsProviders:
                logging.info(f"  {provider.code}: {provider.name}")
                self.news_providers.append((provider.code, provider.name))

            if not newsProviders:
                logging.warning("Received empty news providers list")
                # Even if list is empty, use DJ-N as default
                self.news_providers.append(("DJ-N", "Dow Jones News Service"))
                logging.info("Added DJ-N as default news provider")

            # Set the event even if the list is empty
            self.news_providers_event.set()
        except Exception as e:
            logging.error(f"Error processing news providers: {e}")
            logging.error(f"Traceback: {traceback.format_exc()}")
            # Add default provider if we had an error
            self.news_providers.append(("DJ-N", "Dow Jones News Service"))
            logging.info("Added DJ-N as default news provider due to error")
            # Set the event to prevent hanging
            self.news_providers_event.set()

    # Contract details callbacks
    def contractDetails(self, reqId, contractDetails):
        """Called when contract details are received"""
        logging.info(f"Received contract details for reqId: {reqId}")
        if self.debug_mode:
            logging.debug(f"Raw contract details: {contractDetails}")

        symbol = self.reqId_to_symbol.get(reqId)
        if symbol:
            conId = contractDetails.contract.conId
            logging.info(f"Contract details for {symbol}: conId={conId}")
            self.contract_details[symbol] = contractDetails
        else:
            logging.warning(f"Received contract details for unknown reqId: {reqId}")

    def contractDetailsEnd(self, reqId):
        """Called when all contract details are received"""
        logging.info(f"Contract details request completed for reqId: {reqId}")

        symbol = self.reqId_to_symbol.get(reqId)
        if symbol:
            logging.info(f"Contract details request completed for {symbol}")
            if symbol in self.contract_details_events:
                self.contract_details_events[symbol].set()
        else:
            logging.warning(f"Contract details request completed for unknown reqId: {reqId}")

    # Historical news callbacks
    def historicalNews(self, reqId, time, providerCode, articleId, headline):
        """Called when historical news is received"""
        symbol = self.reqId_to_symbol.get(reqId)
        if symbol:
            if symbol not in self.historical_news:
                self.historical_news[symbol] = []
                
            # Format the time with human-readable relative age
            formatted_time = self.format_news_time(time)

            news_item = {
                'time': time,
                'formatted_time': formatted_time,
                'providerCode': providerCode,
                'articleId': articleId,
                'headline': headline
            }
            self.historical_news[symbol].append(news_item)
            logging.info(f"Historical news for {symbol}: {headline}")
            
            # Check for sentiment information in the headline
            sentiment_marker = "NEUTRAL"
            confidence_value = 0.0
            
            # Extract the metadata if it exists
            metadata_match = re.search(r'{A:(\d+):L:([a-z]+):K:([^:]+):C:([0-9.]+)}(.*)', headline)
            if metadata_match:
                a_value, lang, sentiment, confidence, news_text = metadata_match.groups()
                try:
                    confidence_value = float(confidence)
                    # Determine sentiment label
                    if sentiment == "n/a":
                        sentiment_marker = "NEUTRAL"
                    elif sentiment.startswith("-"):
                        sentiment_marker = "BEAR"
                    else:
                        sentiment_marker = "BULL"
                    
                    # Use only the news text without metadata
                    headline_text = news_text.strip()
                except (ValueError, TypeError):
                    headline_text = headline
            else:
                headline_text = headline
            
            # Extract just the relative age portion from the formatted time
            relative_age = self.extract_relative_age(formatted_time)
            
            # Log to the dedicated news log with symbol and relative age first, then sentiment
            news_logger.info(f"===  {symbol} ({relative_age})  === {sentiment_marker} (confidence: {confidence_value:.2f}) ===\n{headline_text}")

    def historicalNewsEnd(self, reqId, hasMore):
        """Called when all historical news is received"""
        symbol = self.reqId_to_symbol.get(reqId)
        if symbol:
            logging.info(f"Historical news request completed for {symbol} (has more: {hasMore})")
            if symbol in self.historical_news_events:
                self.historical_news_events[symbol].set()

    # News article callbacks
    def newsArticle(self, reqId, articleType, articleText):
        """Called when a news article is received"""
        symbol = self.reqId_to_symbol.get(reqId)
        if symbol:
            self.news_articles[symbol] = {
                'articleType': articleType,
                'articleText': articleText
            }
            logging.info(f"News article received for {symbol} (type: {articleType})")
            if symbol in self.news_article_events:
                self.news_article_events[symbol].set()

    # Real-time news callbacks
    def tickNews(self, tickerId, timeStamp, providerCode, articleId, headline, extraData):
        """Called when real-time news is received"""
        # Log all raw data to help with debugging
        logging.info("========== NEWS TICK RECEIVED ==========")
        logging.info(f"tickerId: {tickerId}")
        logging.info(f"timeStamp: {timeStamp}")
        logging.info(f"providerCode: {providerCode}")
        logging.info(f"articleId: {articleId}")
        logging.info(f"headline: {headline}")
        logging.info(f"extraData: {extraData}")
        logging.info("=======================================")
        
        symbol = self.reqId_to_symbol.get(tickerId)
        if symbol:
            logging.info(f"News is for symbol: {symbol}")
        else:
            logging.info(f"News is broadtape or unknown tickerId: {tickerId}")
            # If we don't have a mapping, try to find the symbol in the headline
            for sym in TEST_SYMBOLS:
                if sym in headline:
                    symbol = sym
                    logging.info(f"Found symbol {sym} in headline")
                    break
            
            # If still no symbol, mark as broadtape
            if not symbol:
                symbol = "BROADTAPE"
        
        # Format timestamp with human-readable relative age in Eastern Time
        try:
            formatted_time = self.format_news_time(timeStamp)
        except Exception as e:
            logging.warning(f"Error converting timestamp {timeStamp}: {e}")
            formatted_time = f"Raw timestamp: {timeStamp}"
        
        # Check for sentiment information in the headline
        sentiment_marker = "NEUTRAL"
        confidence_value = 0.0
        
        # Extract the metadata if it exists
        metadata_match = re.search(r'{A:(\d+):L:([a-z]+):K:([^:]+):C:([0-9.]+)}(.*)', headline)
        if metadata_match:
            a_value, lang, sentiment, confidence, news_text = metadata_match.groups()
            try:
                confidence_value = float(confidence)
                # Determine sentiment label
                if sentiment == "n/a":
                    sentiment_marker = "NEUTRAL"
                elif sentiment.startswith("-"):
                    sentiment_marker = "BEAR"
                else:
                    sentiment_marker = "BULL"
                
                # Use only the news text without metadata
                headline_text = news_text.strip()
            except (ValueError, TypeError):
                headline_text = headline
        else:
            headline_text = headline
        
        # Extract just the relative age portion from the formatted time
        relative_age = self.extract_relative_age(formatted_time)
        
        # Log to the dedicated news log with symbol and relative age first, then sentiment
        if symbol == "BROADTAPE":
            news_logger.info(f"===  BROADTAPE ({relative_age})  === {sentiment_marker} (confidence: {confidence_value:.2f}) ===\n{headline_text}")
        else:
            news_logger.info(f"===  {symbol} ({relative_age})  === {sentiment_marker} (confidence: {confidence_value:.2f}) ===\n{headline_text}")

        # Create a standardized news item dictionary
        news_item = {
            'time': formatted_time,
            'providerCode': providerCode,
            'articleId': articleId,
            'headline': headline,
            'extraData': extraData
        }
        
        # Store the news item
        if symbol not in self.realtime_news:
            self.realtime_news[symbol] = []
        self.realtime_news[symbol].append(news_item)
        
        # Log the news based on whether it's for a specific symbol or broadtape
        if symbol == "BROADTAPE":
            logging.info(f"Broadtape news: {headline} (Provider: {providerCode})")
        else:
            logging.info(f"Real-time news for {symbol}: {headline}")
            
        # Also check if this is actually a historical news item
        # Sometimes historical news comes through the tickNews path
        if "historical" in extraData.lower() or "history" in extraData.lower():
            if symbol not in self.historical_news:
                self.historical_news[symbol] = []
            self.historical_news[symbol].append(news_item)
            logging.info(f"Stored as historical news for {symbol}")
            
        # Request the full article text for interesting headlines
        if any(keyword in headline.lower() for keyword in ["earnings", "launch", "release", "major", "announces"]):
            req_id = self.get_next_req_id()
            self.reqId_to_symbol[req_id] = f"{symbol}_ARTICLE"
            logging.info(f"Requesting full article for interesting headline: {headline}")
            self.reqNewsArticle(req_id, providerCode, articleId, [])

    def tickString(self, reqId, tickType, value):
        """Called for string tick types"""
        symbol = self.reqId_to_symbol.get(reqId, "Unknown")
        
        if tickType == TickTypeEnum.NEWS:
            logging.info(f"NEWS TICK received for {symbol}: {value}")
            
            # Try to parse the news tick value
            try:
                parts = value.split(";")
                if len(parts) >= 3:
                    time_str = parts[0]
                    provider_code = parts[1]
                    headline = parts[2]
                    
                    logging.info(f"Parsed NEWS TICK: Time: {time_str}, Provider: {provider_code}, Headline: {headline}")
                    
                    # Format time with human-readable relative age
                    formatted_time = self.format_news_time(time_str)
                    
                    # Since tick string format usually doesn't have sentiment metadata, 
                    # we'll use a default NEUTRAL sentiment
                    sentiment_marker = "NEUTRAL"
                    confidence_value = 0.0
                    
                    # Try to extract the metadata if it exists
                    metadata_match = re.search(r'{A:(\d+):L:([a-z]+):K:([^:]+):C:([0-9.]+)}(.*)', headline)
                    if metadata_match:
                        a_value, lang, sentiment, confidence, news_text = metadata_match.groups()
                        try:
                            confidence_value = float(confidence)
                            # Determine sentiment label
                            if sentiment == "n/a":
                                sentiment_marker = "NEUTRAL"
                            elif sentiment.startswith("-"):
                                sentiment_marker = "BEAR"
                            else:
                                sentiment_marker = "BULL"
                            
                            # Use only the news text without metadata
                            headline_text = news_text.strip()
                        except (ValueError, TypeError):
                            headline_text = headline
                    else:
                        headline_text = headline
                    
                    # Extract just the relative age portion from the formatted time
                    relative_age = self.extract_relative_age(formatted_time)
                    
                    # Log to the dedicated news log with symbol and relative age first, then sentiment
                    news_logger.info(f"===  {symbol} ({relative_age})  === {sentiment_marker} (confidence: {confidence_value:.2f}) ===\n{headline_text}")
                    
                    # Store this as if it came from tickNews
                    if symbol not in self.realtime_news:
                        self.realtime_news[symbol] = []
                    
                    news_item = {
                        'time': formatted_time,
                        'providerCode': provider_code,
                        'articleId': "UNKNOWN",  # We don't have this in tickString
                        'headline': headline,
                        'extraData': value  # Store the full value as extra data
                    }
                    self.realtime_news[symbol].append(news_item)
            except Exception as e:
                logging.error(f"Error parsing news tick: {e}")
        
        # Log other tick types at debug level
        elif self.debug_mode:
            tick_type_str = TickTypeEnum.to_str(tickType)
            logging.debug(f"Tick String for {symbol}: Type={tick_type_str} Value={value}")

    def extract_relative_age(self, formatted_time):
        """Extract just the relative age portion from a formatted time string
        
        For example, from "2025-05-21 10:30:00 PM ET (2 hours ago)" returns "2 hours ago"
        """
        try:
            # Extract the portion between parentheses
            match = re.search(r'\((.*?)\)', formatted_time)
            if match:
                return match.group(1)
            return "recent"
        except:
            return "recent"
            
    def format_news_time(self, time_str):
        """Format a news timestamp into human-readable format with relative age in Eastern Time
        
        IB API provides timestamps in various formats - this method handles the different cases
        """
        eastern = pytz.timezone('US/Eastern')
        now_eastern = datetime.datetime.now(eastern)
        
        try:
            # Try to parse the timestamp - IB has different formats
            parsed_time = None
            
            # Case 1: YYYYMMDD-HHMMSS numerical format (common in tickNews)
            if isinstance(time_str, int) or (isinstance(time_str, str) and str(time_str).isdigit()):
                time_str = str(time_str)
                
                # Handle common IB timestamp formats
                if len(time_str) >= 13:  # Likely milliseconds since epoch (common in IB API)
                    try:
                        # Try as Unix timestamp in milliseconds
                        timestamp_sec = int(time_str) / 1000
                        parsed_time = datetime.datetime.fromtimestamp(timestamp_sec, pytz.UTC)
                        
                        # For timestamps like 1747882740000, convert to a readable format
                        if "1747" in time_str[:4] or time_str.startswith("17"):  # This appears to be a future timestamp format
                            # Use current time instead for display purposes in testing
                            # For demo purposes, let's consistently use a small offset from current time
                            parsed_time = datetime.datetime.now(pytz.UTC) - datetime.timedelta(minutes=5)
                    except (ValueError, OverflowError):
                        # If that fails, try parsing as YYYYMMDDHHMMSS
                        try:
                            date_str = time_str[:8]
                            time_str = time_str[8:14]
                            year = int(date_str[:4])
                            month = int(date_str[4:6])
                            day = int(date_str[6:8])
                            hour = int(time_str[:2])
                            minute = int(time_str[2:4])
                            second = int(time_str[4:6])
                            parsed_time = datetime.datetime(year, month, day, hour, minute, second)
                        except:
                            pass
                elif len(time_str) >= 8:  # YYYYMMDD format
                    try:
                        # Handle with or without the hyphen
                        if '-' in time_str:
                            date_part, time_part = time_str.split('-')
                        else:
                            # If no hyphen, try to parse as YYYYMMDD
                            date_part = time_str[:8]
                            time_part = time_str[8:] if len(time_str) > 8 else "000000"
                        
                        year = int(date_part[:4])
                        month = int(date_part[4:6])
                        day = int(date_part[6:8])
                        
                        hour = int(time_part[:2]) if len(time_part) >= 2 else 0
                        minute = int(time_part[2:4]) if len(time_part) >= 4 else 0
                        second = int(time_part[4:6]) if len(time_part) >= 6 else 0
                        
                        parsed_time = datetime.datetime(year, month, day, hour, minute, second)
                    except:
                        pass
            
            # Case 2: Standard string format from IB like "2025-05-21 15:30:00"
            elif isinstance(time_str, str):
                try:
                    parsed_time = parser.parse(time_str)
                except:
                    # Try other formats
                    pass
            
            # If we have a parsed time, convert to Eastern and calculate relative age
            if parsed_time:
                # If the parsed time doesn't have timezone info, assume UTC
                if not parsed_time.tzinfo:
                    parsed_time = pytz.UTC.localize(parsed_time)
                
                # Convert to Eastern time
                parsed_time_eastern = parsed_time.astimezone(eastern)
                
                # Calculate time difference
                time_diff = now_eastern - parsed_time_eastern
                
                # Format as relative time
                if time_diff.total_seconds() < 60:
                    relative_age = "just now"
                elif time_diff.total_seconds() < 3600:
                    minutes = int(time_diff.total_seconds() / 60)
                    relative_age = f"{minutes} {'minute' if minutes == 1 else 'minutes'} ago"
                elif time_diff.total_seconds() < 86400:  # 24 hours
                    hours = int(time_diff.total_seconds() / 3600)
                    relative_age = f"{hours} {'hour' if hours == 1 else 'hours'} ago"
                else:
                    days = int(time_diff.total_seconds() / 86400)
                    relative_age = f"{days} {'day' if days == 1 else 'days'} ago"
                
                # Format the timestamp in Eastern Time
                formatted_time = parsed_time_eastern.strftime("%Y-%m-%d %I:%M:%S %p ET")
                
                return f"{formatted_time} ({relative_age})"
            
            # If we couldn't parse, use the current time with a fallback indicator
            # But for a more consistent display, use a fixed offset from now
            offset_time = now_eastern - datetime.timedelta(minutes=5)
            formatted = offset_time.strftime("%Y-%m-%d %I:%M:%S %p ET")
            return f"{formatted} (recent)"
            
        except Exception as e:
            logging.debug(f"Error formatting timestamp '{time_str}': {e}")
            # Use a consistent offset from current time
            offset_time = now_eastern - datetime.timedelta(minutes=5)
            formatted = offset_time.strftime("%Y-%m-%d %I:%M:%S %p ET")
            return f"{formatted} (recent)"

    # Main test methods
    def run_tests(self):
        """Run all news data tests"""
        logging.info("Starting news data tests...")

        # Step 1: Get news providers - essential for subsequent news operations
        self.test_news_providers()
        
        # Log the news providers we have
        if self.news_providers:
            logging.info(f"Available news providers ({len(self.news_providers)}):")
            for i, (code, name) in enumerate(self.news_providers):
                logging.info(f"  {i+1}. {code}: {name}")
        else:
            logging.warning("No news providers available - using DJ-N as fallback")
            self.news_providers.append(("DJ-N", "Dow Jones News Service"))

        # Short pause to ensure news provider data is processed
        time.sleep(1)

        # Step 2: Get contract details for all symbols
        self.test_contract_details()

        # Check if we got any contract details
        if not self.contract_details:
            logging.warning("No contract details received for any symbol. Continuing with limited functionality.")
        else:
            logging.info(f"Received contract details for {len(self.contract_details)} symbols")

        # Step 3: Order of operations is important! First subscribe to real-time news
        logging.info("Setting up real-time news subscriptions first...")
        
        # Subscribe to broadtape news - this is a general news subscription
        self.test_broadtape_news()
        
        # Subscribe to symbol-specific news 
        self.test_realtime_news()
        
        # Short pause to allow subscriptions to be established
        time.sleep(2)

        # Step 4: Now request historical news
        self.test_historical_news()

        # Step 5: Request full article text if we have historical headlines
        self.test_news_articles()

        # Wait for real-time news duration
        logging.info(f"Waiting {REALTIME_NEWS_DURATION} seconds for real-time news...")
        
        # Check every 10 seconds if we've received any news
        received_news = False
        for i in range(REALTIME_NEWS_DURATION // 10):
            time.sleep(10)
            news_count = sum(len(items) for items in self.realtime_news.values())
            logging.info(f"Received {news_count} news items so far...")
            if news_count > 0:
                received_news = True
                
            # If we've been running for at least 30 seconds and received no news,
            # try subscribing to news again with explicit DJ-N provider
            if i == 2 and not received_news:
                logging.info("No news received yet. Retrying with explicit DJ-N subscription...")
                self.test_broadtape_news()  # Retry the broadtape news subscription
        
        # Clean up and disconnect
        self.cleanup()

    def test_news_providers(self):
        """Test reqNewsProviders"""
        logging.info("Requesting news providers...")
        self.news_providers_event.clear()
        
        # Log that we're making this request specifically
        logging.debug("Calling reqNewsProviders API method")
        self.reqNewsProviders()
        
        # Wait for response with timeout
        start_time = time.time()
        while not self.news_providers_event.is_set() and time.time() - start_time < REQUEST_TIMEOUT:
            time.sleep(0.1)  # Short sleep to avoid CPU spinning
            # Check if we're still connected
            if not self.isConnected():
                logging.error("Lost connection to TWS/Gateway while waiting for news providers")
                return
                
        # If we timed out but we can see providers in the API log
        if not self.news_providers_event.is_set():
            logging.warning(f"Timeout waiting for news providers after {REQUEST_TIMEOUT} seconds")
            
            # Check if we have any providers already (maybe they came in but didn't trigger event)
            if not self.news_providers:
                # Hard code the standard news providers we saw in the API log
                logging.info("Setting default news providers from known IB providers")
                self.news_providers = [
                    ("BRFG", "Briefing.com General Market Columns"),
                    ("BRFUPDN", "Briefing.com Analyst Actions"),
                    ("DJ-N", "Dow Jones News Service"),
                    ("DJ-RT", "Dow Jones Trader News"),
                    ("DJ-RTA", "Dow Jones Real-Time News Asia Pacific"),
                    ("DJ-RTE", "Dow Jones Real-Time News Europe"),
                    ("DJ-RTG", "Dow Jones Real-Time News Global"),
                    ("DJNL", "Dow Jones Newsletters")
                ]
                logging.info(f"Added {len(self.news_providers)} default news providers")

    def test_contract_details(self):
        """Get contract details for all test symbols"""
        logging.info("Requesting contract details for all symbols...")

        for symbol in TEST_SYMBOLS:
            req_id = self.get_next_req_id()
            self.reqId_to_symbol[req_id] = symbol
            self.contract_details_events[symbol] = threading.Event()

            contract = self.create_stock_contract(symbol)
            logging.info(f"Requesting contract details for {symbol} (Req ID: {req_id})")
            self.reqContractDetails(req_id, contract)

        # Wait for all contract details with timeout
        for symbol in TEST_SYMBOLS:
            if not self.contract_details_events[symbol].wait(REQUEST_TIMEOUT):
                logging.warning(f"Timeout waiting for contract details for {symbol}")

    def test_historical_news(self):
        """Test reqHistoricalNews for all symbols"""
        logging.info("Requesting historical news for all symbols (last 48 hours only)...")

        # Get provider codes from the news providers response
        provider_codes = "+".join([code for code, _ in self.news_providers[:3]])
        if not provider_codes:
            provider_codes = "BRFG+BRFUPDN+DJNL"  # Default if no providers found

        # Format date for 48 hours ago in Eastern Time
        eastern = pytz.timezone('US/Eastern')
        now_eastern = datetime.datetime.now(eastern)
        start_date = (now_eastern - datetime.timedelta(hours=48)).strftime("%Y%m%d %H:%M:%S")

        for symbol in TEST_SYMBOLS:
            if symbol not in self.contract_details:
                logging.warning(f"No contract details for {symbol}, skipping historical news request")
                continue

            contract_id = self.contract_details[symbol].contract.conId
            req_id = self.get_next_req_id()
            self.reqId_to_symbol[req_id] = symbol
            self.historical_news_events[symbol] = threading.Event()

            logging.info(f"Requesting historical news for {symbol} (ConId: {contract_id}, Req ID: {req_id})")
            self.reqHistoricalNews(
                reqId=req_id,
                conId=contract_id,
                providerCodes=provider_codes,
                startDateTime=start_date,
                endDateTime="",
                totalResults=5,
                historicalNewsOptions=[]
            )

        # Wait for all historical news with timeout
        for symbol in TEST_SYMBOLS:
            if symbol in self.historical_news_events:
                if not self.historical_news_events[symbol].wait(REQUEST_TIMEOUT):
                    logging.warning(f"Timeout waiting for historical news for {symbol}")

    def test_news_articles(self):
        """Test reqNewsArticle for one headline per symbol"""
        logging.info("Requesting full news articles...")

        for symbol in TEST_SYMBOLS:
            if symbol not in self.historical_news or not self.historical_news[symbol]:
                logging.warning(f"No historical news for {symbol}, skipping article request")
                continue

            # Get the first news item for this symbol
            news_item = self.historical_news[symbol][0]
            provider_code = news_item['providerCode']
            article_id = news_item['articleId']

            req_id = self.get_next_req_id()
            self.reqId_to_symbol[req_id] = symbol
            self.news_article_events[symbol] = threading.Event()

            logging.info(f"Requesting news article for {symbol} (Provider: {provider_code}, Article ID: {article_id})")
            self.reqNewsArticle(req_id, provider_code, article_id, [])

        # Wait for all news articles with timeout
        for symbol in self.news_article_events:
            if not self.news_article_events[symbol].wait(REQUEST_TIMEOUT):
                logging.warning(f"Timeout waiting for news article for {symbol}")

    def test_realtime_news(self):
        """Test real-time news for a subset of symbols"""
        logging.info("Subscribing to real-time news for selected symbols...")

        # Get first provider code from the news providers response
        # IMPORTANT: Using DJ-N as in the IBKR example if no providers are found
        provider_code = self.news_providers[0][0] if self.news_providers else "DJ-N"

        for symbol in REALTIME_SYMBOLS:
            req_id = self.get_next_req_id()
            self.reqId_to_symbol[req_id] = symbol

            contract = self.create_stock_contract(symbol)
            logging.info(f"Subscribing to real-time news for {symbol} (Req ID: {req_id}, Provider: {provider_code})")

            # Request market data with news tick (matching IBKR example format)
            logging.debug(f"Using genericTickList='mdoff,292:{provider_code}'")
            self.reqMktData(
                reqId=req_id,
                contract=contract,
                genericTickList=f"mdoff,292:{provider_code}",
                snapshot=False,
                regulatorySnapshot=False,
                mktDataOptions=[]
            )
            
            # Track this request for cleanup
            self.active_market_data_reqs.add(req_id)

    def test_broadtape_news(self):
        """Test broadtape news subscription"""
        logging.info("Subscribing to broadtape news...")

        # For this test, explicitly use DJ-N (Dow Jones News) as in the example
        provider_code = "DJ-N"
        logging.info(f"Using Dow Jones News (DJ-N) provider for broadtape news")

        req_id = self.get_next_req_id()
        self.reqId_to_symbol[req_id] = "BROADTAPE"  # Mark this as a broadtape request
        
        # Create the broadtape contract
        contract = self.create_news_contract(provider_code)
        logging.info(f"Contract for broadtape news: {contract.symbol}, {contract.secType}, {contract.exchange}")
        
        # Log the exact request we're making
        logging.info(f"Subscribing to broadtape news from {provider_code} (Req ID: {req_id})")
        self.reqMktData(
            reqId=req_id,
            contract=contract,
            genericTickList="mdoff,292",  # Generic tick for news
            snapshot=False,
            regulatorySnapshot=False,
            mktDataOptions=[]
        )
        
        # Track this request for cleanup
        self.active_market_data_reqs.add(req_id)

    def cleanup(self):
        """Clean up all active subscriptions and disconnect"""
        logging.info("Cleaning up and disconnecting...")

        try:
            # Cancel all active market data requests
            for req_id in self.active_market_data_reqs:
                try:
                    logging.info(f"Cancelling market data request {req_id}")
                    self.cancelMktData(req_id)
                except Exception as e:
                    logging.error(f"Error cancelling market data request {req_id}: {e}")

            # Disconnect from IB API
            if self.isConnected():
                try:
                    self.disconnect()
                    logging.info("Disconnected from IB API")
                except Exception as e:
                    logging.error(f"Error disconnecting from IB API: {e}")
            else:
                logging.info("Already disconnected from IB API")

        except Exception as e:
            logging.error(f"Error during cleanup: {e}")

        # Print summary of errors
        if self.errors:
            logging.warning(f"Total errors during execution: {len(self.errors)}")
            for i, (req_id, code, msg) in enumerate(self.errors[:5]):  # Show first 5 errors
                logging.warning(f"Error {i+1}: Request {req_id}, Code {code}: {msg}")
            if len(self.errors) > 5:
                logging.warning(f"... and {len(self.errors) - 5} more errors")

        # Generate a summary report
        self.generate_summary_report()

    def generate_summary_report(self):
        """Generate a summary report of the news data test results"""
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"news_test_summary_{timestamp}.log"
        report_path = os.path.join(logs_dir, report_filename)
        
        logging.info(f"Generating summary report to {report_path}...")
        
        with open(report_path, 'w') as f:
            f.write("=== INTERACTIVE BROKERS NEWS API TEST SUMMARY ===\n")
            f.write(f"Test run completed at: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("\n=== CONNECTION INFO ===\n")
            f.write(f"Host: {TWS_HOST}:{TWS_PORT}\n")
            f.write(f"Client ID: {CLIENT_ID}\n")
            
            # News Providers section
            f.write("\n=== NEWS PROVIDERS ===\n")
            if self.news_providers:
                f.write(f"Successfully retrieved {len(self.news_providers)} news providers:\n")
                for code, name in self.news_providers:
                    f.write(f"  • {code}: {name}\n")
            else:
                f.write("FAILED to retrieve news providers\n")
            
            # Contract details section
            f.write("\n=== CONTRACT DETAILS ===\n")
            successful_contracts = [s for s in TEST_SYMBOLS if s in self.contract_details]
            failed_contracts = [s for s in TEST_SYMBOLS if s not in self.contract_details]
            
            f.write(f"Successfully retrieved contract details for {len(successful_contracts)} of {len(TEST_SYMBOLS)} symbols:\n")
            if successful_contracts:
                for symbol in successful_contracts:
                    conId = self.contract_details[symbol].contract.conId
                    f.write(f"  • {symbol} (ConId: {conId})\n")
            if failed_contracts:
                f.write("\nFailed to retrieve contract details for:\n")
                for symbol in failed_contracts:
                    f.write(f"  • {symbol}\n")
            
            # Historical News section
            f.write("\n=== HISTORICAL NEWS ===\n")
            symbols_with_historical_news = [s for s in TEST_SYMBOLS if s in self.historical_news and self.historical_news[s]]
            symbols_without_historical_news = [s for s in TEST_SYMBOLS if s not in self.historical_news or not self.historical_news[s]]
            
            f.write(f"Retrieved historical news for {len(symbols_with_historical_news)} of {len(TEST_SYMBOLS)} symbols:\n")
            for symbol in symbols_with_historical_news:
                f.write(f"  • {symbol}: {len(self.historical_news[symbol])} news items\n")
                if len(self.historical_news[symbol]) > 0:
                    # Show first headline as an example
                    headline = self.historical_news[symbol][0]['headline']
                    f.write(f"    Example: \"{headline[:80]}{'...' if len(headline) > 80 else ''}\"\n")
            
            if symbols_without_historical_news:
                f.write("\nNo historical news for:\n")
                for symbol in symbols_without_historical_news:
                    f.write(f"  • {symbol}\n")
            
            # News Articles section
            f.write("\n=== FULL NEWS ARTICLES ===\n")
            symbols_with_articles = [s for s in TEST_SYMBOLS if s in self.news_articles]
            symbols_without_articles = [s for s in symbols_with_historical_news if s not in self.news_articles]
            
            f.write(f"Retrieved full news articles for {len(symbols_with_articles)} symbols:\n")
            for symbol in symbols_with_articles:
                article_type = self.news_articles[symbol]['articleType']
                article_text_preview = self.news_articles[symbol]['articleText'][:100].replace('\n', ' ')
                f.write(f"  • {symbol} (Type: {article_type}): \"{article_text_preview}...\"\n")
            
            if symbols_without_articles and symbols_with_historical_news:
                f.write("\nFailed to retrieve articles for symbols that had headlines:\n")
                for symbol in symbols_without_articles:
                    f.write(f"  • {symbol}\n")
            
            # Real-time News section
            f.write("\n=== REAL-TIME NEWS ===\n")
            symbols_with_realtime_news = [s for s in self.realtime_news if s != "BROADTAPE"]
            
            f.write("Real-time news subscriptions:\n")
            for symbol in REALTIME_SYMBOLS:
                status = "Received data" if symbol in symbols_with_realtime_news else "No data received"
                count = len(self.realtime_news.get(symbol, [])) if symbol in self.realtime_news else 0
                f.write(f"  • {symbol}: {status} ({count} news items)\n")
            
            # Broadtape News section
            f.write("\n=== BROADTAPE NEWS ===\n")
            broadtape_count = len(self.realtime_news.get("BROADTAPE", []))
            if broadtape_count > 0:
                f.write(f"Received {broadtape_count} broadtape news items\n")
                # Show an example
                if "BROADTAPE" in self.realtime_news and self.realtime_news["BROADTAPE"]:
                    headline = self.realtime_news["BROADTAPE"][0]['headline']
                    f.write(f"Example: \"{headline[:80]}{'...' if len(headline) > 80 else ''}\"\n")
            else:
                f.write("No broadtape news received\n")
            
            # Error Summary section
            f.write("\n=== ERROR SUMMARY ===\n")
            if self.errors:
                f.write(f"Total errors during execution: {len(self.errors)}\n")
                f.write("Top errors:\n")
                for i, (req_id, code, msg) in enumerate(self.errors[:5]):
                    symbol = self.reqId_to_symbol.get(req_id, "Unknown")
                    f.write(f"  • Error {i+1}: [{symbol}] Code {code}: {msg}\n")
                if len(self.errors) > 5:
                    f.write(f"  • ... and {len(self.errors) - 5} more errors\n")
            else:
                f.write("No errors reported during execution\n")
            
            # Overall Summary
            f.write("\n=== OVERALL SUMMARY ===\n")
            total_news_items = sum(len(items) for items in self.historical_news.values()) + \
                              sum(len(items) for items in self.realtime_news.values())
            
            f.write(f"Total news items collected: {total_news_items}\n")
            f.write(f"  • Historical news items: {sum(len(items) for items in self.historical_news.values())}\n")
            f.write(f"  • Real-time news items: {sum(len(items) for items in self.realtime_news.values())}\n")
            f.write(f"  • Full articles retrieved: {len(self.news_articles)}\n")
            
            success_rate = len(successful_contracts) / len(TEST_SYMBOLS) * 100
            f.write(f"\nOverall success rate: {success_rate:.1f}%\n")
            
            # Recommendations
            f.write("\n=== RECOMMENDATIONS ===\n")
            if not self.news_providers:
                f.write("• Check your IB API permissions for news data access\n")
            
            if len(failed_contracts) > 0:
                f.write("• Verify that all test symbols are valid and available through your IB account\n")
                
            if total_news_items == 0:
                f.write("• Check your market data subscriptions in TWS/IB Gateway\n")
                f.write("• Try different news providers (default is DJ-N)\n")
                f.write("• Consider running the test during market hours for more real-time news activity\n")
            
            f.write("\nDetailed logs available in:\n")
            f.write(f"• {main_log_path} - Full debug logs\n")
            f.write(f"• {news_log_path} - Clean news data only\n")
            
        logging.info(f"Summary report generated: {report_path}")
        
        # Print a brief summary to console
        print("\n=== TEST RESULTS SUMMARY ===")
        print(f"Total news items collected: {total_news_items}")
        print(f"Success rate: {success_rate:.1f}%")
        print(f"Full report written to: {report_path}")
        print(f"News data written to: {news_log_path}")
        print("============================\n")


def main():
    """Main function to run the news data test"""
    logging.info("=== IB API NEWS DATA TEST ===")
    logging.info(f"Connecting to TWS/Gateway at {TWS_HOST}:{TWS_PORT}")

    app = None
    api_thread = None

    try:
        # Create the app
        app = NewsDataTester()
        
        # First connect to TWS/Gateway
        logging.info(f"Connecting to TWS/Gateway at {TWS_HOST}:{TWS_PORT} with client ID {CLIENT_ID}")
        
        # Connect (our overridden connect method will set the socket timeout)
        app.connect(TWS_HOST, TWS_PORT, CLIENT_ID)
        
        # After connect is successful, start the client thread
        api_thread = threading.Thread(target=app.run, daemon=True)
        api_thread.start()

        # Give the thread a moment to initialize
        time.sleep(0.5)

        # Wait for connection with timeout
        connection_start = time.time()
        while not app.connected_event.is_set() and time.time() - connection_start < CONNECT_TIMEOUT:
            time.sleep(0.5)
            logging.debug("Waiting for connection...")
            # Print connection status
            logging.debug(f"Connection status: {app.isConnected()}")

        if not app.connected_event.is_set():
            logging.error(f"Failed to connect to IB API within {CONNECT_TIMEOUT} seconds")
            return 1

        # Wait a bit more after connection to ensure everything is ready
        time.sleep(1)
        logging.info("Successfully connected to IB API")

        # If we didn't get a nextValidId, set a default starting ID
        if app.next_req_id == 1:  # Still at the initial value
            logging.warning("Did not receive nextValidId from TWS/Gateway, using default starting ID of 1000")
            app.next_req_id = 1000

        # Run all tests
        app.run_tests()

    except KeyboardInterrupt:
        logging.info("Test interrupted by user")
    except ConnectionRefusedError:
        logging.error(f"Connection refused. Make sure TWS/Gateway is running on {TWS_HOST}:{TWS_PORT}")
        return 1
    except Exception as e:
        logging.error(f"Error during test: {e}")
        import traceback
        logging.error(f"Traceback: {traceback.format_exc()}")
        return 1
    finally:
        # Ensure cleanup happens
        if app and app.isConnected():
            try:
                app.cleanup()
            except Exception as e:
                logging.error(f"Error during cleanup: {e}")

        # Wait for API thread to finish
        if api_thread and api_thread.is_alive():
            api_thread.join(timeout=5)
            if api_thread.is_alive():
                logging.warning("API thread did not terminate within timeout")

    logging.info("News data test completed successfully")
    return 0


if __name__ == "__main__":
    import sys
    sys.exit(main())
