#!/bin/bash
# run_scanner.sh
# Script to run the ORB scanner in a screen session with Python environment and logging

# Set variables
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_DIR="$( cd "$SCRIPT_DIR/.." && pwd )" # Bin is now directly under project root
LOG_DIR="$PROJECT_DIR/logs/live"
LOG_ARCHIVE_DIR="$LOG_DIR/old"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
SCREEN_NAME="scan_orb_$TIMESTAMP"
PYTHON_SCRIPT="$PROJECT_DIR/scanner_main.py" # Using the new refactored main script

# Add PYTHONPATH setting to ensure modules can be found
export PYTHONPATH="$PROJECT_DIR:$PYTHONPATH"

# Create log directories if they don't exist
mkdir -p "$LOG_DIR"
mkdir -p "$LOG_ARCHIVE_DIR"

# Function to rotate log files
rotate_scan_logs() {
    echo "Rotating ORB scanner log files..."

    # Find all scan_orb* files in the log directory
    scan_logs=$(find "$LOG_DIR" -maxdepth 1 -name "scan_orb*" -type f)

    # Check if any files were found
    if [ -z "$scan_logs" ]; then
        echo "No scan_orb* log files found to rotate."
    else
        # Count the number of files to move
        file_count=$(echo "$scan_logs" | wc -l)
        echo "Found $file_count scan_orb* log files to rotate."

        # Move files to the archive directory
        for log_file in $scan_logs; do
            filename=$(basename "$log_file")
            echo "Moving $filename to archive directory..."
            mv "$log_file" "$LOG_ARCHIVE_DIR/"
        done

        echo "scan_orb* log rotation complete. Files moved to $LOG_ARCHIVE_DIR"
    fi

    # Find all data_request_trace* files in the log directory
    trace_logs=$(find "$LOG_DIR" -maxdepth 1 -name "data_request_trace_*" -type f)

    # Check if any trace files were found
    if [ -z "$trace_logs" ]; then
        echo "No data_request_trace* log files found to rotate."
    else
        # Count the number of trace files to move
        trace_file_count=$(echo "$trace_logs" | wc -l)
        echo "Found $trace_file_count data_request_trace* log files to rotate."

        # Move trace files to the archive directory
        for trace_file in $trace_logs; do
            filename=$(basename "$trace_file")
            echo "Moving $filename to archive directory..."
            mv "$trace_file" "$LOG_ARCHIVE_DIR/"
        done

        echo "data_request_trace* log rotation complete. Files moved to $LOG_ARCHIVE_DIR"
    fi

    # Find all scan_7min*selection.log files in the log directory
    selection_logs=$(find "$LOG_DIR" -maxdepth 1 -name "scan_7min*_selection.log" -type f)

    # Check if any selection log files were found
    if [ -z "$selection_logs" ]; then
        echo "No scan_7min*_selection.log files found to rotate."
    else
        # Count the number of files to move
        file_count=$(echo "$selection_logs" | wc -l)
        echo "Found $file_count scan_7min*_selection.log files to rotate."

        # Move files to the archive directory
        for log_file in $selection_logs; do
            filename=$(basename "$log_file")
            echo "Moving $filename to archive directory..."
            mv "$log_file" "$LOG_ARCHIVE_DIR/"
        done

        echo "scan_7min*_selection.log rotation complete. Files moved to $LOG_ARCHIVE_DIR"
    fi

    # Find all scan_orb*selection.log files in the log directory
    orb_selection_logs=$(find "$LOG_DIR" -maxdepth 1 -name "scan_orb*_selection.log" -type f)

    # Check if any scan_orb selection log files were found
    if [ -z "$orb_selection_logs" ]; then
        echo "No scan_orb*_selection.log files found to rotate."
    else
        # Count the number of files to move
        file_count=$(echo "$orb_selection_logs" | wc -l)
        echo "Found $file_count scan_orb*_selection.log files to rotate."

        # Move files to the archive directory
        for log_file in $orb_selection_logs; do
            filename=$(basename "$log_file")
            echo "Moving $filename to archive directory..."
            mv "$log_file" "$LOG_ARCHIVE_DIR/"
        done

        echo "scan_orb*_selection.log rotation complete. Files moved to $LOG_ARCHIVE_DIR"
    fi

    echo "All log rotation operations completed."
}

# Rotate log files at startup
rotate_scan_logs

# Check if the Python script exists
if [ ! -f "$PYTHON_SCRIPT" ]; then
    echo "ERROR: Scanner script not found at $PYTHON_SCRIPT"
    exit 1
fi

# Check if screen is installed
if ! command -v screen &> /dev/null; then
    echo "ERROR: 'screen' is not installed. Please install it first."
    exit 1
fi

# Check if a virtual environment exists
if [ -d "$PROJECT_DIR/venv" ]; then
    VENV_PATH="$PROJECT_DIR/venv"
    echo "Found virtual environment at $VENV_PATH"
else
    echo "No virtual environment found at $PROJECT_DIR/venv"
    echo "Creating a new virtual environment..."
    python3 -m venv "$PROJECT_DIR/venv"
    VENV_PATH="$PROJECT_DIR/venv"

    # Install required packages
    source "$VENV_PATH/bin/activate"
    pip install --upgrade pip
    if [ -f "$PROJECT_DIR/requirements.txt" ]; then
        pip install -r "$PROJECT_DIR/requirements.txt"
    else
        echo "WARNING: requirements.txt not found. Installing basic packages..."
        pip install pandas numpy matplotlib requests tabulate pytz # Add any specific packages needed for test_7min_scan.py here if not in requirements
    fi
    deactivate
fi

# Kill any existing ORB scanner processes
echo "Checking for existing ORB scanner processes..."

# Find and kill any existing screen sessions with 'scan_orb' in the name
EXISTING_SESSIONS=$(screen -ls | grep 'scan_orb' | awk '{print $1}' | cut -d. -f1)
if [ -n "$EXISTING_SESSIONS" ]; then
    echo "Found existing ORB scanner screen sessions. Killing them..."
    for SESSION_ID in $EXISTING_SESSIONS; do
        echo "Killing screen session: $SESSION_ID"
        screen -S "$SESSION_ID" -X quit
    done
    echo "All existing ORB scanner screen sessions have been terminated."
else
    echo "No existing ORB scanner screen sessions found."
fi

# Find and kill any existing Python processes running scanner.py
EXISTING_PROCESSES=$(pgrep -f "python.*scanner\.py")
if [ -n "$EXISTING_PROCESSES" ]; then
    echo "Found existing ORB scanner Python processes. Killing them..."
    for PID in $EXISTING_PROCESSES; do
        echo "Killing process with PID: $PID"
        kill -9 "$PID" 2>/dev/null
    done
    echo "All existing ORB scanner Python processes have been terminated."
else
    echo "No existing ORB scanner Python processes found."
fi

# Wait a moment to ensure all processes are properly terminated
sleep 2

# Note: We're letting the Python script handle timezone and market hours logic
# Auto-enable historical data when after-open is used
if [ "$AFTER_OPEN" = true ] && [ "$USE_HISTORICAL_DATA" = false ]; then
    echo "Auto-enabling historical data mode because --after-open flag is used"
    USE_HISTORICAL_DATA=true
fi

# Start the screen session with logging
echo "Starting ORB scanner in screen session: $SCREEN_NAME"
echo "Using Python script's logic and client ID handling"
LOG_FILE_BASE="$LOG_DIR/scan_orb_$TIMESTAMP" # Changed log base name
echo "Log will be saved to: ${LOG_FILE_BASE}.log"

# Check if typescript is installed
if ! command -v script &> /dev/null; then
    echo "WARNING: 'script' command not found. Typescript logging to /tmp/scanout_orb will not be available."
    TYPESCRIPT_AVAILABLE=false
else
    TYPESCRIPT_AVAILABLE=true
    TYPESCRIPT_LOG="/tmp/scanout_orb" # Changed typescript log name
    # Remove existing typescript logs if they exist
    echo "Checking for existing typescript logs..."
    if [ -f "$TYPESCRIPT_LOG" ]; then
        echo "Removing existing typescript log at $TYPESCRIPT_LOG"
        rm -f "$TYPESCRIPT_LOG"
    fi

    # Also check for any other scanner-related logs in /tmp (optional, adjust pattern if needed)
    EXISTING_LOGS=$(find /tmp -name "scan*" -type f 2>/dev/null)
    if [ -n "$EXISTING_LOGS" ]; then
        echo "Found additional scanner-related logs in /tmp. Cleaning up..."
        for LOG_FILE in $EXISTING_LOGS; do
            # Be careful not to delete unrelated logs if pattern is too broad
            if [[ "$LOG_FILE" == *scanout* ]]; then
                 echo "Removing log file: $LOG_FILE"
                 rm -f "$LOG_FILE"
            fi
        done
        echo "Relevant scanner-related logs in /tmp have been cleaned up."
    else
        echo "No additional relevant scanner-related logs found in /tmp."
    fi
fi

# Parse command line arguments
CONTINUOUS_SCAN=true  # Default to continuous mode
SCAN_INTERVAL=30

# Let the Python script handle duration calculation with proper timezone awareness
# The Python script will default to running until market close (4:00 PM ET)
# when no duration is specified

USE_HISTORICAL_DATA=false
MARKET_OPEN_DATE=""
MARKET_OPEN_TIME="09:30"
HISTORICAL_DATA_TIMEOUT=30
SYNC_WITH_MARKET_OPEN=true
AFTER_OPEN=false
USE_TREND_ALIGNMENT=true
ENABLE_FAIL_SAFE=true  # Enable fail-safe mechanism by default
ENABLE_DATA_TRACING=true   # Enable data tracing by default to help diagnose missing data issues
GENERATE_REPORT=true  # Enable report generation by default
ENABLE_TEST_CANDIDATE=false  # Disable test candidate mode by default
TEST_CANDIDATE_SYMBOL="AAPL"  # Default test candidate symbol
SCAN_DURATION=""  # Empty by default, will let Python script calculate until market close

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --continuous)
            CONTINUOUS_SCAN=true
            shift
            ;;
        --duration)
            SCAN_DURATION="$2"
            shift 2
            ;;
        --interval)
            SCAN_INTERVAL="$2"
            shift 2
            ;;
        --use-historical-data)
            USE_HISTORICAL_DATA=true
            shift
            ;;
        --market-open-date)
            MARKET_OPEN_DATE="$2"
            shift 2
            ;;
        --market-open-time)
            MARKET_OPEN_TIME="$2"
            shift 2
            ;;
        --historical-data-timeout)
            HISTORICAL_DATA_TIMEOUT="$2"
            shift 2
            ;;
        --historical-continuous)
            # Shortcut for continuous scanning with historical data
            CONTINUOUS_SCAN=true
            USE_HISTORICAL_DATA=true
            shift
            ;;
        --no-sync)
            # Disable synchronization with market open
            SYNC_WITH_MARKET_OPEN=false
            shift
            ;;
        --after-open)
            # Simulate as if market just opened
            AFTER_OPEN=true
            # Auto-enable historical data when after-open is used
            USE_HISTORICAL_DATA=true
            shift
            ;;
        --no-trend-alignment)
            # Disable daily trend alignment
            USE_TREND_ALIGNMENT=false
            shift
            ;;
        --use-trend-alignment)
            # Enable daily trend alignment (default)
            USE_TREND_ALIGNMENT=true
            shift
            ;;
        --enable-fail-safe)
            # Enable fail-safe mechanism (default)
            ENABLE_FAIL_SAFE=true
            shift
            ;;
        --disable-fail-safe)
            # Disable fail-safe mechanism
            ENABLE_FAIL_SAFE=false
            shift
            ;;
        --enable-data-tracing)
            # Enable detailed data request tracing
            ENABLE_DATA_TRACING=true
            shift
            ;;
        --disable-data-tracing)
            # Disable detailed data request tracing
            ENABLE_DATA_TRACING=false
            shift
            ;;
        --generate-report)
            # Enable report generation
            GENERATE_REPORT=true
            shift
            ;;
        --no-generate-report)
            # Disable report generation
            GENERATE_REPORT=false
            shift
            ;;
        --enable-test-candidate)
            # Enable test candidate mode
            ENABLE_TEST_CANDIDATE=true
            shift
            ;;
        --test-candidate-symbol)
            # Set test candidate symbol
            TEST_CANDIDATE_SYMBOL="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --continuous                 Enable continuous scanning mode (default: enabled)"
            echo "  --duration MINUTES           Duration of continuous scanning in minutes (default: until market close, calculated by Python script)"
            echo "  --interval SECONDS           Interval between scans in seconds (default: 30)"
            echo "  --use-historical-data        Use historical data for opening ranges when testing after market open"
            echo "  --historical-continuous      Shortcut for continuous scanning with historical data"
            echo "  --market-open-date DATE      Market open date for historical data (format: YYYY-MM-DD, default: today)"
            echo "  --market-open-time TIME      Market open time for historical data (format: HH:MM, default: 09:30)"
            echo "  --historical-data-timeout SEC Timeout for historical data requests in seconds (default: 30)"
            echo "  --no-sync                    Disable synchronization with market open (default: enabled)"
            echo "  --after-open                 Simulate as if market just opened, regardless of actual time (auto-enables historical data)"
            echo "  --use-trend-alignment        Use daily trend alignment with VWAP/SMA200 (default: enabled)"
            echo "  --no-trend-alignment         Disable daily trend alignment"
            echo "  --enable-fail-safe           Enable fail-safe mechanism that exits when no valid data (default: enabled)"
            echo "  --disable-fail-safe          Disable fail-safe mechanism and continue even with no valid data"
            echo "  --enable-data-tracing        Enable detailed data request tracing for debugging missing data issues (default: enabled)"
            echo "  --disable-data-tracing       Disable detailed data request tracing"
            echo "  --generate-report            Enable detailed report generation after completion (default: enabled)" 
            echo "  --no-generate-report         Disable report generation"
            echo "  --enable-test-candidate      Enable test candidate mode for debugging (default: disabled)"
            echo "  --test-candidate-symbol SYM  Symbol to use for test candidate (default: AAPL)"
            echo "  --help                       Display this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Build the command with appropriate options
CMD_ARGS="--output-dir $LOG_DIR --log-base $LOG_FILE_BASE"

# Add continuous scan arguments if enabled
if [ "$CONTINUOUS_SCAN" = true ]; then
    CMD_ARGS="$CMD_ARGS --continuous-scan --scan-interval $SCAN_INTERVAL"

    # Only pass duration if explicitly provided by user
    if [ -n "$SCAN_DURATION" ]; then
        CMD_ARGS="$CMD_ARGS --scan-duration $SCAN_DURATION"
        echo "Running in continuous scanning mode for $SCAN_DURATION minutes with $SCAN_INTERVAL second intervals"
    else
        echo "Running in continuous scanning mode until market close with $SCAN_INTERVAL second intervals"
    fi
else
    echo "Running in single scan mode"
fi

# Add market open synchronization option
if [ "$SYNC_WITH_MARKET_OPEN" = true ]; then
    CMD_ARGS="$CMD_ARGS --sync-with-market-open"
    echo "Synchronizing with market open at 9:30 AM"
else
    CMD_ARGS="$CMD_ARGS --no-sync-with-market-open"
    echo "Not synchronizing with market open (using current time as start time)"
fi

# Add historical data arguments if enabled
if [ "$USE_HISTORICAL_DATA" = true ]; then
    CMD_ARGS="$CMD_ARGS --use-historical-data --historical-data-timeout $HISTORICAL_DATA_TIMEOUT"

    # Add market open date if provided
    if [ -n "$MARKET_OPEN_DATE" ]; then
        CMD_ARGS="$CMD_ARGS --market-open-date $MARKET_OPEN_DATE"
    fi

    # Add market open time if provided
    if [ -n "$MARKET_OPEN_TIME" ]; then
        CMD_ARGS="$CMD_ARGS --market-open-time $MARKET_OPEN_TIME"
    fi

    echo "Using historical data for opening ranges"
    if [ -n "$MARKET_OPEN_DATE" ]; then
        echo "  Market open date: $MARKET_OPEN_DATE"
    else
        echo "  Market open date: Today"
    fi
    echo "  Market open time: $MARKET_OPEN_TIME"
    echo "  Historical data timeout: $HISTORICAL_DATA_TIMEOUT seconds"
else
    echo "Using real-time data for opening ranges"
fi

# Add after-open flag if enabled
if [ "$AFTER_OPEN" = true ]; then
    CMD_ARGS="$CMD_ARGS --after-open"
    echo "Simulating as if market just opened, regardless of actual time"
    echo "Note: Historical data mode automatically enabled with --after-open"
fi

# Add trend alignment options
if [ "$USE_TREND_ALIGNMENT" = true ]; then
    CMD_ARGS="$CMD_ARGS --use-trend-alignment"
    echo "Using daily trend alignment with VWAP/SMA200 to reject candidates breaking against the trend"
else
    CMD_ARGS="$CMD_ARGS --no-trend-alignment"
    echo "Daily trend alignment is disabled"
fi

# Add fail-safe options
if [ "$ENABLE_FAIL_SAFE" = true ]; then
    CMD_ARGS="$CMD_ARGS --enable-fail-safe"
    echo "Fail-safe mechanism ENABLED: Test will exit if no valid data in critical phases"
else
    CMD_ARGS="$CMD_ARGS --disable-fail-safe"
    echo "Fail-safe mechanism DISABLED: Test will continue even with no valid data"
fi

# Add test candidate options
if [ "$ENABLE_TEST_CANDIDATE" = true ]; then
    CMD_ARGS="$CMD_ARGS --enable-test-candidate --test-candidate-symbol $TEST_CANDIDATE_SYMBOL"
    echo "⚠️ TEST MODE ACTIVE: Will use $TEST_CANDIDATE_SYMBOL as test candidate if no candidates are found"
    echo "⚠️ WARNING: TEST MODE IS FOR DEBUGGING ONLY. DO NOT USE FOR REAL TRADING!"
fi

# Add report generation option
if [ "$GENERATE_REPORT" = true ]; then
    CMD_ARGS="$CMD_ARGS --generate-report"
    echo "Report generation ENABLED: Will generate detailed report after scan completes"
else
    echo "Report generation DISABLED: No report will be generated"
fi

# Add data tracing options
if [ "$ENABLE_DATA_TRACING" = true ]; then
    CMD_ARGS="$CMD_ARGS --enable-data-tracing"
    echo "Data request tracing ENABLED: Detailed request/response data will be logged"
    TRACE_LOG_PATH="$LOG_DIR/data_request_trace_$(date +"%Y%m%d_%H%M%S").log"
    echo "Data trace logs will be saved to: $TRACE_LOG_PATH"
    echo ""
    echo "=== ABOUT DATA REQUEST TRACING ==="
    echo "Data request tracing logs detailed information about each price data request:"
    echo "  - Request parameters (symbol, request type, request ID)"
    echo "  - Request timing (start time, duration)"
    echo "  - Response details (success/failure, response data)"
    echo "  - Error information when requests fail"
    echo ""
    echo "This helps diagnose missing price data issues by showing:"
    echo "  - Which specific data requests are failing"
    echo "  - Success rates for different types of requests"
    echo "  - Timing statistics for data operations"
    echo "  - Detailed error messages from the API"
    echo ""
    echo "At the end of the run, a statistical report will show overall success rates"
    echo "and performance metrics to help identify the root cause of data issues."
    echo "==============================="
    echo ""
else
    CMD_ARGS="$CMD_ARGS --disable-data-tracing"
    echo "Data request tracing DISABLED: No detailed request/response data will be logged"
fi

# Create the screen session with the ORB scanner
if [ "$TYPESCRIPT_AVAILABLE" = true ]; then
    # Use typescript to log all terminal output
    screen -L -Logfile "${LOG_FILE_BASE}_screen.log" -dmS "$SCREEN_NAME" bash -c "cd $PROJECT_DIR && source $VENV_PATH/bin/activate && script -f $TYPESCRIPT_LOG -c 'python3 $PYTHON_SCRIPT $CMD_ARGS'; exec bash"
    echo "Typescript log will be saved to: $TYPESCRIPT_LOG"
else
    # Fallback without typescript
    screen -L -Logfile "${LOG_FILE_BASE}_screen.log" -dmS "$SCREEN_NAME" bash -c "cd $PROJECT_DIR && source $VENV_PATH/bin/activate && python3 $PYTHON_SCRIPT $CMD_ARGS; exec bash"
fi

# Check if screen session started successfully
if screen -list | grep -q "$SCREEN_NAME"; then
    echo "ORB scanner started successfully in screen session: $SCREEN_NAME"
    echo ""
    echo "=== HOW TO MONITOR YOUR ORB SCANNER ==="
    echo "  - View live activity:  screen -r $SCREEN_NAME"
    echo "  - Detach from screen:  Press Ctrl+A, then D"
    echo "  - Check main log:      tail -f ${LOG_FILE_BASE}.log"
    echo "  - Check screen log:    tail -f ${LOG_FILE_BASE}_screen.log"
    if [ "$TYPESCRIPT_AVAILABLE" = true ]; then
        echo "  - Check typescript log: tail -f $TYPESCRIPT_LOG"
    fi
    if [ "$ENABLE_DATA_TRACING" = true ]; then
        echo "  - Check data trace log: ls -t $LOG_DIR/data_request_trace_*.log | head -1 | xargs tail -f"
        echo "  - View trace stats:     grep 'DATA REQUEST STATISTICS' -A 20 \$(ls -t $LOG_DIR/data_request_trace_*.log | head -1)"
    fi
    echo "  - Stop the test:       screen -S $SCREEN_NAME -X quit"
    echo ""
else
    echo "ERROR: Failed to start screen session."
    echo "Check for errors in the output above."
    exit 1
fi

exit 0
