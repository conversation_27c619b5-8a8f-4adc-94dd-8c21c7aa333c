#!/bin/bash
# quick_run.sh - Wrapper script for running the ORB scanner with different options

# Set script to exit on error
set -e

# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
SCANNER_SCRIPT="$SCRIPT_DIR/run_scanner.sh"

# Check if the scanner script exists
if [ ! -f "$SCANNER_SCRIPT" ]; then
    echo "ERROR: Scanner script not found at $SCANNER_SCRIPT"
    exit 1
fi

# Make sure the scanner script is executable
chmod +x "$SCANNER_SCRIPT"

# Function to get the last market day (excluding weekends)
get_last_market_day() {
    # Get current date
    current_date=$(date +%Y-%m-%d)

    # Get day of week (1-7, where 1 is Monday)
    day_of_week=$(date +%u)

    # Calculate the last market day based on current day of week
    case $day_of_week in
        1) # Monday - use Friday (3 days ago)
            last_market_day=$(date -d "$current_date -3 days" +%Y-%m-%d)
            ;;
        2|3|4|5) # Tuesday through Friday - use yesterday
            last_market_day=$(date -d "$current_date -1 days" +%Y-%m-%d)
            ;;
        6) # Saturday - use Friday (1 day ago)
            last_market_day=$(date -d "$current_date -1 days" +%Y-%m-%d)
            ;;
        7) # Sunday - use Friday (2 days ago)
            last_market_day=$(date -d "$current_date -2 days" +%Y-%m-%d)
            ;;
    esac

    echo "$last_market_day"
}

# Function to kill all running instances of the ORB scanner
kill_scanner_instances() {
    echo "Killing all running instances of the ORB scanner..."
    pkill -f "$SCANNER_SCRIPT" || true
    echo "All instances have been terminated."
    echo ""
    echo "Checking for lingering screen sessions..."
    # Find all running screen sessions with 'scan_orb' in the name
    screen_sessions=$(screen -ls | grep -E '\t' | awk '{print $1}' | grep -E 'scan_orb|scan_7min' || true)
    if [ -n "$screen_sessions" ]; then
        echo "Killing the following screen sessions:"
        for session in $screen_sessions; do
            echo "  screen -S $session -X quit"
            screen -S "$session" -X quit
        done
        echo "All matching screen sessions have been killed."
    else
        echo "No matching screen sessions found."
    fi
    echo ""
}

# Function to remove all files in logs/live/old directory
remove_old_logs() {
    OLD_LOGS_DIR="$SCRIPT_DIR/../../logs/live/old"
    if [ -d "$OLD_LOGS_DIR" ]; then
        echo "Removing all files in $OLD_LOGS_DIR ..."
        rm -rf "$OLD_LOGS_DIR"/*
        echo "All files in $OLD_LOGS_DIR have been removed."
    else
        echo "Directory $OLD_LOGS_DIR does not exist."
    fi
    echo ""
}

# Display menu
show_menu() {
    clear
    echo "===== ORB Scanner Runner ====="
    echo "1) Run scanner waiting for market open (normal mode)"
    echo "2) Run scanner immediately with simulated market open (14 min duration)"
    echo "3) Run scanner immediately with simulated market open using last market day data"
    echo "4) Kill all running instances of the ORB scanner"
    echo "5) Exit"
    echo "6) Remove all files in logs/live/old directory"
    echo "=================================="
}

# Run the scanner with option 1 (normal mode)
run_normal_mode() {
    echo "Running scanner in normal mode (waiting for market open)..."
    echo "Command: bash $SCANNER_SCRIPT"
    bash "$SCANNER_SCRIPT"

    # Wait a moment for the log file to be created
    sleep 2

    # Show the log file (user can press Ctrl+C to exit)
    echo ""
    echo "Showing log file. Press Ctrl+C to exit the log view."
    echo ""
    tail -f /tmp/scanout_orb
}

# Run the scanner with option 2 (after-open with duration, no sync)
run_after_open_mode() {
    echo "Running scanner with --after-open, 14 minute duration, and no market open sync..."
    echo "Command: bash $SCANNER_SCRIPT --after-open --duration 14 --no-sync"
    bash "$SCANNER_SCRIPT" --after-open --duration 14 --no-sync

    # Wait a moment for the log file to be created
    sleep 2

    # Show the log file (user can press Ctrl+C to exit)
    echo ""
    echo "Showing log file. Press Ctrl+C to exit the log view."
    echo ""
    tail -f /tmp/scanout_orb
}

# Run the scanner with option 3 (after-open with duration, last market day, and no sync)
run_after_open_with_date() {
    # Get the last market day
    last_market_day=$(get_last_market_day)

    echo "Running scanner with --after-open, 14 minute duration, last market day ($last_market_day), and no market open sync..."
    echo "Command: bash $SCANNER_SCRIPT --after-open --duration 14 --market-open-date $last_market_day --no-sync"
    bash "$SCANNER_SCRIPT" --after-open --duration 14 --market-open-date "$last_market_day" --no-sync

    # Wait a moment for the log file to be created
    sleep 2

    # Show the log file (user can press Ctrl+C to exit)
    echo ""
    echo "Showing log file. Press Ctrl+C to exit the log view."
    echo ""
    tail -f /tmp/scanout_orb
}

# Main loop
while true; do
    show_menu
    read -p "Enter your choice [1-6]: " choice

    case $choice in
        1)
            run_normal_mode
            read -p "Press Enter to continue..."
            ;;
        2)
            run_after_open_mode
            read -p "Press Enter to continue..."
            ;;
        3)
            run_after_open_with_date
            read -p "Press Enter to continue..."
            ;;
        4)
            kill_scanner_instances
            read -p "Press Enter to continue..."
            ;;
        5)
            echo "Exiting..."
            exit 0
            ;;
        6)
            remove_old_logs
            read -p "Press Enter to continue..."
            ;;
        *)
            echo "Invalid option. Please try again."
            read -p "Press Enter to continue..."
            ;;
    esac
done
