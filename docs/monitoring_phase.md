# Monitoring Phase Implementation

## Overview

The monitoring phase is a critical component of the ORB Scanner system that activates after the Opening Range Breakout (ORB) phase completes. It continuously tracks symbols that have established opening ranges, detects price breakouts beyond these ranges, and provides detailed status reporting at regular intervals. This phase represents the transition from data collection to active trade signal generation.

## Architecture

### Core Components

#### 1. BreakoutDetector

Located in `orbscanner/core/breakout_detector.py`, this class is the central component responsible for:

- **Symbol Tracking**: Maintains a registry of monitored symbols with their opening range data
- **Breakout Detection**: Analyzes price movements to identify when a stock breaks out of its opening range
- **Volume Confirmation**: Ensures breakouts are accompanied by sufficient trading volume
- **Status Reporting**: Generates detailed logs about the status of all monitored symbols
- **Price Updates**: Tracks and updates price data for active trades

Key methods:
- `add_symbol_to_monitor(symbol, opening_range_data, scanner_data)`: Adds a symbol to the monitoring list with validation
- `check_for_breakouts(symbol_prices)`: Analyzes current prices to detect breakouts
- `report_detailed_status()`: Generates comprehensive status logs for all monitored symbols
- `update_prices(symbol_prices)`: Updates price information for active trades
- `update_price_data(symbol, price_data)`: Updates and maintains price history for a monitored symbol

#### 2. MonitoringLoop

Located in `monitoring_loop.py`, this class runs as a separate thread during the MONITORING phase and:

- **Continuous Monitoring**: Runs in the background to periodically check for breakouts
- **Data Collection**: Gathers real-time price and volume data for monitored symbols
- **Status Reporting**: Triggers detailed status reports at regular intervals
- **Error Handling**: Provides robust error handling to ensure continuous operation

Key methods:
- `start()`: Launches the monitoring loop in a separate thread
- `stop()`: Safely terminates the monitoring loop
- `_run_loop()`: Main execution loop that performs the monitoring tasks

#### 3. TradeLogger

Located in `orbscanner/core/trade_logger.py`, this class:

- **Trade Recording**: Logs breakout trades when detected
- **Price Updates**: Records price updates for active trades
- **Trade Management**: Maintains a registry of active trades

## Detailed Workflow

### 1. Phase Transition

The transition from ORB phase to MONITORING phase follows these steps:

1. **FINALIZING Phase**:
   - Opening ranges are finalized for all tracked symbols
   - Candidates are filtered based on range width and other criteria
   - Valid symbols are identified for monitoring

2. **Candidate Preservation**:
   - Long and short candidates are preserved using `_preserve_candidates_for_monitoring()`
   - A backup storage is created to prevent data loss
   - Detailed logging tracks the preservation process

3. **Transition Execution**:
   - `transition_to_monitoring_phase()` is called to handle the phase change
   - Phase manager updates the current phase to MONITORING
   - Scanner subscriptions are explicitly stopped to prevent resource leaks

### 2. Monitoring Phase Initialization

The `initialize_monitoring_phase()` method performs these critical steps:

1. **Component Initialization**:
   - TradeLogger is initialized with the correct log path
   - BreakoutDetector is created with the MONITORING phase logger
   - VIX level is retrieved for breakout detection configuration

2. **Symbol Registration**:
   - Valid symbols with opening ranges are added to the breakout detector
   - Each symbol is registered with its opening range data and scanner information
   - Symbols are filtered based on range width percentage (configurable based on VIX level)

3. **Market Data Subscription**:
   - Real-time market data subscriptions are established for all monitored symbols
   - Data flow begins for price and volume monitoring

4. **Monitoring Loop Start**:
   - A dedicated MonitoringLoop instance is created and started
   - The loop runs as a separate thread to ensure continuous monitoring

### 3. Continuous Monitoring Process

During the MONITORING phase, the system performs these operations continuously:

1. **Data Collection**:
   - Every second, current prices and volumes are collected for all monitored symbols
   - Data is retrieved from the real-time data feed

2. **Breakout Detection**:
   - Price movements are analyzed against opening ranges
   - Volume is checked to confirm breakout validity
   - Volume fade-outs are detected and logged
   - Breakout signals are generated when criteria are met

3. **Status Reporting**:
   - Every minute, `report_detailed_status()` is called
   - Detailed status logs are generated with the "MONITOR_STATUS" prefix
   - Information includes current price, distance from range boundaries, and volume data

4. **Price Updates**:
   - Active trades are continuously updated with current prices
   - Price history is maintained for trend analysis

### 4. Breakout Detection Logic

The breakout detection algorithm considers multiple factors:

1. **Price Movement**:
   - For LONG candidates: Price must break above the opening range high
   - For SHORT candidates: Price must break below the opening range low
   - A buffer percentage (configurable based on VIX) is added for confirmation

2. **Volume Confirmation**:
   - Current volume must exceed a threshold relative to opening volume
   - Volume ratio is calculated as: current_volume / max(opening_volume, avg_volume)
   - Volume threshold is dynamically adjusted based on VIX level

3. **Volume Sanity Check**:
   - Prevents false signals when volume dries up after market open
   - Uses time-adjusted Average Daily Volume (ADV) as the baseline for volume comparisons
   - Adjusts expected volume based on time of day (e.g., at 11:00 AM, only ~19% of full ADV is expected)
   - Requires current volume to be at least 5% of time-adjusted ADV
   - Logs detailed "VOLUME_FADE" warnings with time context when volume is insufficient

4. **Dynamic Parameters**:
   - All detection parameters are adjusted based on VIX level:
     - Higher VIX = stricter criteria (higher volume thresholds, wider ranges)
     - Lower VIX = more sensitive detection

## Configuration Parameters

The BreakoutDetector uses these configurable parameters:

| Parameter | Default | High VIX (>35) | Description |
|-----------|---------|---------------|-------------|
| `volume_threshold` | 1.5 | 4.0 | Minimum volume surge ratio required |
| `min_range_width_pct` | 0.5% | 0.8% | Minimum opening range width |
| `max_range_width_pct` | 3.0% | 4.0% | Maximum opening range width |
| `breakout_buffer_pct` | 0.1% | 0.2% | Buffer above/below range for confirmation |
| `adv_threshold` | 0.05 | 0.05 | Minimum current volume relative to ADV |

## Monitoring Status Format

The `report_detailed_status()` method generates detailed logs with this format:

```
MONITOR_STATUS: SYMBOL(T) P:PRICE(Δ±CHANGE%) R:LOW-HIGH D H:HIGH_DIST% L:LOW_DIST%(B) V:VOLUME(Vx)
```

Where:
- `SYMBOL`: Stock symbol
- `T`: Candidate type (L for LONG, S for SHORT)
- `PRICE`: Current price
- `CHANGE%`: Percentage change from initial price
- `LOW-HIGH`: Opening range boundaries
- `HIGH_DIST%`: Distance from high boundary (percentage)
- `LOW_DIST%`: Distance from low boundary (percentage)
- `B`: Closest boundary (H or L)
- `VOLUME`: Current volume
- `Vx`: Volume ratio compared to opening volume

## Logging

The monitoring phase uses dedicated log files:

- **Main Log**: `logs/live/scan_orb_YYYYMMDD_HHMMSS_monitoring.log`
- **Trade Log**: `logs/live/scan_orb_YYYYMMDD_HHMMSS_trades.csv`
- **Price Updates**: `logs/live/scan_orb_YYYYMMDD_HHMMSS_price_updates.csv`

Key log prefixes:
- `MONITOR_STATUS`: Regular status updates for all monitored symbols
- `MONITOR_FILTER`: Logs related to symbol filtering during monitoring setup
- `VOLUME_FADE`: Warnings when volume is insufficient compared to pre-market
- `LONG BREAKOUT` / `SHORT BREAKOUT`: Breakout detection events
- `BREAKOUT DETECTED`: Confirmation of breakout trade signals

## Error Handling

The monitoring phase includes robust error handling:

1. **Exception Handling**:
   - All exceptions in the monitoring loop are caught and logged
   - The system continues monitoring after errors
   - Detailed stack traces are recorded for debugging

2. **Recovery Mechanisms**:
   - If the monitoring loop encounters an error, it sleeps briefly before continuing
   - Critical errors are logged to both the main log and the phase-specific log

3. **Graceful Shutdown**:
   - The monitoring loop can be safely stopped when the application exits
   - All resources are properly released

## Recent Improvements

1. **Dedicated MonitoringLoop Class**:
   - Implemented as a separate thread for better performance and reliability
   - Provides clean separation of concerns

2. **Enhanced Status Reporting**:
   - More detailed symbol status updates with distance from range boundaries
   - Price and volume history tracking for trend analysis

3. **Volume Fade Detection**:
   - Added sophisticated volume fade detection to prevent false signals
   - Detailed logging of volume fade events with duration tracking

4. **Dynamic Parameter Adjustment**:
   - Parameters automatically adjust based on market volatility (VIX)
   - Provides more reliable signals in different market conditions

5. **Improved Error Handling**:
   - Better exception handling and recovery mechanisms
   - Detailed logging for easier troubleshooting

## Debugging Tips

If monitoring logs don't show expected behavior:

1. **Missing "MONITOR_STATUS" Entries**:
   - Check if the MonitoringLoop started correctly
   - Verify that the BreakoutDetector was initialized with the monitoring logger
   - Ensure the status report interval (default: 60 seconds) has elapsed

2. **No Breakout Detections**:
   - Verify that real-time price data is being collected
   - Check if symbols were properly added to the breakout detector
   - Review the volume threshold and breakout buffer settings

3. **Volume Fade Issues**:
   - Look for "VOLUME_FADE" log entries with time context information
   - Check if ADV data was correctly calculated and added to opening_range_data
   - Verify the adv_threshold setting (default: 5% of time-adjusted ADV)
   - Note the time of day in the warning messages - early morning will have lower volume requirements
   - Ensure avg_volumes is being populated during the FINALIZING phase

4. **Thread Issues**:
   - Check if the monitoring thread is running using thread inspection tools
   - Look for any exceptions in the monitoring loop
   - Verify that the thread was properly started and not terminated prematurely

## Integration Points

The monitoring phase integrates with other system components:

1. **Phase Manager**:
   - Receives phase transition notifications
   - Provides phase-specific logging

2. **Real-time Data Feed**:
   - Receives price and volume updates from IBApp's real_time_data
   - Depends on market data subscriptions being active

3. **Trade Logger**:
   - Records breakout trades when detected
   - Maintains trade history and price updates

4. **Scanner Results**:
   - Uses scanner data to determine candidate type (LONG/SHORT)
   - Incorporates priority scores from scanner results