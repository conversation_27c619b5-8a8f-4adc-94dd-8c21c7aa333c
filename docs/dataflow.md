# 7-Minute Scanner Data Flow: Deep Code Review

## Overview
This document provides a detailed review of the data flow in the 7-Minute Scanner system, focusing on:
- Data generated in each phase (PRE_MARKET, ORB, FINALIZING, MONITORING)
- The structure and type of each data object (dicts, lists, etc.)
- Data structures shared or passed between phases
- How market data is collected, processed, and stored

---

## Phase-by-Phase Data Flow

### 1. PRE_MARKET Phase
**Purpose:**
- Identify potential trading candidates before market open
- Pre-cache average volume data for these candidates

**Key Data Structures:**
- `scannerResults: dict[str, list[dict]]`
  - Stores raw results from each scanner type (e.g., 'TOP_PERC_GAIN', 'HOT_BY_VOLUME')
  - Each value is a list of dicts with keys: symbol, rank, secType, exchange, etc.
- `pre_market_candidates: list[str]`
  - List of unique symbols selected for pre-caching volume data
- `avg_volumes: dict[str, float]`
  - Maps symbol to average daily volume (from contract details or historical data)
- `contract_details_received: dict[int, bool]`
  - Tracks contract details request completion per reqId
- `historical_data_buffer: dict[int, list[BarData]]`
  - Buffers historical bars for volume calculation fallback

**Data Generation & Flow:**
- Scanners are run, populating `scannerResults`.
- `select_pre_market_candidates()` deduplicates and ranks symbols, populating `pre_market_candidates`.
- `pre_cache_average_volumes()` fills `avg_volumes` for each candidate, using contract details API or historical data fallback.
- All these structures are shared with the next phase.

---

### 2. ORB (Opening Range Building) Phase
**Purpose:**
- Track real-time price/volume for each candidate during the first N minutes after market open
- Build opening range (high, low, open, volume) for each symbol

**Key Data Structures:**
- `tracked_symbols: dict[str, dict]`
  - Maps symbol to metadata (e.g., last_price, sector, etc.)
- `opening_range_data: dict[str, dict]`
  - For each symbol: {'high': float, 'low': float, 'open': float, 'volume': int, 'source': str, 'valid': bool}
- `real_time_data: dict[str, dict]`
  - For each symbol: {'last_price': float, 'volume': int, 'bid': float, 'ask': float, ...}
- `market_data_req_ids: dict[str, int]`
  - Maps symbol to active market data request ID
- `valid_symbols: list[str]`
  - List of symbols with valid opening range data (updated at end of phase)

**Data Generation & Flow:**
- For each tracked symbol, real-time market data is requested and stored in `real_time_data`.
- `update_opening_ranges()` updates `opening_range_data` for each symbol using real-time prices.
- At the end of the phase, `valid_symbols` is populated with symbols that have valid opening ranges (width, volume, etc.).
- `opening_range_data` and `valid_symbols` are shared with the FINALIZING phase.

---

### 3. FINALIZING Phase
**Purpose:**
- Finalize opening ranges
- Fetch daily trend data (VWAP, SMA200, trend direction) for candidates
- Filter and score candidates for monitoring

**Key Data Structures:**
- `longCandidates: list[dict]`
  - Each dict: {'symbol': str, 'direction': 'LONG', 'priority_score': float, 'scanners': str, ...}
- `shortCandidates: list[dict]`
  - Each dict: {'symbol': str, 'direction': 'SHORT', 'priority_score': float, 'scanners': str, ...}
- `daily_trend_data: dict[str, dict]`
  - For each symbol: {'vwap': float, 'sma200': float, 'trend': str, ...}
- `combinedResults: list[dict]`
  - All deduplicated scanner results, with scoring and metadata
- `valid_symbols: list[str]`
  - Updated to reflect only those passing all filters
- `_preserved_long_candidates: list[dict]`
  - Deep copy of longCandidates for preservation during phase transition
- `_preserved_short_candidates: list[dict]`
  - Deep copy of shortCandidates for preservation during phase transition

**Data Generation & Flow:**
- `finalize_opening_ranges()` processes `opening_range_data` and candidate lists, applies trend alignment, and updates `longCandidates`, `shortCandidates`, and `valid_symbols`.
- `get_daily_trend_data()` populates `daily_trend_data` for each candidate.
- `process_scanner_results()` combines, scores, and filters candidates, updating `combinedResults` and candidate lists.
- `_preserve_candidates_for_monitoring()` creates deep copies of candidate lists to ensure data preservation during phase transition.
- All these structures are shared with the MONITORING phase.

---

### 4. MONITORING Phase
**Purpose:**
- Monitor selected candidates for breakouts above/below opening range
- Detect and log breakout trades
- Track price movements and provide detailed status reports

**Key Data Structures:**
- `breakout_detector: BreakoutDetector`
  - Core component that tracks symbols and detects breakouts
  - Contains `monitored_symbols: dict[str, dict]` with detailed tracking data:
    ```python
    {
      'symbol': {
        'opening_high': float,
        'opening_low': float,
        'range_width_pct': float,
        'avg_volume': float,
        'opening_volume': int,
        'last_price': float,
        'last_volume': int,
        'scanner_data': dict,
        'last_check_time': datetime,
        'breakout_detected': bool,
        'candidate_type': str,  # 'LONG' or 'SHORT'
        'initial_price': float,
        'price_history': list[float],
        'volume_history': list[int]
      }
    }
    ```
  - Contains `breakout_signals: dict[str, dict]` to track detected breakouts
  - Contains configuration parameters that adjust based on VIX level:
    ```python
    {
      'volume_threshold': float,  # Minimum volume surge ratio
      'min_range_width_pct': float,  # Minimum opening range width
      'max_range_width_pct': float,  # Maximum opening range width
      'breakout_buffer_pct': float,  # Buffer for breakout confirmation
      'volume_sanity_threshold': float  # Minimum volume relative to pre-market
    }
    ```

- `trade_logger: TradeLogger`
  - Logs executed trades, P&L, and performance metrics
  - Contains `trades: dict[str, dict]` to track all trades:
    ```python
    {
      'trade_id': {
        'symbol': str,
        'direction': str,  # 'LONG' or 'SHORT'
        'entry_price': float,
        'entry_time': datetime,
        'opening_high': float,
        'opening_low': float,
        'volume': int,
        'scanners': list[str],
        'priority_score': float,
        'vix': float,
        'sector': str,
        'current_price': float,
        'current_volume': int,
        'last_update_time': datetime,
        'status': str  # 'ACTIVE' or 'CLOSED'
      }
    }
    ```

- `monitoring_loop: MonitoringLoop`
  - Manages continuous monitoring as a separate thread
  - Contains timing parameters:
    ```python
    {
      'monitoring_interval': int,  # Seconds between checks (default: 1)
      'status_report_interval': int  # Seconds between status reports (default: 60)
    }
    ```

- `real_time_data: dict[str, dict]`
  - Continuously updated with latest price and volume data
  - Used by MonitoringLoop to check for breakouts

**Data Generation & Flow:**
1. **Initialization**:
   - `initialize_monitoring_phase()` sets up the monitoring infrastructure
   - `breakout_detector` is initialized with preserved candidates and opening ranges
   - `trade_logger` is initialized to record breakout trades
   - `monitoring_loop` is started as a separate thread

2. **Continuous Monitoring**:
   - `monitoring_loop._run_loop()` executes continuously:
     - Collects current prices from `real_time_data`
     - Calls `breakout_detector.check_for_breakouts(symbol_prices)`
     - Calls `breakout_detector.update_prices(symbol_prices)`
     - Periodically calls `breakout_detector.report_detailed_status()`

3. **Breakout Detection**:
   - When a breakout is detected:
     - `breakout_detector` calls `trade_logger.log_breakout()`
     - Trade is recorded with entry price, time, and other metadata
     - Symbol is marked as `breakout_detected = True` to prevent duplicate signals

4. **Status Reporting**:
   - Every minute, detailed status is logged for all monitored symbols
   - Status includes price, distance from range boundaries, and volume data
   - Price and volume history are maintained for trend analysis

5. **Data Persistence**:
   - Trades are logged to CSV files for later analysis
   - Price updates are recorded for performance tracking

---

## Data Structure Summary Table

| Name                       | Type                       | Populated In      | Used In           | Description |
|----------------------------|----------------------------|-------------------|-------------------|-------------|
| scannerResults             | dict[str, list[dict]]      | PRE_MARKET        | All               | Raw scanner results by type |
| pre_market_candidates      | list[str]                  | PRE_MARKET        | ORB               | Symbols selected for volume pre-caching |
| avg_volumes                | dict[str, float]           | PRE_MARKET        | ORB, FINALIZING   | Average daily volume per symbol |
| tracked_symbols            | dict[str, dict]            | ORB               | ORB, FINALIZING   | Metadata for each tracked symbol |
| opening_range_data         | dict[str, dict]            | ORB               | FINALIZING, MONITORING | Opening range (high, low, open, volume) per symbol |
| real_time_data             | dict[str, dict]            | ORB               | ORB, MONITORING   | Real-time price/volume per symbol |
| valid_symbols              | list[str]                  | ORB, FINALIZING   | FINALIZING, MONITORING | Symbols with valid opening ranges |
| longCandidates             | list[dict]                 | FINALIZING        | MONITORING        | Final long candidates |
| shortCandidates            | list[dict]                 | FINALIZING        | MONITORING        | Final short candidates |
| _preserved_long_candidates | list[dict]                 | FINALIZING        | MONITORING        | Deep copy of long candidates for phase transition |
| _preserved_short_candidates| list[dict]                 | FINALIZING        | MONITORING        | Deep copy of short candidates for phase transition |
| daily_trend_data           | dict[str, dict]            | FINALIZING        | MONITORING        | VWAP, SMA200, trend per symbol |
| combinedResults            | list[dict]                 | FINALIZING        | Reporting         | All deduplicated, scored candidates |
| breakout_detector          | BreakoutDetector           | MONITORING        | MONITORING        | Monitors for breakouts |
| trade_logger               | TradeLogger                | MONITORING        | MONITORING        | Logs trades and P&L |
| monitoring_loop            | MonitoringLoop             | MONITORING        | MONITORING        | Manages continuous monitoring thread |
| monitored_symbols          | dict[str, dict]            | MONITORING        | MONITORING        | Detailed tracking data in breakout_detector |
| breakout_signals           | dict[str, dict]            | MONITORING        | MONITORING        | Detected breakouts in breakout_detector |
| trades                     | dict[str, dict]            | MONITORING        | MONITORING, Reporting | Active and completed trades in trade_logger |

---

## Market Data Flow

1. **Scanner Results:**
   - IBKR scanner API returns lists of contracts (symbols) with metadata.
   - Results are stored in `scannerResults`.

2. **Volume Data:**
   - For each candidate, average volume is fetched via contract details or historical data.
   - Stored in `avg_volumes`.

3. **Real-Time Market Data:**
   - For each tracked symbol, real-time price and volume are requested and stored in `real_time_data`.
   - Used to build `opening_range_data` during ORB phase.
   - Continuously updated during MONITORING phase for breakout detection.

4. **Historical Data (if enabled):**
   - Used for opening range calculation if running after market open or in simulation.
   - Results stored in `opening_range_data`.

5. **Trend Data:**
   - For each candidate, daily trend data (VWAP, SMA200, trend) is fetched and stored in `daily_trend_data`.

6. **Breakout Monitoring:**
   - During MONITORING, real-time prices are checked against opening ranges for breakouts.
   - Price and volume history are maintained for each symbol.
   - Breakouts trigger trade logging in `trade_logger`.
   - Detailed status reports are generated every minute.

7. **Trade Logging:**
   - Breakout trades are logged with entry price, time, and metadata.
   - Price updates are recorded for performance tracking.
   - Trade data is persisted to CSV files for later analysis.

---

## Data Sharing and Transformation Between Phases

### PRE_MARKET to ORB
- `scannerResults` → `pre_market_candidates` (filtering and deduplication)
- `pre_market_candidates` + `avg_volumes` → `tracked_symbols` (adding volume metadata)

### ORB to FINALIZING
- `tracked_symbols` + real-time data → `opening_range_data` (building opening ranges)
- `opening_range_data` → `valid_symbols` (filtering based on range criteria)

### FINALIZING to MONITORING
- `valid_symbols` + trend data → `longCandidates`/`shortCandidates` (candidate classification)
- `longCandidates`/`shortCandidates` → `_preserved_long_candidates`/`_preserved_short_candidates` (deep copy for preservation)
- `_preserved_long_candidates`/`_preserved_short_candidates` + `opening_range_data` → `monitored_symbols` (in breakout_detector)

### Within MONITORING
- `real_time_data` → `symbol_prices` (in monitoring_loop)
- `symbol_prices` → `monitored_symbols` (price/volume updates)
- `monitored_symbols` → `breakout_signals` (when breakouts detected)
- `breakout_signals` → `trades` (in trade_logger)

---

## Critical Data Preservation Mechanisms

The system employs several mechanisms to ensure data integrity during phase transitions:

1. **Deep Copying of Candidates**:
   - `_preserve_candidates_for_monitoring()` creates deep copies of candidate lists
   - Prevents data loss during phase transitions
   - Includes backup storage in `_monitoring_candidates` dictionary

2. **Redundant Storage**:
   - Key data like opening ranges is stored in multiple structures
   - `valid_symbols` list is maintained alongside `opening_range_data` dictionary

3. **Phase-Specific Logging**:
   - Each phase has dedicated loggers
   - Critical data transformations are logged for debugging
   - Detailed status reports provide visibility into data state

4. **Error Recovery**:
   - Exception handling in critical data flow paths
   - Fallback mechanisms when primary data sources fail
   - Automatic retry logic for data collection

---

## Notes on Data Integrity and Validation

- **Symbol Validation**:
  - `validate_symbol()` ensures all symbols are valid strings
  - Prevents object references from being used as dictionary keys
  - Provides fallback mechanisms for invalid symbols

- **Data Normalization**:
  - `ensure_float()` and `ensure_int()` normalize numeric values
  - Default values are provided for missing data
  - Minimum thresholds enforce data quality

- **Defensive Programming**:
  - Extensive null checks before accessing data structures
  - Fallback to empty collections when expected data is missing
  - Detailed logging of data anomalies

- **Thread Safety**:
  - MonitoringLoop runs in a separate thread with proper synchronization
  - Critical data structures are accessed with appropriate locking
  - Thread lifecycle is managed with start/stop methods

---

## Market Data Types Used

- **Scanner API:** Symbol lists, price, volume, change %, etc.
- **Contract Details API:** Average daily volume, sector, exchange info
- **Market Data API:** Real-time price, volume, bid/ask, etc.
- **Historical Data API:** 1-min/daily bars for opening range and volume calculation
- **Trend Data:** VWAP, SMA200, trend direction (from historical bars)

---

## Monitoring Phase Data Flow Diagram

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  FINALIZING     │     │  MONITORING     │     │  Reporting      │
│  PHASE          │────▶│  PHASE          │────▶│  & Analysis     │
└─────────────────┘     └─────────────────┘     └─────────────────┘
       │                        │                        │
       ▼                        ▼                        ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│ longCandidates  │     │ breakout_       │     │ trades.csv      │
│ shortCandidates │────▶│ detector        │────▶│ price_updates.csv│
│ opening_range_  │     │ (monitored_     │     │ finalizing_     │
│ data            │     │  symbols)       │     │ report.md       │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                               │
                               │
                               ▼
                        ┌─────────────────┐
                        │ MonitoringLoop  │
                        │ (continuous     │
                        │  thread)        │
                        └─────────────────┘
                               │
                               │
                               ▼
                        ┌─────────────────┐
                        │ real_time_data  │◀───── IBKR API
                        │ (price/volume   │       (Market Data)
                        │  updates)       │
                        └─────────────────┘
```

---

## Summary

The 7-Minute Scanner's data flow is highly modular and phase-oriented, with each phase building on data collected in previous phases. The system employs robust data preservation mechanisms during phase transitions, particularly between FINALIZING and MONITORING phases where candidate preservation is critical.

The MONITORING phase represents the culmination of the data pipeline, where all previously collected data (opening ranges, candidate classifications, trend data) is used to detect breakouts and generate trade signals. The MonitoringLoop provides continuous real-time monitoring as a separate thread, ensuring responsive breakout detection while maintaining detailed status reporting.

Data integrity is maintained through validation, normalization, and defensive programming techniques throughout the pipeline. The system's modular design allows for clear separation of concerns while ensuring efficient data sharing between components.