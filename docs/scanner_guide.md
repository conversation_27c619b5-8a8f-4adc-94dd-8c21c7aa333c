# 7-Minute ORB Scanner Documentation

## Overview

The 7-Minute ORB Scanner is a comprehensive tool designed to identify high-probability Opening Range Breakout (ORB) trading candidates. The scanner operates through distinct phases during the critical first 7 minutes after market open (9:30-9:37 AM EST), using real-time market data to track opening ranges and calculate dynamic priority scores.

**Key Innovation**: The scanner combines real-time market data collection with sophisticated filtering algorithms to identify stocks with optimal characteristics for ORB trading, including volume surge analysis, opening range validation, and trend alignment verification.

## Key Features

- **Multi-Phase Operation**: Operates through PRE_MARKET, ORB, FINALIZING, and MONITORING phases with precise timing
- **Real-Time Opening Range Tracking**: Dynamically tracks high/low ranges for up to 100 symbols during the 7-minute ORB window
- **VIX-Based Market Adaptation**: Automatically adjusts scanner parameters, filters, and position sizing based on current VIX levels
- **Volume Data Skip Mode**: Built-in mode (SKIP_VOLUME_DATA=True) to avoid API rate limits by using IBKR's built-in volume filters
- **Historical Data Integration**: Seamless fallback to historical data when testing outside market hours
- **Comprehensive Trade Logging**: Automated trade signal generation with CSV and JSON logging
- **Real-Time Data Validation**: Strict validation ensuring only real market data is used (no simulation)
- **Dynamic Priority Scoring**: Multi-factor scoring system based on volume, price action, and technical indicators
- **Trend Alignment Filtering**: Advanced trend analysis using VWAP and SMA200 for directional confirmation
- **Phase Manager Integration**: Precise timing control with automatic phase transitions

## Usage

### Basic Commands

The scanner is typically run using the `runit.sh` wrapper script, which calls `run_scanner.sh` and executes the main scanner code in `scanner_main.py`.

#### Single Scan Mode
```bash
./runit.sh
```

This runs a single scan and generates a report. Useful for testing or getting a snapshot of current market conditions.

#### Continuous Scanning Mode (Recommended for Live Trading)
```bash
./runit.sh --continuous --duration 7 --interval 30
```

This runs continuous scanning for 7 minutes with 30-second intervals, updating opening ranges and priority scores with each scan.

### Testing After Market Open

When testing the scanner after market open, use the `--after-open` flag:

```bash
./runit.sh --after-open
```

This automatically enables historical data mode and simulates running at market open regardless of actual time.

### Advanced Options

You can combine multiple flags for different testing scenarios:

```bash
./runit.sh --continuous --duration 7 --interval 15 --after-open
```

This will run continuous scanning for 7 minutes with 15-second intervals, using historical data mode.

### Key Configuration Flags

- **SKIP_VOLUME_DATA**: Currently set to `True` by default due to API rate limiting issues with historical volume data requests
- **Historical Data Mode**: Automatically enabled when using `--after-open` or when scanner detects it's running outside market hours
- **VIX-Based Adjustments**: Automatically applied based on current VIX level

### Scanner Rate Limiting

The scanner implements several measures to avoid hitting IBKR's API rate limits:

1. **Phase-Specific Scanning**: Scanners only run during pre-market and ORB phases, and are explicitly stopped during FINALIZING and MONITORING phases
2. **Batch Processing**: Scanner requests are processed in batches of 4 (MAX_SIMULTANEOUS_SCANNERS) to stay well below IBKR's 10 simultaneous subscription limit
3. **Increased Batch Delay**: A 3-second delay between scanner batches ensures proper processing of cancellations
4. **Enhanced Cancellation**: The stop_scanners method includes verification and retry logic to ensure all subscriptions are properly cancelled
5. **Explicit Cancellation During Phase Transitions**: Scanner subscriptions are explicitly cancelled when transitioning from ORB to FINALIZING phase

These improvements help prevent hitting IBKR's rate limit of 60 scanner requests per 10 minutes.

### Volume Data Skip Mode

**⚠️ Important Note**: The scanner currently includes a built-in mode that skips volume data collection and uses IBKR's built-in relative volume filters instead. This mode is enabled by default with the `SKIP_VOLUME_DATA` flag set to `True`. 

**This flag is currently required due to bugs in the volume data collection code that can cause API rate limiting issues. In future versions, once the volume data collection is fixed, this flag should be removed to enable full volume analysis.**

When this mode is enabled:
1. The scanner skips all historical data requests for average volume calculation
2. It uses IBKR's built-in `today'sVolume/avgDailyVolume` filter in scanner parameters
3. It implements an alternative priority scoring method that doesn't depend on volume ratios
4. It focuses on scanner appearance, price action, and opening range characteristics
5. **Volume criteria are still enforced through IBKR's built-in filters** - this is not optional

This mode is particularly useful for:
- Avoiding rate limits and timeouts during high-volume periods
- Running in environments with limited API access
- Testing the scanner before market open
- Improving reliability when historical data is unavailable

**Known Issues with Volume Data Collection**:
- Historical volume requests can exceed IBKR's API rate limits
- Volume data collection may timeout frequently for certain symbols
- Batch processing of volume requests needs optimization
- Volume calculation logic has potential race conditions

**Critical Note**: Even in skip mode, volume and liquidity remain core, non-negotiable criteria. The scanner still enforces minimum volume requirements through IBKR's built-in filters (`averageDailyVolume`, `volumeAbove`) - it just doesn't collect historical volume data for ratio calculations.

### Historical Data Mode

When testing after market open, you can use historical data to calculate opening ranges instead of simulated data:

```bash
./runit.sh --after-open --use-historical-data
```

This will retrieve historical 1-minute bars for the first 7 minutes after market open (9:30-9:37 AM EST) for each tracked symbol and calculate the opening range based on this data. This is useful for testing the scanner at any time of day while still getting accurate opening ranges.

**MAJOR IMPROVEMENT**: The scanner now uses optimized parameters for historical data requests that have been proven to work reliably:
- Empty endDateTime (uses most recent data)
- "1 D" duration (1 day - includes market open period)
- "1 min" bar size (1-minute bars)
- useRTH=0 (includes all data, not just regular trading hours)

The scanner then post-processes the returned data to extract only the first 7 minutes after market open (9:30-9:36 AM). This approach is much more reliable than previous methods and works consistently regardless of when the scanner is run.

If the exact market open bar (9:30 AM) can't be found, the scanner will:
1. Look for any bars in the 9:30-9:36 time window
2. If that fails, use the earliest bars from the data set

This ensures that the scanner can always create valid opening ranges using the best available data.

You can also specify a specific market open date and time:

```bash
./runit.sh --market-open-date 2023-05-15 --market-open-time 09:30 --after-open
```

This will use historical data from May 15, 2023, with market open at 9:30 AM EST.

### After-Open Mode

When running the scanner significantly after market open, you can use the `--after-open` flag to simulate as if the market just opened:

```bash
./runit.sh --after-open
```

This flag will:
1. Automatically enable historical data mode
2. Simulate as if the market just opened, regardless of the actual time
3. Run for the full 7-minute ORB phase
4. Retrieve historical opening range data for the tracked symbols
5. Transition through phases normally (ORB → finalizing → monitoring)

This is particularly useful when you want to test the scanner during regular market hours but still want it to go through the complete phase transitions as if it were running at market open.

#### Real-Time vs Historical Data Mode

The scanner operates in two distinct modes for opening range calculation:

1. **Real-Time Market Open Mode**:
   - Used when running the scanner during actual market open (9:30 AM ET)
   - Collects tick-by-tick price and volume data in real-time
   - Uses specialized methods to update different aspects of the opening range
   - Builds the opening range incrementally as data arrives
   - Requires active market data subscriptions for all tracked symbols

2. **Historical Data Mode** (enabled with `--after-open` or `--use-historical-data`):
   - Used when running the scanner after market open or for testing
   - Retrieves historical 1-minute bars for the opening range period
   - Calculates the opening range based on the high/low values from these bars
   - Requires historical data permissions in your market data subscription
   - More reliable for testing as it uses actual historical data

The scanner now enforces strict data quality requirements in both modes:
- Only real market data is used - no simulation or fallback data allowed
- Symbols with missing or invalid data are excluded from processing
- Comprehensive validation ensures all price values are valid and positive
- Detailed logging provides transparency into the data collection process

### Combined Continuous and Historical Mode

For convenience, you can combine continuous scanning with historical data mode:

```bash
./runit.sh --continuous --after-open --duration 7 --interval 30
```

This is equivalent to `--continuous --after-open` and will run continuous scanning with historical data for opening ranges. This is the recommended mode for testing the scanner after market open.

### Production Usage

For production use, it's recommended to:

1. Run the scanner at 9:20 AM EST to check VIX levels and start pre-market phase
2. Continue with ORB phase at 9:30 AM EST (market open)
3. Run for 7 minutes with 15-30 second intervals
4. Finalize candidate list at 9:37 AM EST
5. Begin trading based on the identified candidates

```bash
# At 9:20 AM - Start pre-market phase and check VIX
./runit.sh

# At 9:30 AM - Continue with ORB phase
./runit.sh --continuous --duration 7 --interval 15
```

## Command-Line Arguments

### Shell Script Arguments (runit.sh / run_scanner.sh)

- `--continuous`: Enable continuous scanning mode for dynamic opening range tracking
- `--duration MINUTES`: Duration of continuous scanning in minutes (default: 7)
- `--interval SECONDS`: Interval between scans in seconds (default: 30)
- `--after-open`: Simulate as if market just opened, regardless of actual time (auto-enables historical data)
- `--use-historical-data`: Use historical data for opening ranges when testing after market open
- `--market-open-date DATE`: Market open date for historical data (format: YYYY-MM-DD, default: today)
- `--market-open-time TIME`: Market open time for historical data (format: HH:MM, default: 09:30)
- `--historical-data-timeout SEC`: Timeout for historical data requests in seconds (default: 30)
- `--no-sync`: Disable synchronization with market open (default: enabled)
- `--help`: Display help message with all available options

### Python Script Arguments (scanner_main.py)

- `--output-dir DIR`: Directory to save logs and reports (required)
- `--log-base PATH`: Base name for log files (required)
- `--client-id ID`: TWS Client ID (default: random 3000-6000)
- `--host HOST`: TWS host address (default: 127.0.0.1)
- `--port PORT`: TWS port (default: 7497)
- `--continuous-scan`: Enable continuous scanning mode
- `--scan-duration MINUTES`: Duration of continuous scanning in minutes (default: 7)
- `--scan-interval SECONDS`: Interval between scans in seconds (default: 30)
- `--after-open`: Simulate as if market just opened, regardless of actual time (auto-enables historical data)
- `--use-historical-data`: Use historical data for opening ranges when testing after market open
- `--market-open-date DATE`: Market open date for historical data (format: YYYY-MM-DD, default: today)
- `--market-open-time TIME`: Market open time for historical data (format: HH:MM, default: 09:30)
- `--historical-data-timeout SEC`: Timeout for historical data requests in seconds (default: 30)
- `--sync-with-market-open`: Synchronize with market open (default: enabled)

### Example Command with All Options

```bash
./runit.sh --continuous --duration 7 --interval 20 --after-open --market-open-date 2023-05-15 --market-open-time 09:30 --historical-data-timeout 45
```

This command will:
1. Run in continuous scanning mode
2. Scan for 7 minutes
3. Update every 20 seconds
4. Simulate as if market just opened, regardless of actual time
5. Automatically enable historical data mode
6. Use market open data from May 15, 2023 at 9:30 AM
7. Set a 45-second timeout for historical data requests
8. Generate a comprehensive report with candidate lists and opening ranges

### Example for Running After Market Open

```bash
./runit.sh --after-open --duration 390
```

This command will:
1. Simulate as if market just opened, regardless of actual time
2. Automatically enable historical data mode
3. Run for the full 7-minute ORB phase
4. Transition through phases normally (ORB → finalizing → monitoring)
5. Continue monitoring for the entire trading day (390 minutes = 6.5 hours)

## Scanner Types

The scanner uses different sets of scanner types based on VIX level:

### Low VIX (≤ 25)
- HOT_BY_VOLUME
- TOP_PERC_GAIN
- TOP_TRADE_RATE
- MOST_ACTIVE

### High VIX (> 25)
- HOT_BY_VOLUME
- MOST_ACTIVE
- TOP_TRADE_RATE

## VIX-Based Adjustments

The scanner dynamically adjusts its parameters based on the current VIX level to adapt to different market volatility conditions. This ensures that the scanner is more conservative during high-volatility periods and more aggressive during low-volatility periods.

### Scanner Selection

- **VIX > 25 (High Volatility)**:
  - Uses a reduced set of scanner types: HOT_BY_VOLUME, MOST_ACTIVE, TOP_TRADE_RATE
  - Focuses on higher liquidity and more stable instruments

- **VIX ≤ 25 (Normal Volatility)**:
  - Uses the full set of scanner types: HOT_BY_VOLUME, TOP_PERC_GAIN, TOP_TRADE_RATE, MOST_ACTIVE
  - Allows for more diverse candidate selection

### Candidate Limits and Dynamic Filtering

**Fixed Opening Range List Principle**: The scanner fixes the ORB candidate list at market open (9:37 AM) based on clear, backtested filters. No new candidates are added after the opening range period to maintain focus and consistency.

- **VIX > 35 (Extreme Volatility)**:
  - Maximum candidates: 25 (focused and manageable list)
  - **Core Volume Criteria**: Minimum 1M shares daily volume (non-negotiable)
  - **Core Liquidity Criteria**: Market Cap > $5B for stability
  - **Dynamic Sector Weighting**: Reduces weighting for high-volatility sectors (Tech -30%, Finance -20%, Biotech -40%) rather than hard bans

- **VIX > 30 (High Volatility)**:
  - Maximum candidates: 35 (balanced focus)
  - **Core Volume Criteria**: Minimum 750K shares daily volume (non-negotiable)
  - **Core Liquidity Criteria**: Market Cap > $2B for adequate liquidity
  - **Dynamic Sector Weighting**: Moderate sector adjustments (Tech -20%, Biotech -30%)

- **VIX > 25 (Elevated Volatility)**:
  - Linear reduction of candidates between 50 and 35 based on formula:
    ```
    reduction_factor = (vix_level - 25) / 5  # 0 at VIX 25, 1 at VIX 30
    max_candidates = 50 - (15 * reduction_factor)
    ```
  - **Core Volume Criteria**: Minimum 750K shares daily volume (non-negotiable)
  - **Core Liquidity Criteria**: Market Cap > $2B
  - **Dynamic Sector Weighting**: Light sector adjustments based on historical volatility

- **VIX > 20 (Moderate Volatility)**:
  - Maximum candidates: 50 (standard focused list)
  - **Core Volume Criteria**: Minimum 500K shares daily volume (non-negotiable)
  - **Dynamic Sector Weighting**: Minor adjustments for extremely volatile sectors only

- **VIX ≤ 20 (Low Volatility)**:
  - Maximum candidates: 50 (maintains focus while allowing opportunity)
  - **Core Volume Criteria**: Minimum 500K shares daily volume (always required)
  - **No Sector Restrictions**: All sectors weighted equally under normal conditions

**Key Principle**: Volume and liquidity are always core, non-negotiable criteria. Sector considerations use dynamic weighting rather than hard exclusions to maintain opportunity while managing risk.

### Scanner Filters

The scanner applies different filters based on VIX level. **Note**: When `SKIP_VOLUME_DATA=True` (current default), the scanner uses IBKR's built-in volume filters instead of the volumeAbove filters shown below.

```python
# Base filters (always applied)
base_filters = [
    TagValue("priceAbove", "5"),           # Minimum $5 (reduced for testing)
    TagValue("priceBelow", "500"),         # Maximum $500
    TagValue("hasSparseData", "0"),        # Exclude sparse data
    TagValue("peRatioAbove", "0"),         # Positive P/E ratio
    TagValue("exDividendDate", "false"),   # Exclude ex-dividend stocks
    TagValue("averageDailyVolume", "500000")  # 500K minimum liquidity floor
]

# VIX-based volume adjustments (when SKIP_VOLUME_DATA=False)
# Low VIX (≤ 25)
if vix_level <= 25:
    filters.append(TagValue("volumeAbove", "500000"))

# High VIX (> 35) 
elif vix_level > 35:
    filters.extend([
        TagValue("volumeAbove", "1000000"),
        TagValue("marketCapAbove", "5000000000")  # $5B+ market cap
    ])

# Moderate VIX (25-35)
else:
    filters.extend([
        TagValue("volumeAbove", "750000"),
        TagValue("marketCapAbove", "2000000000")  # $2B+ market cap
    ])
```

## Priority Scoring

The priority score for each symbol is calculated using a weighted formula that considers multiple factors. This dynamic scoring system helps identify the most promising candidates for ORB trading by prioritizing symbols with strong volume, price action, and opening range characteristics.

**Note**: When `SKIP_VOLUME_DATA=True` (current default), the priority scoring uses an alternative method that doesn't rely on volume ratios since historical volume data is not collected.

### Scoring Formula (with Volume Data)

```python
def calculate_priority_score(symbol_data):
    # Get volume data (when available)
    volume_ratio = current_volume / avg_volume

    # Get price data
    price_ratio = current_price / pre_market_high

    # Get opening range data
    range_width = (range_high - range_low) / range_low

    # Calculate components with caps to prevent outliers from dominating
    volume_component = 0.4 * min(volume_ratio, 10.0)  # Cap at 10x
    price_component = 0.3 * min(price_ratio, 1.5)     # Cap at 1.5x
    range_component = 0.2 * min(range_width * 10, 2.0)  # Cap at 2.0
    scanner_component = 0.1 * scanner_count

    # Calculate final priority score
    priority_score = volume_component + price_component + range_component + scanner_component

    return priority_score
```

### Alternative Scoring Formula (SKIP_VOLUME_DATA=True)

```python
def calculate_priority_score_no_volume(symbol_data):
    # Focus on scanner appearance, price action, and opening range characteristics
    scanner_count = symbol_data.get('scanner_count', 1)
    price_ratio = current_price / open_price
    range_width = (range_high - range_low) / range_low

    # Adjusted weights without volume component
    scanner_component = 0.5 * scanner_count        # Increased weight for scanner appearance
    price_component = 0.3 * min(price_ratio, 1.5)  # Price momentum
    range_component = 0.2 * min(range_width * 10, 2.0)  # Opening range volatility

    priority_score = scanner_component + price_component + range_component
    return priority_score
```

### Component Weights

#### With Volume Data:
- **Volume Component (40%)**: Volume ratio compared to average volume (capped at 10x)
- **Price Component (30%)**: Price ratio compared to pre-market high (capped at 1.5x)
- **Range Component (20%)**: Opening range width (normalized and capped at 2.0)
- **Scanner Component (10%)**: Number of scanners the symbol appeared in

#### Without Volume Data (SKIP_VOLUME_DATA=True):
- **Scanner Component (50%)**: Number of scanners the symbol appeared in (increased weight)
- **Price Component (30%)**: Price momentum from open (capped at 1.5x)
- **Range Component (20%)**: Opening range volatility (normalized and capped at 2.0)

## Continuous Scanning Process

The continuous scanning process is designed to capture evolving market conditions during the critical first 7 minutes after market open. This approach provides a more dynamic and accurate view of potential ORB candidates compared to a single scan.

### Process Flow

1. **Initialization Phase**:
   - Connect to TWS API and verify connection
   - Retrieve VIX value to determine scanner parameters
   - Initialize data structures for tracking symbols and opening ranges
   - Set up scanner parameters based on VIX level

2. **Opening Range Building (ORB) Phase** (Exactly 7 minutes):
   - Track new symbols and add them to the opening range tracking list (up to 100 symbols)
   - Update opening ranges for tracked symbols with current price data
   - Calculate priority scores based on volume, price, and range data
   - Update candidate lists with the latest scores
   - Add scan results to history for reporting
   - Phase ends exactly 7 minutes after start time

3. **Finalizing Phase** (Exactly 2 minutes 57 seconds):
   - Finalize opening ranges at the 7-minute mark
   - Collect daily trend data for top candidates (limited to 2:30 minutes)
   - Prioritize trend data collection based on candidate scores
   - Process candidates in batches of 20 with 0.7-second delay between batches
   - Filter out candidates with missing trend data
   - Generate final candidate lists with priority-based sorting
   - Apply VIX-based candidate limits
   - Phase ends exactly 2:57 after start time

4. **Monitoring Phase** (Until market close or specified duration):
   - Monitor price action for all selected candidates
   - Detect breakouts above/below opening ranges
   - Generate trade signals when breakouts occur
   - Create detailed report with scan history and candidate information
   - Generate CSV files for further analysis

### Implementation Details

#### Opening Range Validation

The scanner validates opening ranges to ensure they meet specific criteria:

```python
def validate_opening_range(self, symbol):
    """
    Validate the opening range quality for a symbol.
    Only call this during or after the opening range formation.

    Args:
        symbol (str): The symbol to check

    Returns:
        tuple: (is_valid, reason)
    """
    if symbol not in self.opening_range_data:
        return False, "No opening range data available"

    or_data = self.opening_range_data[symbol]
    high = or_data.get('high', 0)
    low = or_data.get('low', 0)
    open_price = or_data.get('open', 0)

    if open_price <= 0:
        return False, "Invalid open price"

    # Calculate range percentage
    range_pct = (high - low) / open_price if open_price > 0 else 0

    # Check minimum range requirement
    if range_pct < 0.005:  # 0.5% minimum range
        return False, f"Insufficient opening range ({range_pct:.4f} < 0.005)"

    return True, "Valid opening range"
```

#### Dynamic Spread Logic

The scanner applies different spread filters based on price level:

```python
# Enhanced spread check with dynamic logic
price = symbol_data.get('last_price', 0)
spread = symbol_data.get('spread_pct', float('inf'))

# For low-priced stocks, use absolute spread instead of percentage
if price < 20:
    absolute_spread = price * spread
    if absolute_spread > 0.02:  # $0.02 absolute spread for <$20 stocks
        return False, f"Symbol {symbol} excluded: Wide absolute spread (${absolute_spread:.3f} > $0.02)"
else:
    # For higher-priced stocks, use percentage spread
    if spread < float('inf') and spread > MAX_SPREAD:
        return False, f"Symbol {symbol} excluded: Wide percentage spread ({spread:.4f} > {MAX_SPREAD:.4f})"
```

#### Continuous Scanning Implementation

```python
def run_continuous_scanner(self, start_time, end_time, interval=30):
    """Run scanners continuously from start_time to end_time with specified interval"""
    self.continuous_scan_active = True

    # Use the defined phase duration constants for strict timing
    orb_duration = ORB_PHASE_DURATION  # 7 minutes

    # Store phase start time for accurate timing
    self.phase_start_times = {}
    self.phase_start_times[PHASE_ORB] = datetime.now(pytz.timezone('US/Eastern'))

    # Calculate exact phase end times
    orb_end_time = self.phase_start_times[PHASE_ORB] + orb_duration

    # Run scanners until end_time
    while current_time < end_time and self.continuous_scan_active:
        # Get current phase
        current_phase = self.phase_manager.current_phase

        # CRITICAL FIX: Only run scanners during PRE_MARKET and ORB phases
        if current_phase in [PhaseManager.PRE_MARKET, PhaseManager.ORB]:
            # Run scanners if we're not in the middle of processing results
            if not self.processing_results and not self.allScannersFinished:
                # Start scanners with current VIX level
                self.start_scanners(vix_level=self.vix_value, is_continuous=True, phase=current_phase)

                # Wait for results
                wait_start = time.time()
                while not self.allScannersFinished and time.time() - wait_start < SCANNER_TIMEOUT:
                    time.sleep(0.5)
        else:
            # CRITICAL FIX: Ensure no scanners are running in FINALIZING or MONITORING phases
            if self.active_scanner_subscriptions:
                logging.info(f"CRITICAL FIX: Stopping all scanners in {current_phase} phase")
                self.stop_scanners()

        # Update opening ranges
        self.update_opening_ranges()

        # Add to scan history
        self.record_scan_history()

        # Check for phase transition
        if current_phase == PHASE_ORB and current_time >= orb_end_time:
            # CRITICAL FIX: Stop all scanners before transitioning to FINALIZING phase
            if self.active_scanner_subscriptions:
                logging.info("CRITICAL FIX: Stopping all scanner subscriptions before transitioning to FINALIZING phase")
                self.stop_scanners()
                # Add a small delay to ensure all cancellations are processed
                time.sleep(1)

            # Transition to finalizing phase with exact timing
            self.phase_start_times[PHASE_FINALIZING] = current_time
            finalizing_end_time = self.phase_start_times[PHASE_FINALIZING] + FINALIZING_PHASE_DURATION

            # Finalize opening ranges with time limit
            trend_collection_end_time = self.phase_start_times[PHASE_FINALIZING] + TREND_DATA_COLLECTION_DURATION
            self.finalize_opening_ranges(max_duration=TREND_DATA_COLLECTION_DURATION)

            # Transition to monitoring phase at exact time
            while datetime.now() < finalizing_end_time:
                time.sleep(1)

            self.phase_start_times[PHASE_MONITORING] = datetime.now()
            current_phase = PHASE_MONITORING

        # Sleep until next interval
        time.sleep(interval)
        current_time = datetime.now()

    # Finalize opening ranges
    self.finalize_opening_ranges()
```

#### Prioritized Trend Data Collection

The scanner prioritizes trend data collection based on candidate scores:

```python
# Enhanced scanner cancellation with verification and retry
def stop_scanners(self):
    """
    Cancel all active scanner subscriptions with verification

    CRITICAL FIX: Enhanced to ensure proper cancellation and verification
    to prevent lingering subscriptions that could hit rate limits
    """
    # Make a copy of the dictionary items to avoid modification during iteration
    scanner_items = list(self.scannerRequestIds.items())

    if not scanner_items:
        logging.info("No active scanner subscriptions to cancel")
        self.allScannersFinished = True
        self.active_scanner_subscriptions = False
        return

    logging.info(f"CRITICAL FIX: Cancelling {len(scanner_items)} active scanner subscriptions with verification...")

    # Track successful cancellations
    cancelled_count = 0
    failed_cancellations = []

    # Cancel each subscription
    for scanner_type, req_id in scanner_items:
        try:
            logging.info(f"Cancelling {scanner_type} scanner subscription (Req ID: {req_id}).")
            self.cancelScannerSubscription(req_id)
            # Immediately mark as finished to ensure our internal state is updated
            self.scannerFinished[req_id] = True
            cancelled_count += 1
        except Exception as e:
            logging.error(f"Error cancelling {scanner_type} scanner: {e}")
            # Still mark as finished even if there was an error
            self.scannerFinished[req_id] = True
            failed_cancellations.append((scanner_type, req_id))

    # Retry failed cancellations once
    if failed_cancellations:
        logging.warning(f"Retrying {len(failed_cancellations)} failed cancellations...")
        retry_success = 0

        for scanner_type, req_id in failed_cancellations:
            try:
                logging.info(f"Retry: Cancelling {scanner_type} scanner subscription (Req ID: {req_id}).")
                self.cancelScannerSubscription(req_id)
                retry_success += 1
            except Exception as e:
                logging.error(f"Retry failed for {scanner_type} scanner (Req ID: {req_id}): {e}")

        if retry_success > 0:
            logging.info(f"Successfully cancelled {retry_success} subscriptions on retry")
            cancelled_count += retry_success

    # Update state
    self.allScannersFinished = True
    self.active_scanner_subscriptions = False

    # Clear the request IDs dictionary to ensure we don't try to cancel them again
    self.scannerRequestIds.clear()

    # Verify all scanners are marked as finished
    unfinished_scanners = [req_id for req_id, finished in self.scannerFinished.items() if not finished]
    if unfinished_scanners:
        logging.warning(f"WARNING: {len(unfinished_scanners)} scanner subscriptions still marked as unfinished")
        # Force mark them as finished
        for req_id in unfinished_scanners:
            self.scannerFinished[req_id] = True
            logging.info(f"Forced scanner with Req ID {req_id} to finished state")

    logging.info(f"CRITICAL FIX: Successfully cancelled and verified {cancelled_count} scanner subscriptions")

# Get all candidates and sort by priority score
all_candidates = []

if hasattr(self, 'longCandidates') and self.longCandidates:
    for candidate in self.longCandidates:
        candidate['direction'] = 'LONG'
        all_candidates.append(candidate)

if hasattr(self, 'shortCandidates') and self.shortCandidates:
    for candidate in self.shortCandidates:
        candidate['direction'] = 'SHORT'
        all_candidates.append(candidate)

# Sort all candidates by priority score (highest first)
all_candidates.sort(key=lambda x: x.get('priority_score', 0), reverse=True)

# Take top 40 candidates (or fewer if there aren't that many)
top_candidates = all_candidates[:40]

# Process candidates in batches with time limit
batch_size = BATCH_SIZE  # 20 symbols
candidate_batches = [top_candidates[i:i+batch_size] for i in range(0, len(top_candidates), batch_size)]

# Calculate end time for trend data collection
end_time = start_time + TREND_DATA_COLLECTION_DURATION  # 2:30 minutes

for batch_idx, candidate_batch in enumerate(candidate_batches):
    # Check if we've exceeded the max duration
    if datetime.now() >= end_time:
        logging.warning(f"Reached max duration for trend data collection.")
        break

    # Process batch
    for candidate in candidate_batch:
        # Check time limit again for each symbol
        if datetime.now() >= end_time:
            break

        # Get daily trend data
        trend_data = self.get_daily_trend_data(symbol)

    # Small delay between batches
    time.sleep(BATCH_PROCESSING_DELAY)  # 0.7 seconds
```

#### Trend Data Filtering

The scanner filters out candidates with missing trend data:

```python
# Filter out candidates with missing trend data
if hasattr(self, 'use_trend_alignment') and self.use_trend_alignment and hasattr(self, 'daily_trend_data'):
    # Count candidates before filtering
    long_before = len(long_candidates)
    short_before = len(short_candidates)

    logging.info(f"Candidates before trend data filtering: {long_before} long, {short_before} short")

    # Filter out candidates with missing trend data
    filtered_long = []
    filtered_short = []
    excluded_long = []
    excluded_short = []

    for candidate in long_candidates:
        symbol = candidate['symbol']
        if symbol in self.daily_trend_data:
            filtered_long.append(candidate)
        else:
            excluded_long.append(symbol)

    for candidate in short_candidates:
        symbol = candidate['symbol']
        if symbol in self.daily_trend_data:
            filtered_short.append(candidate)
        else:
            excluded_short.append(symbol)

    # Log filtering results
    long_excluded = len(excluded_long)
    short_excluded = len(excluded_short)

    if long_excluded > 0 or short_excluded > 0:
        logging.info(f"Excluded {long_excluded} long and {short_excluded} short candidates due to missing trend data")

        # Update candidates lists
        long_candidates = filtered_long
        short_candidates = filtered_short
```

## Output and Reporting

The scanner generates comprehensive output files that provide detailed information about the scanning process, identified candidates, and opening ranges. These outputs are designed to facilitate analysis and decision-making for ORB trading, and support continuous improvement of filters and strategies.

### Trade Signal Logging

The scanner automatically logs all breakout signals for performance tracking and filter improvement:

```python
def log_breakout(symbol, direction, entry_price, opening_high, opening_low,
                volume, scanners, priority, vix):
    """Log a breakout signal to CSV and JSON files"""
    # Records detailed trade information including:
    # - Symbol, direction, entry price, opening range
    # - Volume data, scanner sources, priority score
    # - VIX level at time of signal
    # - Timestamp and market conditions
```

This comprehensive logging enables:
- Analysis of win rates by scanner type, VIX level, and sector
- Performance tracking of different filter criteria
- Identification of patterns in successful vs failed breakouts
- Continuous improvement of the scanning strategy

### Performance Tracking for Filter Optimization

The scanner includes tools for analyzing filter performance:

1. **Daily Performance Reports**: Automated generation of daily P&L and win rate statistics
2. **Scanner Type Analysis**: Performance breakdown by HOT_BY_VOLUME, TOP_PERC_GAIN, etc.
3. **VIX-Based Performance**: Analysis of how strategies perform under different volatility conditions
4. **Sector Performance**: Tracking of dynamic sector weighting effectiveness

### Markdown Report

A detailed report in Markdown format containing:

- **Market and Timing Context**: Information about when the scan was run relative to market open
- **VIX Context**: Current VIX level and applied strategy adjustments
- **Continuous Scanning Summary**: Overview of scan iterations and tracked symbols
- **Scan History**: Table showing the progression of candidates over time
- **Scanner Summary**: Results from each scanner type
- **Opening Range Data**: High/low ranges and widths for tracked symbols
- **Long Candidates**: Top candidates for long positions with scores and priorities
- **Short Candidates**: Top candidates for short positions with scores and priorities
- **VIX-Based Adjustments**: Details of adjustments made based on VIX level
- **Continuous Scanning Strategy**: Explanation of the scanning approach

### CSV Files

Three CSV files are generated for further analysis:

- **Combined Results CSV**: Contains all unique symbols identified across all scanners with detailed information
  - Columns: symbol, scanner_type, rank, scanner_count, long_score, short_score, priority_score, candidate_type, final_score

- **Long Candidates CSV**: Contains the top long candidates sorted by score
  - Columns: symbol, scanner_type, rank, scanner_count, long_score, short_score, priority_score, candidate_type, final_score

- **Short Candidates CSV**: Contains the top short candidates sorted by score
  - Columns: symbol, scanner_type, rank, scanner_count, long_score, short_score, priority_score, candidate_type, final_score

### Log Files

Detailed logs of the scanning process, including:

- Connection status and VIX retrieval
- Scanner requests and responses
- Opening range updates
- Priority score calculations
- Error handling and warnings
- Performance metrics

## Implementation Notes

- **API Integration**: The scanner uses the Interactive Brokers TWS API for market data and scanning
- **Opening Range Tracking**: Opening range data is tracked for up to 100 symbols to manage API load
- **Dynamic Scoring**: Priority scores are calculated dynamically based on real-time data with each scan
- **Volume Data Skip Mode**: Currently enabled by default (SKIP_VOLUME_DATA=True) due to API rate limiting issues
- **Historical Data Mode**: When testing after market open, the scanner retrieves historical 1-minute bars for accurate opening ranges
- **Batch Processing**: Scanner requests are processed in batches to avoid overwhelming the API
- **API Limitations**: Handles IBKR's 10 simultaneous scanner subscription limit through batch processing
- **Real-Time Data Validation**: Strict validation ensures only real market data is used (no simulation)
- **Phase Manager**: Precise timing control with automatic phase transitions (ORB → FINALIZING → MONITORING)
- **Error Handling**: Robust error handling for connection issues, missing data, and API limitations
- **Performance Optimization**: Efficient data structures and processing to minimize latency

## Recent Improvements (2025)

### Critical Fixes and Enhancements

The scanner has undergone significant improvements to enhance stability, reliability, and performance:

#### 1. Volume Data Skip Mode Implementation
- **Default Mode**: `SKIP_VOLUME_DATA=True` is now the default due to API rate limiting issues
- **Alternative Scoring**: Implements priority scoring without volume ratios
- **Improved Reliability**: Reduces API timeouts and rate limit violations
- **Future Enhancement**: Flag will be removed once volume data collection bugs are fixed

#### 2. Real-Time Data Validation
- **Strict Validation**: Only real market data is used, no simulation or fallback values
- **Symbol Exclusion**: Symbols with missing or invalid data are automatically excluded
- **Price Validation**: Comprehensive checks for NaN, infinity, and non-positive values
- **Data Quality**: Ensures all trading decisions are based on actual market conditions

#### 3. Phase Management Improvements
- **Precise Timing**: Exact 7-minute ORB phase and 2:57 finalizing phase
- **Scanner Control**: Scanners only run during PRE_MARKET and ORB phases
- **Proper Cancellation**: Enhanced scanner subscription cancellation with verification
- **Phase Transitions**: Automatic transitions with proper resource cleanup

#### 4. API Rate Limiting Protection
- **Batch Processing**: Scanner requests processed in batches to stay within limits
- **Enhanced Cancellation**: Verification and retry logic for scanner subscription cancellations
- **Timeout Management**: Improved timeout handling for all API requests
- **Resource Management**: Proper cleanup of scanner subscriptions during phase transitions

#### 5. Historical Data Integration
- **After-Open Mode**: `--after-open` flag for testing outside market hours
- **Optimized Parameters**: Proven reliable historical data request parameters
- **Smart Fallback**: Automatic detection and use of appropriate data sources
- **Time Window Processing**: Extraction of exact opening range periods from historical data

## Best Practices for ORB Trading

### Core Principles (Actionable Advice)

1. **Fix Your ORB Candidate List at Market Open**:
   - The scanner locks in the ORB candidate list at exactly 9:37 AM EST (end of 7-minute opening range)
   - No new candidates are added after this point to maintain focus and consistency
   - Use clear, backtested filters that have proven effective in live trading
   - Document and track the performance of your filter criteria for continuous improvement

2. **Use Volume and Liquidity as Core, Non-Negotiable Criteria**:
   - Minimum 500K average daily volume (always enforced, never optional)
   - Current volume sanity check: Must maintain at least 70-90% of pre-market volume (VIX-adjusted)
   - Bid-ask spread filters to ensure sufficient liquidity
   - Market cap requirements scaled by VIX level for additional stability

3. **Avoid Hard Sector Bans—Use Dynamic Scoring Instead**:
   - Never completely exclude sectors (maintains opportunity while managing risk)
   - Apply dynamic weighting: reduce scoring for volatile sectors rather than elimination
   - Tech sector: -20% to -30% weighting adjustment during high VIX periods
   - Biotech sector: -30% to -40% weighting adjustment during high VIX periods
   - Finance sector: -20% weighting adjustment during extreme VIX periods (>35)
   - All adjustments are scoring-based, not hard exclusions

4. **Keep Your Opening Range List Focused and Manageable**:
   - Maximum 50 candidates in normal conditions (VIX ≤ 20)
   - Scale down to 25-35 candidates during high volatility (VIX > 25)
   - Track up to 100 symbols during ORB phase, then prioritize to final manageable list
   - Focus on quality over quantity—better to have fewer, high-conviction trades

5. **Continuously Review and Adapt Filters Based on Real-World Results**:
   - Track all trades and breakout signals in CSV and JSON logs
   - Monitor win rates by scanner type, VIX level, and sector
   - Adjust volume thresholds, spread limits, and market cap requirements based on performance data
   - Review and update dynamic sector weightings quarterly based on market conditions
   - Use backtesting data to validate filter changes before implementing in live trading

### Timing and Execution

1. **Pre-Market Preparation**:
   - Check VIX level at 9:28 AM EST to determine volatility conditions
   - Review pre-market movers and news for potential candidates
   - Ensure TWS connection is stable and ready

2. **Scanning Process**:
   - Start continuous scanning exactly at market open (9:30 AM EST)
   - Use 15-30 second intervals for optimal balance between freshness and API load
   - Run for the full 7-minute opening range period

3. **Trade Selection**:
   - Focus on candidates with high priority scores (>3.0)
   - Verify candidates have sufficient liquidity (volume > 500K)
   - Check for clean opening ranges with defined support/resistance levels
   - Diversify across sectors to reduce correlation risk

4. **Position Sizing**:
   - Reduce position size by 50% when VIX > 35
   - Use ATR for position sizing to account for volatility
   - Consider reducing size for lower priority candidates

### VIX-Based Strategy Adjustments

| VIX Level | Strategy Adjustment |
|-----------|---------------------|
| VIX > 35  | Reduce position size by 50%, require 4x+ relative volume, minimum liquidity 2M shares, wider buffers |
| VIX > 30  | Reduce position size by 25%, require 3x+ relative volume, minimum liquidity 1M shares |
| VIX > 25  | Standard position size, require 2x+ relative volume, avoid certain sectors |
| VIX 20-25 | Standard position size, require 1.5x+ relative volume, dynamic correlation threshold |
| VIX ≤ 20  | Standard position size, standard volume requirements |

### Trend Alignment Filtering

The scanner uses trend alignment to ensure that trading candidates are aligned with the broader market trend. This is a critical step in the finalizing phase that filters out candidates whose trend direction doesn't match their breakout direction.

#### Trend Determination

Trend is determined by comparing the current price to two key indicators:
- **VWAP** (Volume Weighted Average Price) for the last 20 trading days
- **SMA200** (Simple Moving Average over 200 days)

The trend is classified as:
- **STRONG_UP**: Price is above both VWAP and SMA200
- **WEAK_UP**: Price is above VWAP but below SMA200
- **WEAK_DOWN**: Price is below VWAP but above SMA200
- **STRONG_DOWN**: Price is below both VWAP and SMA200

#### Trend Alignment Rules

For a symbol to pass trend alignment:
- **Long candidates** must have a trend of "UPTREND", "STRONG_UP", or "WEAK_UP"
- **Short candidates** must have a trend of "DOWNTREND", "STRONG_DOWN", or "WEAK_DOWN"

If a symbol fails trend alignment, it is removed from the candidate list and will not proceed to the monitoring phase.

#### Correlation Thresholds

The scanner uses dynamic correlation thresholds based on VIX levels to determine trend alignment:

| VIX Range | Threshold Type | Calculation |
|-----------|----------------|-------------|
| VIX > 30  | Fixed (Strict) | 0.8 |
| 25 < VIX ≤ 30 | Fixed (Moderate) | 0.6 |
| 20 < VIX ≤ 25 | Linear Interpolation | 0.4 + ((VIX - 20)/5) × 0.2 |
| VIX ≤ 20 | Disabled | N/A |

This dynamic approach provides a smoother transition between different volatility regimes, allowing for more precise filtering based on market conditions.

#### Trend Data Collection

During the finalizing phase, the scanner:
1. Collects trend data for top candidates based on priority scores
2. Processes candidates in batches to avoid overwhelming the API
3. Enforces a strict time limit of 2:30 minutes for trend data collection
4. Filters out candidates with missing trend data

If no symbols pass trend alignment despite having opening range data, the scanner will exit with an error message, as proceeding without valid candidates would compromise the trading strategy.

## Troubleshooting

### Common Issues

1. **Trend Alignment Failures**:
   - **Symptom**: "CRITICAL ERROR: No valid symbols passed trend alignment criteria" or "Exiting finalize_opening_ranges. Final counts: longCandidates: 0, shortCandidates: 0, valid_symbols for monitoring: 0"
   - **Cause**: This can happen due to:
     - Trend data key mismatch: The code expects `trend_data.get('trend')` but might be looking for `trend_data.get('trend_direction')`
     - Trend value mismatch: The trend analyzer returns values like "STRONG_UP" but the alignment check might be looking for "UPTREND"
     - Missing trend data: The API failed to return valid trend data for the symbols
   - **Solution**:
     - Check the finalizing log for detailed trend data values
     - Ensure the trend data key names match between the trend analyzer and the scanner
     - Verify that the trend alignment logic accepts all valid trend values (e.g., "STRONG_UP", "WEAK_UP" for long candidates)
     - If the issue persists, consider temporarily disabling trend alignment for testing with `--no-trend-alignment`

2. **Scanner API Errors**:
   - **Symptom**: "API_ERROR: {'reqId': X, 'errorCode': 10337, 'errorMessage': 'Misc options key = X is invalid in ReqScannerSubscription(Y) request'}"
   - **Solution**: Ensure scanner filter parameters are passed correctly as `scannerSubscriptionFilterOptions` (the 4th parameter to `reqScannerSubscription`), not as `scannerSubscriptionOptions` (the 3rd parameter)
   - **Note**: The correct format is `reqScannerSubscription(req_id, subscription, [], filter_options)` where `filter_options` is a list of TagValue objects

2. **Scanner Timeout**:
   - **Symptom**: "Not all scanners completed within X seconds"
   - **Solution**: Increase SCANNER_TIMEOUT value or reduce number of concurrent scanners

3. **VIX Data Not Available**:
   - **Symptom**: "Could not retrieve VIX value"
   - **Solution**: Ensure VIX market data subscription is active in TWS, check contract details

4. **API Connection Issues**:
   - **Symptom**: "Failed to connect to TWS"
   - **Solution**: Verify TWS is running, check API settings in TWS, confirm port and client ID

5. **Scanner Subscription Limit**:
   - **Symptom**: "Only 10 simultaneous API scanner subscriptions are allowed"
   - **Solution**: Reduce number of concurrent scanners or implement sequential scanning

6. **No Opening Range Data**:
   - **Symptom**: "Past 7-minute opening range window, skipping update" or "No valid price data for [symbol] - excluding from opening range tracking"
   - **Solution**: Ensure scanner is run during market open hours (9:30-9:37 AM ET) or use historical data mode with `--after-open` flag
   - **Note**: The scanner now strictly requires real market data - symbols with missing data will be excluded rather than using fallback values

7. **Historical Data Timeout**:
   - **Symptom**: "Historical data request timed out for [symbol]"
   - **Solution**: Increase historical data timeout with `--historical-data-timeout` or check market data permissions
   - **Note**: The pre-screening system will automatically exclude symbols after multiple timeouts

8. **No Historical Data Bars**:
   - **Symptom**: "No historical data bars received for [symbol]"
   - **Solution**: Verify market data subscriptions, check if the specified date was a market holiday, or try a different date

9. **Symbol Pre-Screening Exclusions**:
   - **Symptom**: "Symbol [symbol] excluded: [reason]"
   - **Solution**: This is normal behavior as the pre-screening system filters out problematic symbols. Check the specific reason for exclusion:
     - **Insufficient data**: Symbol has less than 180 days of historical data
     - **Too many timeouts**: Symbol has timed out multiple times
     - **Low volume**: Symbol has insufficient trading volume
     - **Wide spread**: Symbol has a wide bid-ask spread

10. **Invalid Market Open Date/Time**:
    - **Symptom**: "Error parsing market open date/time"
    - **Solution**: Ensure date format is YYYY-MM-DD and time format is HH:MM

11. **Real-Time Data Issues**:
    - **Symptom**: "CRITICAL ERROR: No valid price data for [symbol]" or "Received non-positive price for [symbol]"
    - **Solution**: Verify market data subscriptions for the affected symbols, check TWS connection, and ensure you have the necessary market data permissions
    - **Note**: The scanner now strictly requires real market data with no fallback values. If real-time data is unavailable, use the `--after-open` flag to use historical data instead

12. **Phase Transition Failures**:
    - **Symptom**: "Cannot transition from FINALIZING to MONITORING: No valid data available" or "At least one symbol must have valid data to proceed"
    - **Cause**: The phase manager requires at least one valid symbol to transition from FINALIZING to MONITORING. If all symbols fail trend alignment, there will be no valid symbols, and the transition will fail.
    - **Solution**:
      - Check the finalizing log for trend alignment failures
      - Verify that trend data is being correctly retrieved and processed
      - The system will now exit with a clear error message if no symbols pass trend alignment, rather than getting stuck in the finalizing phase
      - For testing purposes, you can temporarily disable trend alignment with `--no-trend-alignment`

### Performance Optimization

1. **Reduce API Load**:
   - Limit tracked symbols to 100 most promising candidates
   - Use efficient data structures for symbol tracking
   - Implement caching for frequently accessed data

2. **Improve Scan Speed**:
   - Use parallel processing for scanner requests
   - Optimize priority calculation for faster processing
   - Implement early filtering to reduce candidate set size

3. **Enhance Reliability**:
   - Implement reconnection logic for API disconnects
   - Add timeout handling for all API requests
   - Use fallback mechanisms for critical data points

## Volume Sanity Check and Liquidity Floor

### Volume Sanity Check

The scanner implements comprehensive real-time price data collection with no simulation or fallback data allowed. This ensures that all trading decisions are based on actual market data, improving reliability and accuracy.

### Market Data Request Enhancements

The scanner requests multiple tick types for comprehensive market data:

```python
# Request market data with valid generic tick types for stocks as per IBKR API:
# 165 = Misc Stats
# 233 = RTVolume (real-time volume, price, and time)
# 295 = VolumeRate
# 318 = LastRTHTrade
generic_ticks = "165,233,295,318"
self.reqMktData(req_id, contract, generic_ticks, False, False, [])
```

This ensures that the scanner receives all necessary price and volume data for accurate opening range calculation.

### Specialized Opening Range Updates

The scanner uses specialized methods for updating different aspects of the opening range:

1. **High Price Updates**: Uses the `_update_symbol_opening_range_high` method to process High tick types (6)
2. **Low Price Updates**: Uses the `_update_symbol_opening_range_low` method to process Low tick types (7)
3. **Open Price Updates**: Uses the `_update_symbol_opening_range_open` method to process Open tick types (14)
4. **Last Price Updates**: Uses the `_update_symbol_opening_range` method to process Last price tick types (4)

This specialized approach ensures that each price component is handled appropriately, resulting in more accurate opening ranges.

### Real-Time Data Validation

The scanner implements comprehensive validation for real-time market data:

```python
# Validate price is a valid number
try:
    price = float(price)
    if math.isnan(price) or math.isinf(price):
        logging.error(f"Invalid price value received: {price} (NaN or Infinity) for tickType {tick_name}")
        return
except (ValueError, TypeError):
    logging.error(f"Non-numeric price value received: {price} for tickType {tick_name}")
    return

# Validate price is greater than zero
if price <= 0:
    logging.warning(f"Received non-positive {tick_name} price for {symbol}: {price} - ignoring")
    return
```

Symbols with missing or invalid real-time data are excluded from processing rather than using simulated data:

```python
# If no valid price data, exclude from opening range tracking
if current_price <= 0:
    logging.error(f"CRITICAL ERROR: No valid price data for {symbol} - excluding from opening range tracking")

    # Remove this symbol from tracked_symbols to prevent further processing
    if symbol in self.tracked_symbols:
        logging.error(f"Removing {symbol} from tracked symbols due to missing real-time price data")
        del self.tracked_symbols[symbol]
```

### Volume Sanity Check

The scanner implements a volume sanity check to reject candidates where current_volume < threshold * pre_market_volume. This prevents fade-outs where volume dries up after the open, which often leads to failed breakouts.

```python
# Volume sanity check: Reject candidates where current_volume < threshold * pre_market_volume
if opening_volume > 0 and current_volume < opening_volume * self.volume_sanity_threshold:
    self.logger.warning(f"Volume sanity check failed for {symbol}: current_volume ({current_volume}) < {self.volume_sanity_threshold * 100:.0f}% of pre-market volume ({opening_volume})")
    continue
```

The threshold is dynamically adjusted based on VIX level:
- Normal conditions: 70% of pre-market volume
- Moderate volatility (VIX > 25): 80% of pre-market volume
- High volatility (VIX > 35): 90% of pre-market volume

### Liquidity Floor

The scanner always includes a minimum liquidity floor with the `averageDailyVolume` filter:

```python
filter_options.append(TagValue("averageDailyVolume", "500000"))  # 500K shares minimum liquidity floor
```

This ensures that all candidates have sufficient liquidity for trading, reducing the risk of slippage and execution issues.

### False Gap Protection

The scanner implements false gap protection to exclude stocks with gaps caused by dividends or splits:

```python
# Add filter to exclude dividend/split-related gaps
filter_options.append(TagValue("exDividendDate", "false"))
```

This filter ensures that only genuine price gaps driven by market sentiment and news are considered, avoiding false signals from technical gaps caused by corporate actions.
