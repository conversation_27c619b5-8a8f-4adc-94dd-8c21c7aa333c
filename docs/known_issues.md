# Known Issues and Fixes


## Current Issues

### After-Open Mode Using Real-Time Ticks Instead of Historical Bars (Identified 2025-05-20)

**Problem Statement:**
When running in after-open mode, the scanner is using real-time tick data instead of historical bars for calculating opening ranges, which is not the intended behavior.

**Root Causes:**
1. **Logic Issue**: The after-open mode is not correctly prioritizing historical data for opening ranges
2. **Data Source Selection**: Despite being in after-open mode, the system is making real-time market data requests and using the resulting tick data
3. **Historical Data Errors**: There are errors with some historical data requests, which may be causing the system to fall back to real-time data

**Notes:**
Issue with Historical Data Path:

In after_open=True mode, the intended primary method for populating opening ranges is _update_opening_ranges_historical (in scanner.py), which should use reqHistoricalData to fetch bar data.
The fact that real-time data appears to be the source suggests one or more of the following for the historical data path:
Failure to Execute or Prioritize: The logic in run_continuous_scanner for the ORB_PHASE might not be correctly prioritizing or ensuring the successful completion of _update_opening_ranges_historical before other methods are used.
Silent Failure: _update_opening_ranges_historical might be executing but failing to retrieve or process historical bar data correctly. These errors might be logged elsewhere (e.g., orb.log or screen.log from the ORB phase) or might not be prominent.
Data Not Populated Correctly: Even if historical data is fetched, it might not be correctly inserted into or formatted for self.opening_range_data, leaving it empty or incomplete from the historical perspective.
Real-Time Data Overwriting/Supplementing: The system might be designed (or flawed) to call update_opening_ranges (real-time) after an attempt (successful or not) to get historical data. If the real-time calls succeed (which they now do), they could be filling in or overwriting what the historical process did or didn't do.
**Impact:**
- Opening ranges in after-open mode may be less accurate or incomplete since they're based on real-time ticks received after starting the scanner, rather than historical data from the actual market open
- This defeats the purpose of after-open mode, which is designed to simulate as if the scanner was running during market open by using historical data
- May lead to inconsistent trading signals compared to running the scanner during actual market open

**Locations to Review:**
- The `update_opening_ranges` method in opening_range.py where the decision between historical and real-time data is made
- The conditional logic that checks the after_open flag and determines which data source to use
- The error handling for historical data requests that might be causing fallbacks to real-time data

**Proposed Fix:**
- Modify the `update_opening_ranges` method to always use historical data when in after-open mode
- Add explicit checks for the after_open flag before making any real-time market data requests
- Improve error handling for historical data requests to prevent fallbacks to real-time data
- Add more detailed logging to track which data source is being used for each symbol in after-open mode

## Placeholder Pre-screening Logic in `scanner.py` (Added on YYYY-MM-DD)

- **Issue:** The `finalize_opening_ranges` method in `scanner.py` is using placeholder logic for pre-screening symbols.
- **Details:** The line `is_eligible, prescreen_reason = True, "Pre-screen passed (placeholder)"` bypasses actual pre-screening. The call `self.pre_screen_symbol(symbol)` is commented out.
- **Impact:** All symbols currently pass pre-screening, potentially leading to unnecessary processing and API calls for non-viable candidates.
- **Action Required:** Uncomment the line `is_eligible, prescreen_reason = self.pre_screen_symbol(symbol)` (or the appropriate variant based on the return type of `self.pre_screen_symbol`) and remove/comment out the placeholder line in the `finalize_opening_ranges` method in `scanner.py`.

## Unused Market Condition Codes from Tick Types 32 and 33 (Added on 2025-05-19)

**Severity: MINOR**

- **Issue:** The application receives market condition codes via tick types 32 and 33 but doesn't process or utilize this data.
- **Details:** Tick type 32 (Market Data Type) and 33 (Auction Information) provide valuable information about trading status, market conditions, and auction states through letter codes (e.g., "ANJKPTXZU"), but there's no implementation of the `tickString` method in the IBApp class to handle these specific tick types.
- **Impact:** The application is missing opportunities to:
  - Filter out symbols with unfavorable market conditions
  - Detect trading halts, crossed markets, or auction periods
  - Prioritize symbols with the best bid/ask on preferred exchanges
  - Log important market status changes that could affect trading decisions
- **Action Required:** Implement a custom `tickString` method in the IBApp class that specifically handles tick types 32 and 33 and interprets the letter codes for trading decisions.

### High-Level Pseudocode: Filtering Scanner Results Using Tick 32 and 33 for Preferred Exchanges

Below is a high-level pseudocode outline for filtering scanner results using tick types 32 (bid exchange) and 33 (ask exchange) to ensure only symbols with best bid/ask on preferred exchanges are considered for the ORB strategy.

```plaintext
# Define preferred exchanges
PREFERRED_EXCHANGES = ["NYSE", "NASDAQ", "ARCA", "BATS", "EDGX"]

# Step 1: Run scanner and get initial list of candidate symbols
scanner_results = run_symbol_scanner()

# Step 2: For each symbol, request exchange mapping codes
for symbol in scanner_results:
    exchange_map_code = get_exchange_map_code(symbol)  # via tickReqParams

    # Step 3: Use reqSmartComponents to get mapping from letter codes to exchange names
    exchange_mapping = get_exchange_letter_to_name_map(exchange_map_code)  # e.g., {"P": "ARCA", "Q": "NASDAQ", ...}

    # Step 4: Subscribe to market data and receive tick 32 (bidExch) and tick 33 (askExch)
    bid_exch_code = get_tick32_bid_exchange_code(symbol)   # e.g., "P"
    ask_exch_code = get_tick33_ask_exchange_code(symbol)   # e.g., "Q"

    # Step 5: Translate letter codes to exchange names
    bid_exch_name = exchange_mapping[bid_exch_code]
    ask_exch_name = exchange_mapping[ask_exch_code]

    # Step 6: Filter symbol if both bid and ask are on preferred exchanges
    if bid_exch_name in PREFERRED_EXCHANGES and ask_exch_name in PREFERRED_EXCHANGES:
        add_to_final_trade_list(symbol)
    else:
        skip_symbol(symbol)

# Step 7: Proceed with ORB logic on final_trade_list
```

This approach ensures the ORB strategy only acts on symbols with actionable liquidity on the best venues, improving fill quality and execution reliability.

