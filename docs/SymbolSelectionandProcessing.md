# Symbol Selection and Processing in the ORB Scanner

## Overview

This document provides a comprehensive analysis of how symbols are selected, filtered, and processed throughout the different phases of the ORB (Opening Range Breakout) Scanner. The system employs a sophisticated multi-phase approach to identify, evaluate, and monitor potential trading candidates based on market conditions, price movements, volume patterns, and other technical factors.

## Table of Contents

1. [Scanner Types and Parameters](#scanner-types-and-parameters)
2. [PRE_MARKET Phase Selection](#pre_market-phase-selection)
3. [ORB Phase Processing](#orb-phase-processing)
4. [FINALIZING Phase Filtering](#finalizing-phase-filtering)
5. [MONITORING Phase Selection](#monitoring-phase-selection)
6. [Priority Scoring System](#priority-scoring-system)
7. [VIX-Based Parameter Adjustments](#vix-based-parameter-adjustments)
8. [Trend Alignment and Filtering](#trend-alignment-and-filtering)
9. [Data Preservation Between Phases](#data-preservation-between-phases)

## Scanner Types and Parameters

### Scanner Types

The system uses different scanner types based on market conditions and the current phase:

#### PRE_MARKET Phase Scanners
```python
"PRE_MARKET": [
    "TOP_PERC_GAIN",
    "HOT_BY_VOLUME",
    "MOST_ACTIVE"
]
```

#### Normal Market Conditions (VIX ≤ 25)
```python
"LOW_VIX": [
    "HOT_BY_VOLUME",
    "TOP_PERC_GAIN",
    "TOP_TRADE_RATE",
    "MOST_ACTIVE",
    "GAP_SCANNER"
]
```

#### High Volatility Conditions (VIX > 25)
```python
"HIGH_VIX": [
    "HOT_BY_VOLUME",
    "MOST_ACTIVE",
    "TOP_TRADE_RATE",
    "GAP_SCANNER"
]
```

### Scanner Parameters

Scanner parameters are dynamically adjusted based on market volatility (VIX level):

| Parameter | Normal (VIX ≤ 20) | Elevated (VIX > 20) | Moderate-High (VIX > 30) | High (VIX > 35) |
|-----------|-------------------|---------------------|--------------------------|-----------------|
| Base Rows | 50 | 50 | 35 | 20 |
| Min Price | $10.00 | $10.00 | $10.00 | $10.00 |
| Max Price | $500.00 | $500.00 | $500.00 | $500.00 |
| Min Volume | 300,000 | 300,000 | 750,000 | 1,000,000 |
| RVOL Multiplier | 1.5x | 1.5x | 2.0x | 3.0x |
| Gap Threshold | 1.0% | 1.0% | 1.5% | 2.0% |
| Price Change (TOP_PERC_GAIN) | 1.0% | 1.0% | 1.5% | 2.0% |
| Price Change (HOT_BY_VOLUME) | 0.5% | 0.5% | 1.0% | 1.5% |

### Scanner Weights for Scoring

Different scanner types are weighted based on historical performance:

```python
scanner_weights = {
    'HOT_BY_VOLUME': 1.2,  # Best performer
    'TOP_PERC_GAIN': 1.1,  # Strong performer
    'MOST_ACTIVE': 1.0,    # Standard weight
    'TOP_TRADE_RATE': 0.9  # Slightly less valuable
}
```

### Scanner Bias for Direction

Scanners are categorized by their effectiveness for long vs. short candidates:

```python
long_scanners = ['HOT_BY_VOLUME', 'TOP_PERC_GAIN']
short_scanners = ['TOP_TRADE_RATE', 'MOST_ACTIVE']
```

## PRE_MARKET Phase Selection

During the PRE_MARKET phase, the system identifies potential candidates before market open and pre-caches volume data.

### Process Flow

1. **Scanner Execution**:
   - Runs PRE_MARKET scanners: TOP_PERC_GAIN, HOT_BY_VOLUME, MOST_ACTIVE
   - Results are stored in `scannerResults` dictionary

2. **Candidate Selection**:
   - `select_pre_market_candidates()` deduplicates symbols across scanners
   - Ranks symbols by appearance frequency and scanner priority
   - Populates `pre_market_candidates` list (limited to MAX_PRE_MARKET_CANDIDATES = 30)

3. **Volume Data Pre-Caching**:
   - For each candidate, fetches average daily volume via:
     - Contract details API (primary method)
     - Historical data API (fallback method)
   - Stores results in `avg_volumes` dictionary

### Key Filtering Parameters

- Maximum candidates: 30 (MAX_PRE_MARKET_CANDIDATES)
- Minimum price: $10.00
- Maximum price: $500.00
- Minimum volume: Varies by VIX level (300,000 to 1,000,000)

## ORB Phase Processing

During the ORB (Opening Range Building) phase, the system tracks real-time price and volume data for the first 7 minutes after market open.

### Process Flow

1. **Symbol Tracking**:
   - Tracks symbols from `pre_market_candidates` and new scanner results
   - Limited to MAX_TRACKED_SYMBOLS = 25
   - Stores metadata in `tracked_symbols` dictionary

2. **Real-Time Data Collection**:
   - Subscribes to market data for each tracked symbol
   - Updates `real_time_data` dictionary with price and volume

3. **Opening Range Building**:
   - `update_opening_ranges()` continuously updates high, low, open, and volume
   - Stores data in `opening_range_data` dictionary
   - Marks ranges as valid or invalid based on criteria

4. **Range Validation**:
   - At the end of the phase, validates opening ranges based on:
     - Range width percentage (not too narrow or wide)
     - Sufficient volume
     - Price stability
   - Populates `valid_symbols` list with symbols having valid ranges

### Key Filtering Parameters

- Opening range duration: 7 minutes (ORB_PHASE_DURATION)
- Maximum tracked symbols: 25 (MAX_TRACKED_SYMBOLS)
- Range width validation: 0.1% to 10.0% of price

## FINALIZING Phase Filtering

The FINALIZING phase processes the opening ranges, applies trend alignment, and prepares candidates for monitoring.

### Process Flow

1. **Opening Range Finalization**:
   - `finalize_opening_ranges()` processes all opening ranges
   - Applies additional validation criteria
   - Updates `valid_symbols` list

2. **Trend Data Collection**:
   - Fetches daily trend data (VWAP, SMA200, trend direction)
   - Takes approximately 2:30 minutes (TREND_DATA_COLLECTION_DURATION)
   - Stores results in `daily_trend_data` dictionary

3. **Candidate Processing**:
   - `process_scanner_results()` combines and scores all candidates
   - Applies trend alignment if enabled
   - Populates `longCandidates` and `shortCandidates` lists

4. **Candidate Preservation**:
   - `_preserve_candidates_for_monitoring()` creates deep copies of candidates
   - Stores in `_preserved_long_candidates` and `_preserved_short_candidates`
   - Creates backup storage in `_monitoring_candidates` dictionary

### Key Filtering Parameters

- Range width filtering: 0.1% to 10.0% of price
- Trend alignment: Price must align with daily trend (configurable)
- Candidate direction: Based on scanner scores and trend alignment

## MONITORING Phase Selection

The MONITORING phase restores preserved candidates and sets up the breakout detector for continuous monitoring.

### Process Flow

1. **Candidate Restoration**:
   - Restores candidates from preserved lists
   - Falls back to backup storage if needed
   - Updates `longCandidates` and `shortCandidates` lists

2. **Breakout Detector Initialization**:
   - Creates `BreakoutDetector` with appropriate parameters
   - Parameters adjusted based on VIX level
   - Configures with MONITORING phase logger

3. **Symbol Registration**:
   - Adds valid symbols to breakout detector
   - Associates each symbol with its opening range and scanner data
   - Applies additional filtering based on range width and volume

4. **Market Data Subscription**:
   - Subscribes to real-time data for monitored symbols
   - Starts continuous monitoring loop

### Key Filtering Parameters in BreakoutDetector

| Parameter | Normal | High VIX (>25) | Very High VIX (>35) |
|-----------|--------|----------------|---------------------|
| volume_threshold | 1.5x | 2.5x | 4.0x |
| min_range_width_pct | 0.5% | 0.6% | 0.8% |
| max_range_width_pct | 3.0% | 3.5% | 4.0% |
| breakout_buffer_pct | 0.1% | 0.15% | 0.2% |
| volume_sanity_threshold | 0.7 | 0.8 | 0.9 |

## Priority Scoring System

The system uses a sophisticated priority scoring algorithm to rank candidates.

### Standard Scoring Method (with volume data)

When volume data is available, the priority score is calculated as:

```
Priority Score = (Volume Component × 0.4) + (Price Component × 0.3) + (Range Component × 0.2) + (Scanner Component × 0.1)
```

Where:
- **Volume Component** = min(volume_ratio, 10.0) × 0.4
- **Price Component** = min(price/pre_market_high, 1.5) × 0.3
- **Range Component** = min(range_width × 10, 2.0) × 0.2
- **Scanner Component** = scanner_count × 0.1

### Alternative Scoring Method (without volume data)

When SKIP_VOLUME_DATA is enabled:

```
Priority Score = (Volume Component × 0.4) + (Price Change Component × 0.3) + (Gap Component × 0.3) + Gap Scanner Bonus + Scanner Bonus
```

Where:
- **Volume Component** = min(scanner_count × 0.5, 5.0) × 0.4
- **Price Change Component** = min(abs(price_change_pct) × 0.5, 5.0) × 0.3
- **Gap Component** = min(abs(gap_pct) × 0.5, 5.0) × 0.3
- **Gap Scanner Bonus** = 1.0 if in gap scanner else 0.0
- **Scanner Bonus** = log(scanner_count + 1) × 0.5

### Long vs. Short Scoring

For each symbol, both long and short scores are calculated:

```python
# Base score from scanner type and rank
base_score = weight * (50 - rank)

# Apply to appropriate direction based on scanner type
if scanner_type in long_scanners:
    long_score += base_score
if scanner_type in short_scanners:
    short_score += base_score

# Add multi-scanner bonus with quality weighting
scanner_bonus = 0
for result in symbol_results:
    scanner_type = result['scanner_type']
    weight = scanner_weights.get(scanner_type, 1.0)
    scanner_bonus += 5 * weight  # Quality-weighted bonus

# Add scanner bonus to both scores
long_score += scanner_bonus
short_score += scanner_bonus
```

The symbol is classified as LONG or SHORT based on which score is higher.

## VIX-Based Parameter Adjustments

The system dynamically adjusts parameters based on the VIX level to adapt to different market volatility conditions.

### Scanner Parameters

```python
if vix_level is None:
    # Default parameters
elif vix_level > 35:
    # High volatility adjustments
    base_rows = 20
    min_volume = 1000000
elif vix_level > 30:
    # Moderate-high volatility adjustments
    base_rows = 35
    min_volume = 750000
elif vix_level > 20:
    # Elevated volatility adjustments
    # No specific changes to base parameters
else:
    # Normal market conditions
```

### Breakout Detection Parameters

```python
# Default parameters
volume_threshold = 1.5
min_range_width_pct = 0.5
max_range_width_pct = 3.0
breakout_buffer_pct = 0.1
volume_sanity_threshold = 0.7

if vix_level > 35:
    # High volatility adjustments
    volume_threshold = 4.0
    min_range_width_pct = 0.8
    max_range_width_pct = 4.0
    breakout_buffer_pct = 0.2
    volume_sanity_threshold = 0.9
elif vix_level > 25:
    # Moderate volatility adjustments
    volume_threshold = 2.5
    min_range_width_pct = 0.6
    max_range_width_pct = 3.5
    breakout_buffer_pct = 0.15
    volume_sanity_threshold = 0.8
```

## Trend Alignment and Filtering

When trend alignment is enabled, candidates are filtered based on their alignment with the daily trend.

### Trend Data Collection

```python
# For each candidate, fetch daily trend data
daily_trend_data = get_daily_trend_data(symbol)
# Contains VWAP, SMA200, and trend direction
```

### Trend Alignment Logic

```python
# For LONG candidates
if candidate_type == 'LONG' and trend != 'UP':
    # Reject if trend is not UP
    app.trend_alignment_stats['LONG']['rejected'] += 1
    continue

# For SHORT candidates
if candidate_type == 'SHORT' and trend != 'DOWN':
    # Reject if trend is not DOWN
    app.trend_alignment_stats['SHORT']['rejected'] += 1
    continue
```

### Range Width Filtering

```python
# Calculate range width percentage
range_width_pct = ((high - low) / low) * 100

# Check if range width is within acceptable limits
if range_width_pct < min_range_width_pct:
    # Range too narrow
    app.rejected_candidates['range_width_rejected'][symbol] = range_width_pct
    continue

if range_width_pct > max_range_width_pct:
    # Range too wide
    app.rejected_candidates['range_width_rejected'][symbol] = range_width_pct
    continue
```

## Data Preservation Between Phases

The system employs robust mechanisms to preserve data between phases, particularly during the transition from FINALIZING to MONITORING.

### Candidate Preservation

```python
def _preserve_candidates_for_monitoring(self):
    # Create deep copies of candidates
    import copy
    self._preserved_long_candidates = copy.deepcopy(long_candidates)
    self._preserved_short_candidates = copy.deepcopy(short_candidates)
    
    # Create backup storage
    self.__dict__['_monitoring_candidates'] = {
        'long': copy.deepcopy(long_candidates),
        'short': copy.deepcopy(short_candidates),
        'timestamp': datetime.now().isoformat()
    }
```

### Candidate Restoration

```python
# Try to get preserved candidates
long_candidates = self._preserved_long_candidates if has_preserved_long else []
short_candidates = self._preserved_short_candidates if has_preserved_short else []

# If empty, try backup storage
if preserved_long_count + preserved_short_count == 0:
    backup = self.__dict__.get('_monitoring_candidates', {})
    if backup:
        long_candidates = backup.get('long', [])
        short_candidates = backup.get('short', [])
```

## Summary of Symbol Selection Process

1. **PRE_MARKET Phase**:
   - Run PRE_MARKET scanners to identify initial candidates
   - Select up to 30 candidates based on scanner appearance and ranking
   - Pre-cache volume data for selected candidates

2. **ORB Phase**:
   - Track up to 15 symbols for opening range building
   - Collect real-time price and volume data for 7 minutes
   - Build and validate opening ranges based on width and volume criteria

3. **FINALIZING Phase**:
   - Finalize opening ranges with additional validation
   - Collect daily trend data for trend alignment
   - Score and classify candidates as LONG or SHORT
   - Apply trend alignment filtering if enabled
   - Preserve candidates for monitoring phase

4. **MONITORING Phase**:
   - Restore preserved candidates
   - Initialize breakout detector with VIX-adjusted parameters
   - Register valid symbols with opening ranges
   - Subscribe to real-time data for continuous monitoring
   - Start monitoring loop for breakout detection

This multi-phase approach ensures that only the most promising candidates with valid opening ranges and appropriate trend alignment are selected for monitoring, maximizing the effectiveness of the ORB strategy.