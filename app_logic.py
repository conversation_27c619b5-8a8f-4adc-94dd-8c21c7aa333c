#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
App Logic - General application logic for the ORB Scanner.

This module contains general application logic, EWrapper callbacks not specific to scanning or data,
phase management, sanity checks, and general utility methods.
"""

import logging
import time
import traceback
import inspect
import threading
from datetime import datetime, timedelta
import pytz
import os

from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract

# Import our modules from the orbscanner package
from orbscanner.utils.phase.manager import PhaseManager
from orbscanner.utils.phase.logging import log_to_phase

# Base class for IBApp that handles general application logic
class IBAppLogic(EWrapper, EClient):
    def __init__(self):
        """
        Initialize the IBAppLogic instance.
        This method initializes attributes related to general application logic.
        """
        # Initialize attributes that might be accessed before they're defined
        self.phase_manager = None
        self._enable_attribute_tracking = True
        self._attribute_tracking_history = []
        self.log_base_path = None
        self.args = None
        self.opening_range_data = {}
        self.reqId_to_symbol = {}
        self.historical_data_errors = {}
        self.historical_data_delay = 0
        self.sanity_check_req_ids = set()

        # Initialize candidate lists (these should NOT be re-initialized in IBApp.__init__)
        self.longCandidates = []
        self.shortCandidates = []
        self._preserved_long_candidates = []
        self._preserved_short_candidates = []

    def log_to_phase(self, phase_enum_value, message, level=logging.INFO):
        """
        Logs a message to the specific logger associated with the given phase.
        This is a method of IBApp to centralize phase logging calls.
        It wraps the standalone log_to_phase function.
        """
        if not hasattr(self, 'phase_manager') or not self.phase_manager:
            # Fallback if phase_manager is not available
            phase_name_str = phase_enum_value.name if hasattr(phase_enum_value, 'name') else str(phase_enum_value)
            fallback_message = f"PhaseManager not available for IBApp.log_to_phase. Logging to default for phase {phase_name_str}. Msg: {message}"
            if level == logging.CRITICAL: logging.critical(fallback_message)
            elif level == logging.ERROR: logging.error(fallback_message)
            elif level == logging.WARNING: logging.warning(fallback_message)
            # Add other levels as needed, e.g., DEBUG
            # elif level == logging.DEBUG: logging.debug(fallback_message)
            else: logging.info(fallback_message)  # Default to INFO
            return

        # Import the standalone log_to_phase function
        from orbscanner.utils.phase.logging import log_to_phase as standalone_log_to_phase_function

        # Call the imported function with the correct arguments
        # Note: We pass self.phase_manager, not self, as the first argument
        standalone_log_to_phase_function(self.phase_manager, phase_enum_value, message, level)

    def __setattr__(self, name, value):
        """
        Override __setattr__ to prevent infinite recursion when setting vix_value attribute
        and to track candidate-related attributes.
        """
        # Special handling for vix_value to avoid recursion
        if name == "vix_value":
            # CRITICAL FIX: Use direct dictionary assignment to avoid recursion
            self.__dict__[name] = value
            return

        # Track candidate-related attributes if tracking is enabled
        if hasattr(self, '_enable_attribute_tracking') and self._enable_attribute_tracking and name in [
            'longCandidates', 'shortCandidates', '_preserved_long_candidates', '_preserved_short_candidates'
        ]:
            try:
                import traceback
                import inspect

                # Get call stack information
                stack = traceback.extract_stack()
                caller = stack[-2]
                filename = caller.filename.split('/')[-1]
                lineno = caller.lineno

                # Get the count of items being set
                item_count = len(value) if isinstance(value, list) else 0

                logging.critical(f"CDG_ATTRIBUTE_CHANGED: Setting {name} to {item_count} items [file: {filename}, line: {lineno}]")

                # Log the stack trace for deeper analysis
                stack_str = "".join(traceback.format_list(stack[-4:-1]))  # Last 3 frames excluding this one
                logging.critical(f"CDG_STACK: {stack_str.strip()}")

                # Store in history
                if hasattr(self, '_attribute_tracking_history'):
                    self._attribute_tracking_history.append({
                        'attribute': name,
                        'count': item_count,
                        'file': filename,
                        'line': lineno,
                        'timestamp': datetime.now().isoformat()
                    })
            except Exception as e:
                # Don't let tracking errors affect normal operation
                logging.error(f"Error in attribute tracking: {e}")

        # Normal attribute setting for all other attributes
        super().__setattr__(name, value)

    def validate_symbol(self, symbol):
        """
        Ensure symbol is a valid string.

        Args:
            symbol: The symbol to validate (any type)

        Returns:
            str: The validated symbol as a string
        """
        # If already a string, return it
        if isinstance(symbol, str):
            return symbol

        logging.error(f"CRITICAL ERROR: Symbol is not a string: {type(symbol).__name__}")
        logging.error(f"Symbol value: {symbol}")
        logging.error(f"This is a critical bug that must be fixed.")

        try:
            # Try to convert to string if it's a simple type
            if hasattr(symbol, 'symbol'):
                # If it's an object with a symbol attribute, use that
                symbol_str = symbol.symbol
                logging.warning(f"CRITICAL FIX: Converted object to string using symbol attribute: {symbol_str}")
                return symbol_str
            else:
                # Otherwise try direct string conversion
                symbol_str = str(symbol)
                # Check if the conversion resulted in something useful (not just object representation)
                if '<' in symbol_str and '>' in symbol_str and 'object at' in symbol_str:
                    logging.error(f"CRITICAL FIX: String conversion resulted in object representation: {symbol_str}")
                    # Use a default symbol as fallback
                    return "UNKNOWN"
                logging.warning(f"CRITICAL FIX: Converted {type(symbol).__name__} to string: {symbol_str}")
                return symbol_str
        except Exception as e:
            logging.error(f"CRITICAL FIX: Failed to convert symbol to string: {e}")
            logging.error(f"Traceback: {traceback.format_exc()}")
            # Use a default symbol as fallback
            return "UNKNOWN"

    def create_standard_contract(self, symbol):
        """
        Create a standardized contract with only essential parameters.

        Args:
            symbol (str): The stock symbol

        Returns:
            Contract: A standard contract object
        """
        # CRITICAL FIX: Ensure symbol is a string using our validation method
        symbol = self.validate_symbol(symbol)

        # If symbol is invalid, return None
        if symbol == "UNKNOWN" or symbol == "INVALID":
            logging.error(f"Cannot create contract for invalid symbol: {symbol}")
            return None

        # Create contract with validated symbol
        contract = Contract()
        contract.symbol = symbol
        contract.secType = "STK"
        contract.exchange = "SMART"
        contract.currency = "USD"

        # Log the created contract for debugging
        logging.debug(f"Created standard contract: {contract.symbol} {contract.secType} {contract.exchange} {contract.currency}")

        return contract

    def ensure_float(self, value, default=0.0, min_value=0.0):
        """
        Ensure value is a float and >= min_value

        Args:
            value: The value to convert to float
            default: Default value if conversion fails
            min_value: Minimum allowed value

        Returns:
            float: The converted value, or default if conversion fails
        """
        try:
            result = float(value)
            return max(result, min_value)
        except (TypeError, ValueError):
            return default

    def ensure_int(self, value, default=0, min_value=0):
        """
        Ensure value is an int and >= min_value

        Args:
            value: The value to convert to int
            default: Default value if conversion fails
            min_value: Minimum allowed value

        Returns:
            int: The converted value, or default if conversion fails
        """
        try:
            result = int(value)
            return max(result, min_value)
        except (TypeError, ValueError):
            return default

    def cdg_track_candidates(self, tracking_point, line_number):
        """
        Track candidate counts for debugging purposes.
        This is a utility method to help diagnose candidate data flow issues.

        Args:
            tracking_point (str): A string identifier for the tracking point
            line_number (int): Line number in the code where tracking is called
        """
        try:
            # Get candidate counts
            long_count = len(self.longCandidates) if hasattr(self, 'longCandidates') else 0
            short_count = len(self.shortCandidates) if hasattr(self, 'shortCandidates') else 0
            preserved_long_count = len(self._preserved_long_candidates) if hasattr(self, '_preserved_long_candidates') else 0
            preserved_short_count = len(self._preserved_short_candidates) if hasattr(self, '_preserved_short_candidates') else 0

            # Log the counts
            logging.critical(f"CDG_TRACKING [{tracking_point}:{line_number}]: longCandidates={long_count}, shortCandidates={short_count}, _preserved_long_candidates={preserved_long_count}, _preserved_short_candidates={preserved_short_count}")

            # Log some sample data if available
            if long_count > 0:
                sample = self.longCandidates[0]
                sample_type = type(sample).__name__
                sample_str = str(sample)[:100] + "..." if len(str(sample)) > 100 else str(sample)
                logging.critical(f"CDG_TRACKING [{tracking_point}:{line_number}]: longCandidates[0] type={sample_type}, value={sample_str}")

            if short_count > 0:
                sample = self.shortCandidates[0]
                sample_type = type(sample).__name__
                sample_str = str(sample)[:100] + "..." if len(str(sample)) > 100 else str(sample)
                logging.critical(f"CDG_TRACKING [{tracking_point}:{line_number}]: shortCandidates[0] type={sample_type}, value={sample_str}")

        except Exception as e:
            # Don't let tracking errors affect normal operation
            logging.error(f"Error in candidate tracking: {e}")
            logging.error(f"Traceback: {traceback.format_exc()}")

    def _preserve_candidates_for_monitoring(self):
        """
        Internal method to preserve candidates for monitoring phase.
        This method copies the current candidates to preserved attributes.

        Returns:
            tuple: (preserved_long_count, preserved_short_count)
        """
        from orbscanner.utils.phase.manager import PhaseManager

        logging.critical("PRESERVATION: Preserving candidates for monitoring phase")
        log_to_phase(PhaseManager.MONITORING, "PRESERVATION: Preserving candidates for monitoring phase", level=logging.INFO)

        # Initialize preserved candidates attributes if they don't exist
        if not hasattr(self, '_preserved_long_candidates'):
            self._preserved_long_candidates = []
        if not hasattr(self, '_preserved_short_candidates'):
            self._preserved_short_candidates = []

        # Get current candidates
        long_candidates = getattr(self, 'longCandidates', [])
        short_candidates = getattr(self, 'shortCandidates', [])

        # Log current candidate counts
        long_count = len(long_candidates)
        short_count = len(short_candidates)
        logging.critical(f"PRESERVATION: Found {long_count} long and {short_count} short candidates to preserve")
        log_to_phase(PhaseManager.MONITORING, f"PRESERVATION: Found {long_count} long and {short_count} short candidates to preserve", level=logging.INFO)

        # Create deep copies of candidates to avoid reference issues
        import copy
        self._preserved_long_candidates = copy.deepcopy(long_candidates)
        self._preserved_short_candidates = copy.deepcopy(short_candidates)

        # Log preserved candidate counts
        preserved_long_count = len(self._preserved_long_candidates)
        preserved_short_count = len(self._preserved_short_candidates)
        logging.critical(f"PRESERVATION: Preserved {preserved_long_count} long and {preserved_short_count} short candidates")
        log_to_phase(PhaseManager.MONITORING, f"PRESERVATION: Preserved {preserved_long_count} long and {preserved_short_count} short candidates", level=logging.INFO)

        # Create backup storage in case attributes get lost
        self.__dict__['_monitoring_candidates'] = {
            'long': copy.deepcopy(long_candidates),
            'short': copy.deepcopy(short_candidates),
            'timestamp': datetime.now().isoformat()
        }
        logging.critical("PRESERVATION: Created backup storage for candidates")
        log_to_phase(PhaseManager.MONITORING, "PRESERVATION: Created backup storage for candidates", level=logging.INFO)

        # Return preserved counts
        return preserved_long_count, preserved_short_count

    def preserve_candidates_for_monitoring(self):
        """
        Preserve candidates for monitoring phase.
        This method is called when transitioning from FINALIZING to MONITORING phase.

        Returns:
            tuple: (preserved_long_count, preserved_short_count)
        """
        from orbscanner.utils.phase.manager import PhaseManager

        logging.critical("PRESERVATION: Starting candidate preservation for monitoring phase")
        log_to_phase(PhaseManager.MONITORING, "PRESERVATION: Starting candidate preservation for monitoring phase", level=logging.INFO)

        try:
            # Call internal method to preserve candidates
            preserved_long_count, preserved_short_count = self._preserve_candidates_for_monitoring()

            # Log success
            logging.critical(f"PRESERVATION: Successfully preserved {preserved_long_count} long and {preserved_short_count} short candidates")
            log_to_phase(PhaseManager.MONITORING, f"PRESERVATION: Successfully preserved {preserved_long_count} long and {preserved_short_count} short candidates", level=logging.INFO)

            # Return preserved counts
            return preserved_long_count, preserved_short_count
        except Exception as e:
            # Log error
            logging.critical(f"PRESERVATION ERROR: {str(e)}")
            log_to_phase(PhaseManager.MONITORING, f"PRESERVATION ERROR: {str(e)}", level=logging.ERROR)
            import traceback
            logging.critical(f"Traceback: {traceback.format_exc()}")
            log_to_phase(PhaseManager.MONITORING, f"Traceback: {traceback.format_exc()}", level=logging.ERROR)

            # Return 0 counts
            return 0, 0

    def transition_to_monitoring_phase(self):
        """
        Handle transition from FINALIZING to MONITORING phase with proper candidate preservation.
        This method ensures that candidate data is properly preserved and transferred during the transition.
        """
        import inspect
        logging.critical(f"CDG_TRACKING [TRANSITION_TO_MONITORING_PHASE_BEFORE_{__name__}_L{inspect.currentframe().f_lineno}]: Before, longCandidates={len(self.longCandidates) if hasattr(self, 'longCandidates') else 'N/A'}, shortCandidates={len(self.shortCandidates) if hasattr(self, 'shortCandidates') else 'N/A'}")

        # Log and verify scanner subscription status at the beginning of transition
        scanner_status = getattr(self, 'active_scanner_subscriptions', None)
        logging.critical(f"SCANNER STATUS [transition_start]: active_scanner_subscriptions={scanner_status}")

        # CRITICAL FIX: Ensure all scanners are stopped before transition
        from orbscanner.scanners.scanner_factory import stop_scanners
        logging.critical("CRITICAL FIX: Ensuring all scanner subscriptions are cancelled before phase transition")
        stop_scanners(self)

        try:
            from orbscanner.utils.phase.manager import PhaseManager

            logging.critical("TRANSITION: Starting transition to MONITORING phase")
            log_to_phase(PhaseManager.MONITORING, "TRANSITION: Starting transition to MONITORING phase", level=logging.INFO)

            # Verify scanner subscription status after explicit stop
            scanner_status = getattr(self, 'active_scanner_subscriptions', None)
            logging.critical(f"SCANNER STATUS [after_explicit_stop]: active_scanner_subscriptions={scanner_status}")

            # CRITICAL FIX: Explicitly ensure the flag is set to False
            self.active_scanner_subscriptions = False
            logging.critical("CRITICAL FIX: Explicitly set active_scanner_subscriptions to False")

            # CDG TRACKING: Track candidates at the start of transition_to_monitoring_phase
            self.cdg_track_candidates("start_transition_to_monitoring", inspect.currentframe().f_lineno)

            # ENHANCED DIAGNOSTICS: Log call stack to track where transition_to_monitoring_phase is being called from
            import traceback
            call_stack = traceback.format_stack()[:-1]  # exclude current frame
            logging.critical("TRANSITION DIAGNOSTICS: Called from:")
            for line in call_stack[-3:]:  # Show last 3 frames
                logging.critical(line.strip())

            # Step 1: Log the current candidates before preservation
            if hasattr(self, 'longCandidates') and hasattr(self, 'shortCandidates'):
                long_count = len(self.longCandidates) if hasattr(self, 'longCandidates') else 0
                short_count = len(self.shortCandidates) if hasattr(self, 'shortCandidates') else 0
                logging.critical(f"TRANSITION: Found {long_count} long and {short_count} short candidates to preserve")
                log_to_phase(PhaseManager.MONITORING, f"TRANSITION: Found {long_count} long and {short_count} short candidates to preserve", level=logging.INFO)

                # ENHANCED DIAGNOSTICS: Log detailed candidate information for debugging
                if long_count > 0:
                    for i, candidate in enumerate(self.longCandidates[:3]):  # Log first 3 only
                        candidate_type = type(candidate).__name__
                        if isinstance(candidate, dict):
                            symbol = candidate.get('symbol', 'UNKNOWN')
                            logging.critical(f"TRANSITION DIAGNOSTICS: Long candidate {i+1}/{long_count}: {symbol} (type: {candidate_type})")
                        else:
                            logging.critical(f"TRANSITION DIAGNOSTICS: Long candidate {i+1}/{long_count}: {candidate} (type: {candidate_type})")

                if short_count > 0:
                    for i, candidate in enumerate(self.shortCandidates[:3]):  # Log first 3 only
                        candidate_type = type(candidate).__name__
                        if isinstance(candidate, dict):
                            symbol = candidate.get('symbol', 'UNKNOWN')
                            logging.critical(f"TRANSITION DIAGNOSTICS: Short candidate {i+1}/{short_count}: {symbol} (type: {candidate_type})")
                        else:
                            logging.critical(f"TRANSITION DIAGNOSTICS: Short candidate {i+1}/{short_count}: {candidate} (type: {candidate_type})")

            # Step 2: Log some sample candidate data for diagnostic purposes
            if long_count > 0:
                sample = self.longCandidates[0]
                logging.critical(f"TRANSITION: Sample long candidate: {sample}")
                log_to_phase(PhaseManager.MONITORING, f"TRANSITION: Sample long candidate: {sample}", level=logging.INFO)

                # Log the first few candidates
                symbols = [c.get('symbol', c) if isinstance(c, dict) else c for c in self.longCandidates[:3]]
                logging.critical(f"TRANSITION: Long candidate symbols: {symbols}")
                log_to_phase(PhaseManager.MONITORING, f"TRANSITION: Long candidate symbols: {symbols}", level=logging.INFO)

            if short_count > 0:
                sample = self.shortCandidates[0]
                logging.critical(f"TRANSITION: Sample short candidate: {sample}")
                log_to_phase(PhaseManager.MONITORING, f"TRANSITION: Sample short candidate: {sample}", level=logging.INFO)

                # Log the first few candidates
                symbols = [c.get('symbol', c) if isinstance(c, dict) else c for c in self.shortCandidates[:3]]
                logging.critical(f"TRANSITION: Short candidate symbols: {symbols}")
                log_to_phase(PhaseManager.MONITORING, f"TRANSITION: Short candidate symbols: {symbols}", level=logging.INFO)

            # Step 3: Preserve candidates BEFORE phase transition
            logging.critical("TRANSITION: Preserving candidates before phase transition")
            log_to_phase(PhaseManager.MONITORING, "TRANSITION: Preserving candidates before phase transition", level=logging.INFO)

            # CDG TRACKING: Track candidates before preservation in transition
            self.cdg_track_candidates("before_preservation_in_transition", inspect.currentframe().f_lineno)

            preserved_long_count, preserved_short_count = self._preserve_candidates_for_monitoring()

            # Step 4: Log the preserved candidates to verify preservation worked
            logging.critical(f"TRANSITION: After preservation - {preserved_long_count} long and {preserved_short_count} short candidates preserved")
            log_to_phase(PhaseManager.MONITORING, f"TRANSITION: After preservation - {preserved_long_count} long and {preserved_short_count} short candidates preserved", level=logging.INFO)

            # CDG TRACKING: Track candidates after preservation in transition
            self.cdg_track_candidates("after_preservation_in_transition", inspect.currentframe().f_lineno)

            # Step 5: Update phase manager with candidate count
            total_preserved = preserved_long_count + preserved_short_count
            if hasattr(self, 'phase_manager') and self.phase_manager is not None:
                self.phase_manager.update_symbol_validation(PhaseManager.MONITORING, total_preserved, total_preserved)
                logging.critical(f"TRANSITION: Updated phase manager with {total_preserved} valid symbols for MONITORING phase")
                log_to_phase(PhaseManager.MONITORING, f"TRANSITION: Updated phase manager with {total_preserved} valid symbols for MONITORING phase", level=logging.INFO)

            # Step 6: Now perform the phase transition
            if hasattr(self, 'phase_manager') and self.phase_manager is not None:
                logging.critical(f"TRANSITION: Executing phase transition to MONITORING phase with {total_preserved} preserved candidates")
                log_to_phase(PhaseManager.MONITORING, f"TRANSITION: Executing phase transition to MONITORING phase with {total_preserved} preserved candidates", level=logging.INFO)

                # CDG TRACKING: Track candidates before phase transition
                self.cdg_track_candidates("before_phase_transition", inspect.currentframe().f_lineno)

                self.phase_manager.transition_to(PhaseManager.MONITORING, f"Scheduled transition with {total_preserved} preserved candidates")

                logging.critical("TRANSITION: Phase transition to MONITORING phase completed")
                log_to_phase(PhaseManager.MONITORING, "TRANSITION: Phase transition to MONITORING phase completed", level=logging.INFO)

                # CDG TRACKING: Track candidates after phase transition
                self.cdg_track_candidates("after_phase_transition", inspect.currentframe().f_lineno)
            else:
                logging.critical("TRANSITION: Cannot transition to MONITORING phase - phase_manager not available")
                log_to_phase(PhaseManager.MONITORING, "TRANSITION: Cannot transition to MONITORING phase - phase_manager not available", level=logging.ERROR)

            # Step 7: Initialize the monitoring phase
            logging.critical("TRANSITION: Initializing monitoring phase")
            log_to_phase(PhaseManager.MONITORING, "TRANSITION: Initializing monitoring phase", level=logging.INFO)

            # CDG TRACKING: Track candidates before initialize_monitoring_phase
            self.cdg_track_candidates("before_initialize_monitoring", inspect.currentframe().f_lineno)

            self.initialize_monitoring_phase()

            # CDG TRACKING: Track candidates after initialize_monitoring_phase
            self.cdg_track_candidates("after_initialize_monitoring", inspect.currentframe().f_lineno)

            # CDG TRACKING: Final tracking point at the end of transition_to_monitoring_phase
            logging.critical("CDG_TRACKING [transition_complete]: Transition to monitoring phase completed successfully")

            # CRITICAL FIX: Final verification of scanner subscription status
            scanner_status = getattr(self, 'active_scanner_subscriptions', None)
            logging.critical(f"SCANNER STATUS [transition_complete]: active_scanner_subscriptions={scanner_status}")

            # Force set flag to False if it's still not False
            if scanner_status is not False:
                logging.critical(f"CRITICAL FIX: active_scanner_subscriptions flag is still not False at end of transition, forcing to False")
                self.active_scanner_subscriptions = False
                logging.critical(f"SCANNER STATUS [after_force_fix]: active_scanner_subscriptions={self.active_scanner_subscriptions}")

        except Exception as e:
            logging.critical(f"TRANSITION ERROR: {str(e)}")
            log_to_phase(PhaseManager.MONITORING, f"TRANSITION ERROR: {str(e)}", level=logging.ERROR)
            import traceback
            logging.critical(f"Traceback: {traceback.format_exc()}")
            log_to_phase(PhaseManager.MONITORING, f"Traceback: {traceback.format_exc()}", level=logging.ERROR)

            # CRITICAL FIX: Ensure flag is set to False even on error
            self.active_scanner_subscriptions = False
            logging.critical(f"CRITICAL FIX: Forced active_scanner_subscriptions to False after error")

            # CDG TRACKING: Error tracking point at the end of transition_to_monitoring_phase
            logging.critical(f"CDG_TRACKING [transition_error]: Transition to monitoring phase failed with error: {str(e)}")

            # Even on error, try to continue with transition
            if hasattr(self, 'phase_manager') and self.phase_manager is not None:
                # CDG TRACKING: Track candidates before error recovery phase transition
                try:
                    self.cdg_track_candidates("before_error_recovery_transition", inspect.currentframe().f_lineno)
                except Exception as tracking_error:
                    logging.critical(f"CDG_TRACKING ERROR: Failed to track candidates before error recovery: {tracking_error}")

                self.phase_manager.transition_to(PhaseManager.MONITORING, f"Error during transition: {str(e)}")

                # CDG TRACKING: Track candidates after error recovery phase transition
                try:
                    self.cdg_track_candidates("after_error_recovery_transition", inspect.currentframe().f_lineno)
                except Exception as tracking_error:
                    logging.critical(f"CDG_TRACKING ERROR: Failed to track candidates after error recovery: {tracking_error}")

            # Try to initialize monitoring even after error
            try:
                # CDG TRACKING: Track candidates before error recovery initialize_monitoring_phase
                self.cdg_track_candidates("before_error_recovery_initialize", inspect.currentframe().f_lineno)

                self.initialize_monitoring_phase()

                # CDG TRACKING: Track candidates after error recovery initialize_monitoring_phase
                self.cdg_track_candidates("after_error_recovery_initialize", inspect.currentframe().f_lineno)

                # CDG TRACKING: Final tracking point after error recovery
                logging.critical("CDG_TRACKING [error_recovery_complete]: Error recovery completed successfully")
            except Exception as e2:
                logging.critical(f"TRANSITION ERROR: Failed to initialize monitoring phase after error: {str(e2)}")
                log_to_phase(PhaseManager.MONITORING, f"TRANSITION ERROR: Failed to initialize monitoring phase after error: {str(e2)}", level=logging.ERROR)

                # CDG TRACKING: Final tracking point after error recovery failure
                logging.critical(f"CDG_TRACKING [error_recovery_failed]: Error recovery failed with error: {str(e2)}")
        logging.critical(f"CDG_TRACKING [TRANSITION_TO_MONITORING_PHASE_AFTER_{__name__}_L{inspect.currentframe().f_lineno}]: After, longCandidates={len(self.longCandidates) if hasattr(self, 'longCandidates') else 'N/A'}, shortCandidates={len(self.shortCandidates) if hasattr(self, 'shortCandidates') else 'N/A'}")

    def initialize_monitoring_phase(self):
        """
        Initialize monitoring phase using preserved candidates.
        This method is called when transitioning from FINALIZING to MONITORING phase.
        It restores preserved candidates and sets up the breakout detector.

        Returns:
            bool: True if initialization was successful, False otherwise
        """
        import inspect
        logging.critical(f"CDG_TRACKING [INITIALIZE_MONITORING_PHASE_BEFORE_{__name__}_L{inspect.currentframe().f_lineno}]: Before, longCandidates={len(self.longCandidates) if hasattr(self, 'longCandidates') else 'N/A'}, shortCandidates={len(self.shortCandidates) if hasattr(self, 'shortCandidates') else 'N/A'}")

        from orbscanner.utils.phase.manager import PhaseManager

        logging.critical("MONITORING: Initializing monitoring phase")
        log_to_phase(PhaseManager.MONITORING, "MONITORING: Initializing monitoring phase", level=logging.INFO)

        # CDG TRACKING: Track candidates at the start of initialize_monitoring_phase
        self.cdg_track_candidates("start_initialize_monitoring", inspect.currentframe().f_lineno)

        try:
            # Get log path with fallback
            log_base_path = None
            if hasattr(self, 'log_base_path') and self.log_base_path:
                log_base_path = self.log_base_path
            elif hasattr(self, 'args') and hasattr(self.args, 'log_base'):
                log_base_path = self.args.log_base

            if not log_base_path:
                # Create a default log path in logs/live directory
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                log_dir = "logs/live"
                os.makedirs(log_dir, exist_ok=True)
                log_base_path = f"{log_dir}/scan_orb_{timestamp}"
                logging.warning(f"No log_base_path found, using default: {log_base_path}")

            # Initialize TradeLogger
            from orbscanner.core.trade_logger import TradeLogger
            self.trade_logger = TradeLogger(log_base_path)
            logging.critical(f"Initialized TradeLogger with log_base_path: {log_base_path}")
            log_to_phase(PhaseManager.MONITORING, f"Initialized TradeLogger with log_base_path: {log_base_path}", level=logging.INFO)

            # Get VIX level for breakout detection
            vix_value = getattr(self, 'vix_value', None)
            if vix_value:
                logging.critical(f"Using VIX level: {vix_value:.2f} for breakout detection")
                log_to_phase(PhaseManager.MONITORING, f"Using VIX level: {vix_value:.2f} for breakout detection", level=logging.INFO)
            else:
                logging.warning("No VIX level available, using default")
                log_to_phase(PhaseManager.MONITORING, "No VIX level available, using default", level=logging.WARNING)
                vix_value = 15.0  # Default value

            # Try to get preserved candidates
            # First check normal attributes
            has_preserved_long = hasattr(self, '_preserved_long_candidates')
            has_preserved_short = hasattr(self, '_preserved_short_candidates')

            # CDG TRACKING: Track candidate attributes before restoration
            logging.critical(f"CDG_TRACKING [candidate_attributes]: has_preserved_long={has_preserved_long}, has_preserved_short={has_preserved_short}")

            long_candidates = self._preserved_long_candidates if has_preserved_long else []
            short_candidates = self._preserved_short_candidates if has_preserved_short else []

            preserved_long_count = len(long_candidates)
            preserved_short_count = len(short_candidates)

            # CDG TRACKING: Track candidates after getting preserved candidates
            self.cdg_track_candidates("after_getting_preserved_candidates", inspect.currentframe().f_lineno)

            # If empty, try backup storage
            if preserved_long_count + preserved_short_count == 0:
                backup = self.__dict__.get('_monitoring_candidates', {})
                if backup:
                    # CDG TRACKING: Log backup storage details
                    logging.critical(f"CDG_TRACKING [backup_storage]: Found backup storage with keys: {list(backup.keys())}")

                    long_candidates = backup.get('long', [])
                    short_candidates = backup.get('short', [])
                    preserved_long_count = len(long_candidates)
                    preserved_short_count = len(short_candidates)
                    logging.critical(f"CANDIDATE RESTORATION: Restored from backup storage: {preserved_long_count} long, {preserved_short_count} short")
                    log_to_phase(PhaseManager.MONITORING, f"CANDIDATE RESTORATION: Restored from backup storage: {preserved_long_count} long, {preserved_short_count} short", level=logging.INFO)

                    # CDG TRACKING: Track candidates after backup restoration
                    self.cdg_track_candidates("after_backup_restoration", inspect.currentframe().f_lineno)
                else:
                    logging.critical("CDG_TRACKING [backup_storage]: No backup storage found or it's empty")

            # Log restoration results
            logging.critical(f"CANDIDATE RESTORATION: Found {preserved_long_count} preserved long candidates and {preserved_short_count} preserved short candidates")
            log_to_phase(PhaseManager.MONITORING, f"CANDIDATE RESTORATION: Found {preserved_long_count} preserved long candidates and {preserved_short_count} preserved short candidates", level=logging.INFO)

            # Restore candidates to main attributes
            self.longCandidates = long_candidates
            self.shortCandidates = short_candidates

            # CDG TRACKING: Track candidates after restoration
            self.cdg_track_candidates("after_candidate_restoration", inspect.currentframe().f_lineno)

            # Get the valid symbols with opening ranges for monitoring
            valid_symbols = []
            if hasattr(self, 'valid_symbols'):
                valid_symbols = self.valid_symbols
                logging.critical(f"MONITORING: Found {len(valid_symbols)} valid symbols with opening ranges from valid_symbols attribute")
                log_to_phase(PhaseManager.MONITORING, f"MONITORING: Found {len(valid_symbols)} valid symbols with opening ranges from valid_symbols attribute", level=logging.INFO)

            # If no valid symbols in the attribute, try to find them from opening_range_data
            if not valid_symbols and hasattr(self, 'opening_range_data'):
                for symbol, data in self.opening_range_data.items():
                    if data.get('valid', False):
                        valid_symbols.append(symbol)
                logging.critical(f"MONITORING: Found {len(valid_symbols)} valid symbols with opening ranges from opening_range_data")
                log_to_phase(PhaseManager.MONITORING, f"MONITORING: Found {len(valid_symbols)} valid symbols with opening ranges from opening_range_data", level=logging.INFO)
                # Save the valid symbols for future reference
                self.valid_symbols = valid_symbols

            # Initialize breakout detector
            from orbscanner.core.breakout_detector import BreakoutDetector
            from orbscanner.utils.phase.logging import PHASE_LOGGERS

            # Get the MONITORING phase logger to pass to the BreakoutDetector
            monitoring_logger = PHASE_LOGGERS.get(PhaseManager.MONITORING)
            if monitoring_logger:
                logging.critical("Using MONITORING phase logger for BreakoutDetector")
                log_to_phase(PhaseManager.MONITORING, "Using MONITORING phase logger for BreakoutDetector", level=logging.INFO)
                self.breakout_detector = BreakoutDetector(
                    self.trade_logger,
                    vix_level=vix_value,
                    logger=monitoring_logger
                )
            else:
                logging.critical("MONITORING phase logger not found, using default logger for BreakoutDetector")
                log_to_phase(PhaseManager.MONITORING, "MONITORING phase logger not found, using default logger for BreakoutDetector", level=logging.WARNING)
                self.breakout_detector = BreakoutDetector(
                    self.trade_logger,
                    vix_level=vix_value
                )
            logging.critical("Initialized BreakoutDetector")
            log_to_phase(PhaseManager.MONITORING, "Initialized BreakoutDetector", level=logging.INFO)

            # Add valid symbols to the breakout detector
            symbols_added = 0
            if valid_symbols:
                logging.critical(f"MONITORING: Adding {len(valid_symbols)} valid symbols to breakout detector")
                log_to_phase(PhaseManager.MONITORING, f"MONITORING: Adding {len(valid_symbols)} valid symbols to breakout detector", level=logging.INFO)

                for symbol in valid_symbols:
                    # Get opening range data
                    if symbol in self.opening_range_data:
                        opening_range = self.opening_range_data[symbol]

                        # Find scanner data for this symbol (could be in long or short candidates)
                        scanner_data = None
                        for candidate in self.longCandidates:
                            if isinstance(candidate, dict) and candidate.get('symbol') == symbol:
                                scanner_data = candidate
                                scanner_data['candidate_type'] = 'LONG'
                                break

                        if scanner_data is None:
                            for candidate in self.shortCandidates:
                                if isinstance(candidate, dict) and candidate.get('symbol') == symbol:
                                    scanner_data = candidate
                                    scanner_data['candidate_type'] = 'SHORT'
                                    break

                        # If no scanner data found, create minimal data
                        if scanner_data is None:
                            scanner_data = {
                                'symbol': symbol,
                                'candidate_type': 'UNKNOWN',
                                'scanners': 'UNKNOWN',
                                'priority_score': 0
                            }

                        # Add to breakout detector
                        if self.breakout_detector.add_symbol_to_monitor(symbol, opening_range, scanner_data):
                            symbols_added += 1

            logging.critical(f"MONITORING: Added {symbols_added} symbols to breakout detector for monitoring")
            log_to_phase(PhaseManager.MONITORING, f"MONITORING: Added {symbols_added} symbols to breakout detector for monitoring", level=logging.INFO)

            # Subscribe to real-time market data for monitored symbols
            if symbols_added > 0:
                logging.critical(f"MONITORING: Subscribing to real-time market data for {symbols_added} symbols")
                log_to_phase(PhaseManager.MONITORING, f"MONITORING: Subscribing to real-time market data for {symbols_added} symbols", level=logging.INFO)

                # Get the list of symbols to monitor from the breakout detector
                monitored_symbols = self.breakout_detector.get_monitored_symbols()

                # Subscribe to market data for these symbols
                if hasattr(self, 'subscribe_to_market_data'):
                    subscriptions = self.subscribe_to_market_data(monitored_symbols)
                    logging.critical(f"MONITORING: Successfully subscribed to market data for {subscriptions} symbols")
                    log_to_phase(PhaseManager.MONITORING, f"MONITORING: Successfully subscribed to market data for {subscriptions} symbols", level=logging.INFO)
                else:
                    logging.critical("MONITORING: subscribe_to_market_data method not found")
                    log_to_phase(PhaseManager.MONITORING, "MONITORING: subscribe_to_market_data method not found", level=logging.ERROR)

            # Start continuous monitoring loop
            try:
                from monitoring_loop import MonitoringLoop
                self.monitoring_loop = MonitoringLoop(self)
                if self.monitoring_loop.start():
                    logging.critical("MONITORING: Started continuous monitoring loop")
                    log_to_phase(PhaseManager.MONITORING, "MONITORING: Started continuous monitoring loop", level=logging.INFO)
                else:
                    logging.warning("MONITORING: Continuous monitoring loop was not started")
                    log_to_phase(PhaseManager.MONITORING, "MONITORING: Continuous monitoring loop was not started", level=logging.WARNING)
            except Exception as e:
                logging.error(f"MONITORING: Failed to start monitoring loop: {str(e)}")
                log_to_phase(PhaseManager.MONITORING, f"MONITORING: Failed to start monitoring loop: {str(e)}", level=logging.ERROR)

            # Log success
            logging.critical("MONITORING: Monitoring phase initialized successfully")
            log_to_phase(PhaseManager.MONITORING, "MONITORING: Monitoring phase initialized successfully", level=logging.INFO)

            # CDG TRACKING: Track candidates at the end of initialize_monitoring_phase
            self.cdg_track_candidates("end_initialize_monitoring", inspect.currentframe().f_lineno)

            logging.critical(f"CDG_TRACKING [INITIALIZE_MONITORING_PHASE_AFTER_{__name__}_L{inspect.currentframe().f_lineno}]: After, longCandidates={len(self.longCandidates) if hasattr(self, 'longCandidates') else 'N/A'}, shortCandidates={len(self.shortCandidates) if hasattr(self, 'shortCandidates') else 'N/A'}")

            return True
        except Exception as e:
            logging.critical(f"MONITORING ERROR: Failed to initialize monitoring phase: {str(e)}")
            log_to_phase(PhaseManager.MONITORING, f"MONITORING ERROR: Failed to initialize monitoring phase: {str(e)}", level=logging.ERROR)
            import traceback
            logging.critical(f"Traceback: {traceback.format_exc()}")
            log_to_phase(PhaseManager.MONITORING, f"Traceback: {traceback.format_exc()}", level=logging.ERROR)

            # CDG TRACKING: Track error at the end of initialize_monitoring_phase
            logging.critical(f"CDG_TRACKING [initialize_monitoring_error]: {str(e)}")

            logging.critical(f"CDG_TRACKING [INITIALIZE_MONITORING_PHASE_AFTER_ERROR_{__name__}_L{inspect.currentframe().f_lineno}]: After error, longCandidates={len(self.longCandidates) if hasattr(self, 'longCandidates') else 'N/A'}, shortCandidates={len(self.shortCandidates) if hasattr(self, 'shortCandidates') else 'N/A'}")

            return False

    def error(self, reqId, errorCode, errorString, advancedOrderRejectJson=""):
        """
        Handle errors from the IB API.

        Args:
            reqId (int): Request ID that generated the error
            errorCode (int): Error code
            errorString (str): Error message
            advancedOrderRejectJson (str): Additional error information for orders
        """
        # Log all errors at ERROR level
        logging.error(f"IB API Error {errorCode}: {errorString} (reqId: {reqId})")

        # Additional logging for advanced order rejection
        if advancedOrderRejectJson:
            logging.error(f"Advanced Order Reject: {advancedOrderRejectJson}")

        # Classify error by type
        if errorCode in [1100, 1101, 1102, 1300]:
            # Connection-related errors
            logging.critical(f"CONNECTION ERROR: {errorCode} - {errorString}")
            if errorCode == 1100:
                logging.critical("CRITICAL: Connection to IB server lost")
            elif errorCode == 1101:
                logging.critical("CRITICAL: Connection to IB server restored - data lost")
            elif errorCode == 1102:
                logging.critical("CRITICAL: Connection to IB server restored - data maintained")
            elif errorCode == 1300:
                logging.critical("CRITICAL: Socket connection error")

        elif errorCode in [200, 201, 202, 203, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311]:
            # Order-related errors
            logging.error(f"ORDER ERROR: {errorCode} - {errorString}")

        elif errorCode in [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110]:
            # Authentication and connectivity errors
            logging.critical(f"AUTHENTICATION ERROR: {errorCode} - {errorString}")

        elif errorCode in [10090, 10091, 10092, 10093, 10094, 10095, 10096, 10097]:
            # Rate limiting and pacing errors
            logging.warning(f"RATE LIMIT ERROR: {errorCode} - {errorString}")
            # Track rate limit errors in the app instance
            if hasattr(self, 'rate_limit_errors'):
                self.rate_limit_errors.append({
                    'time': datetime.now().isoformat(),
                    'code': errorCode,
                    'message': errorString,
                    'reqId': reqId
                })

        elif errorCode in [321, 322, 323, 324, 325, 326, 327, 328, 329, 330]:
            # Market data errors
            logging.error(f"MARKET DATA ERROR: {errorCode} - {errorString}")
            # Track market data errors in the app instance
            if hasattr(self, 'market_data_errors'):
                self.market_data_errors[reqId] = {
                    'time': datetime.now().isoformat(),
                    'code': errorCode,
                    'message': errorString
                }

        elif errorCode in [162, 200, 354, 2104, 2106, 2107, 2108, 2109, 2110]:
            # Historical data errors
            logging.error(f"HISTORICAL DATA ERROR: {errorCode} - {errorString}")
            # Track historical data errors in the app instance
            if hasattr(self, 'historical_data_errors') and reqId in self.reqId_to_symbol:
                symbol = self.reqId_to_symbol.get(reqId, f"Unknown-{reqId}")
                if symbol not in self.historical_data_errors:
                    self.historical_data_errors[symbol] = []
                self.historical_data_errors[symbol].append({
                    'time': datetime.now().isoformat(),
                    'code': errorCode,
                    'message': errorString,
                    'reqId': reqId
                })

        elif errorCode in [165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175]:
            # Scanner errors
            logging.error(f"SCANNER ERROR: {errorCode} - {errorString}")
            # Track scanner errors in the app instance
            if hasattr(self, 'scanner_errors'):
                self.scanner_errors.append({
                    'time': datetime.now().isoformat(),
                    'code': errorCode,
                    'message': errorString,
                    'reqId': reqId
                })

        # Handle specific error codes
        if errorCode == 200:
            # No security definition found
            if hasattr(self, 'reqId_to_symbol') and reqId in self.reqId_to_symbol:
                symbol = self.reqId_to_symbol.get(reqId)
                logging.error(f"No security definition found for symbol: {symbol}")
                # Add to invalid symbols list
                if hasattr(self, 'invalid_symbols'):
                    self.invalid_symbols.add(symbol)

        elif errorCode == 162:
            # Historical data request pacing violation
            logging.warning("Historical data request pacing violation. Implementing delay.")
            # Add delay for future requests
            if hasattr(self, 'historical_data_delay'):
                self.historical_data_delay += 1.0  # Add 1 second delay

        elif errorCode == 10090:
            # Pacing violation
            logging.warning("Pacing violation detected. Implementing delay.")
            # Add delay for future requests
            time.sleep(2.0)  # Sleep for 2 seconds to help with pacing

        # Log to phase-specific logger if available
        if hasattr(self, 'phase_manager') and self.phase_manager:
            current_phase = self.phase_manager.current_phase
            if current_phase:
                self.log_to_phase(current_phase, f"IB API Error {errorCode}: {errorString} (reqId: {reqId})", level=logging.ERROR)

        # For sanity check requests, record the error
        if hasattr(self, 'sanity_check_req_ids') and reqId in self.sanity_check_req_ids:
            if hasattr(self, 'sanity_check_errors'):
                self.sanity_check_errors[reqId] = {
                    'code': errorCode,
                    'message': errorString,
                    'time': datetime.now().isoformat()
                }
            logging.error(f"Sanity check request {reqId} failed with error {errorCode}: {errorString}")
